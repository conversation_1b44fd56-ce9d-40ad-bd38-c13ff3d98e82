import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, <PERSON>bul<PERSON>, ArrowR<PERSON>, SkipForward } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface ContextualTipProps {
  tip: {
    id: string;
    title: string;
    content: string;
    icon?: React.ReactNode;
    position?: 'top' | 'bottom' | 'left' | 'right';
    targetSelector?: string;
  } | null;
  isVisible: boolean;
  onDismiss: () => void;
  onNext?: () => void;
  onSkip?: () => void;
}

export const ContextualTip: React.FC<ContextualTipProps> = ({
  tip,
  isVisible,
  onDismiss,
  onNext,
  onSkip
}) => {
  if (!isVisible || !tip) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        >
          <Card className="glass-card max-w-md mx-4">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  <motion.div
                    className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center"
                    animate={{ 
                      scale: [1, 1.05, 1],
                      rotate: [0, 2, -2, 0] 
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut" 
                    }}
                  >
                    {tip.icon || <Lightbulb className="w-5 h-5 text-white" />}
                  </motion.div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">
                        {tip.title}
                      </h3>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {tip.content}
                      </p>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onDismiss}
                      className="flex-shrink-0 h-8 w-8 p-0 hover:bg-muted rounded-full"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center justify-between gap-3 mt-4">
                    {onSkip && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onSkip}
                        className="text-muted-foreground hover:text-foreground"
                      >
                        <SkipForward className="w-4 h-4 mr-2" />
                        Skip tips
                      </Button>
                    )}
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onDismiss}
                      >
                        Got it
                      </Button>
                      
                      {onNext && (
                        <Button
                          size="sm"
                          onClick={onNext}
                          className="bg-gradient-primary text-white hover:opacity-90"
                        >
                          Next tip
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

// Mini tip component for inline usage
interface MiniTipProps {
  text: string;
  className?: string;
}

export const MiniTip: React.FC<MiniTipProps> = ({ text, className }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={cn(
        "inline-flex items-center gap-2 bg-primary/10 text-primary text-xs font-medium px-2 py-1 rounded-full",
        className
      )}
    >
      <Lightbulb className="w-3 h-3" />
      {text}
    </motion.div>
  );
};