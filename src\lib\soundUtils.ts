/**
 * Sound utility functions for notifications and alerts
 */

export type SoundType = 'notification' | 'reminder' | 'alert' | 'success' | 'error';

class SoundManager {
  private audioContext: AudioContext | null = null;
  private soundEnabled = true;

  constructor() {
    // Initialize audio context on first user interaction
    this.initializeAudioContext();
  }

  private initializeAudioContext() {
    try {
      // Create audio context when needed
      if (!this.audioContext && typeof window !== 'undefined') {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
    } catch (error) {
      console.warn('Audio context not supported:', error);
    }
  }

  private async ensureAudioContext() {
    if (!this.audioContext) {
      this.initializeAudioContext();
    }

    // Resume audio context if suspended (required by browser policies)
    if (this.audioContext && this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume();
      } catch (error) {
        console.warn('Could not resume audio context:', error);
      }
    }
  }

  /**
   * Generate a simple beep sound using Web Audio API
   */
  private async generateBeep(frequency: number, duration: number, volume: number = 0.1) {
    if (!this.soundEnabled) return;

    try {
      await this.ensureAudioContext();
      if (!this.audioContext) return;

      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      oscillator.frequency.value = frequency;
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

      oscillator.start(this.audioContext.currentTime);
      oscillator.stop(this.audioContext.currentTime + duration);
    } catch (error) {
      console.warn('Error playing beep sound:', error);
    }
  }

  /**
   * Play notification sound
   */
  async playNotification() {
    // Two-tone notification sound
    await this.generateBeep(800, 0.1, 0.1);
    setTimeout(() => this.generateBeep(600, 0.1, 0.1), 100);
  }

  /**
   * Play reminder sound
   */
  async playReminder() {
    // Gentle reminder sound
    await this.generateBeep(440, 0.2, 0.08);
    setTimeout(() => this.generateBeep(554, 0.2, 0.08), 200);
    setTimeout(() => this.generateBeep(659, 0.3, 0.08), 400);
  }

  /**
   * Play alert sound
   */
  async playAlert() {
    // Urgent alert sound
    for (let i = 0; i < 3; i++) {
      setTimeout(() => this.generateBeep(1000, 0.15, 0.12), i * 200);
    }
  }

  /**
   * Play success sound
   */
  async playSuccess() {
    // Pleasant success sound
    await this.generateBeep(523, 0.1, 0.1); // C
    setTimeout(() => this.generateBeep(659, 0.1, 0.1), 100); // E
    setTimeout(() => this.generateBeep(784, 0.2, 0.1), 200); // G
  }

  /**
   * Play error sound
   */
  async playError() {
    // Error sound
    await this.generateBeep(200, 0.3, 0.1);
  }

  /**
   * Play sound by type
   */
  async playSound(type: SoundType) {
    if (!this.soundEnabled) return;

    switch (type) {
      case 'notification':
        await this.playNotification();
        break;
      case 'reminder':
        await this.playReminder();
        break;
      case 'alert':
        await this.playAlert();
        break;
      case 'success':
        await this.playSuccess();
        break;
      case 'error':
        await this.playError();
        break;
    }
  }

  /**
   * Enable or disable sounds
   */
  setSoundEnabled(enabled: boolean) {
    this.soundEnabled = enabled;
  }

  /**
   * Get current sound enabled state
   */
  isSoundEnabled(): boolean {
    return this.soundEnabled;
  }

  /**
   * Test sound functionality
   */
  async testSound(type: SoundType = 'notification') {
    const wasEnabled = this.soundEnabled;
    this.soundEnabled = true; // Temporarily enable for testing
    await this.playSound(type);
    this.soundEnabled = wasEnabled; // Restore original state
  }
}

// Create singleton instance
export const soundManager = new SoundManager();

/**
 * Utility functions for easy access
 */
export const playNotificationSound = () => soundManager.playSound('notification');
export const playReminderSound = () => soundManager.playSound('reminder');
export const playAlertSound = () => soundManager.playSound('alert');
export const playSuccessSound = () => soundManager.playSound('success');
export const playErrorSound = () => soundManager.playSound('error');

/**
 * Initialize sound settings from localStorage
 */
export const initializeSoundSettings = () => {
  try {
    const savedSettings = localStorage.getItem('app-settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      soundManager.setSoundEnabled(settings.soundEnabled !== false);
    }
  } catch (error) {
    console.warn('Error loading sound settings:', error);
  }
};

/**
 * Update sound settings
 */
export const updateSoundSettings = (enabled: boolean) => {
  soundManager.setSoundEnabled(enabled);
};
