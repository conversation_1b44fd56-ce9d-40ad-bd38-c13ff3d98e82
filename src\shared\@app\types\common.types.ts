// Common utility types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SortParams {
  field: string;
  direction: 'asc' | 'desc';
}

export interface FilterParams {
  [key: string]: any;
}

export interface SearchParams {
  query?: string;
  filters?: FilterParams;
  sort?: SortParams;
  pagination?: PaginationParams;
}

export interface FileUploadResult {
  id: string;
  url: string;
  path: string;
}

export interface DatabaseRow {
  id: string;
  created_at: string;
  updated_at?: string;
}

export type CreateInput<T extends DatabaseRow> = Omit<T, 'id' | 'created_at' | 'updated_at'>;
export type UpdateInput<T extends DatabaseRow> = Partial<Omit<T, 'id' | 'created_at'>>;