// Unified task store with cross-platform compatibility  
import { Task, TasksByDate, TaskFilter, TaskSort, CreateTaskInput, UpdateTaskInput } from '@/shared/@app/types';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface TaskStoreState {
  tasks: Task[];
  tasksByDate: TasksByDate;
  selectedTask: Task | null;
  filters: TaskFilter;
  sort: TaskSort;
  searchQuery: string;
  pendingChanges: number;
  lastSyncTimestamp: number;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
  isOnline: boolean;
}

interface TaskStoreActions {
  setTasks: (tasks: Task[]) => void;
  addTask: (task: Task) => void;
  updateTask: (taskId: string, updates: UpdateTaskInput) => void;
  deleteTask: (taskId: string) => void;
  createTaskOptimistic: (task: CreateTaskInput) => string;
  updateTaskOptimistic: (taskId: string, updates: UpdateTaskInput) => void;
  deleteTaskOptimistic: (taskId: string) => void;
  setSelectedTask: (task: Task | null) => void;
  selectTaskById: (taskId: string) => void;
  setFilters: (filters: Partial<TaskFilter>) => void;
  setSort: (sort: TaskSort) => void;
  setSearchQuery: (query: string) => void;
  resetFilters: () => void;
  getFilteredTasks: () => Task[];
  getTasksForDate: (date: string) => Task[];
  getTaskById: (taskId: string) => Task | undefined;
  getTaskStats: () => {
    total: number;
    completed: number;
    pending: number;
    overdue: number;
    byPriority: Record<string, number>;
    byStatus: Record<string, number>;
  };
  syncWithRemote: () => Promise<void>;
  setPendingChanges: (count: number) => void;
  markForSync: (taskId: string, operation: 'create' | 'update' | 'delete') => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLastUpdated: (timestamp: number) => void;
  setOnlineStatus: (online: boolean) => void;
  reset: () => void;
}

// Helper functions
const groupTasksByDate = (tasks: Task[]): TasksByDate => {
  return tasks.reduce((acc, task) => {
    const date = task.due_date || task.date;
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(task);
    return acc;
  }, {} as TasksByDate);
};

const filterTasks = (tasks: Task[], filters: TaskFilter, searchQuery: string): Task[] => {
  let filtered = tasks;

  if (searchQuery) {
    const query = searchQuery.toLowerCase();
    filtered = filtered.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.description.toLowerCase().includes(query) ||
      (task.category && task.category.toLowerCase().includes(query)) ||
      (task.tags && task.tags.some(tag => tag.toLowerCase().includes(query)))
    );
  }

  if (filters.status && filters.status.length > 0) {
    filtered = filtered.filter(task => 
      filters.status!.includes(task.status || 'pending')
    );
  }

  if (filters.priority && filters.priority.length > 0) {
    filtered = filtered.filter(task => 
      filters.priority!.includes(task.priority)
    );
  }

  if (filters.category && filters.category.length > 0) {
    filtered = filtered.filter(task => 
      task.category && filters.category!.includes(task.category)
    );
  }

  if (filters.date_range) {
    const startDate = new Date(filters.date_range.start);
    const endDate = new Date(filters.date_range.end);
    filtered = filtered.filter(task => {
      const taskDate = new Date(task.due_date || task.date);
      return taskDate >= startDate && taskDate <= endDate;
    });
  }

  if (filters.overdue_only) {
    const now = new Date();
    filtered = filtered.filter(task => {
      if (!task.due_date || task.status === 'completed') return false;
      return new Date(task.due_date) < now;
    });
  }

  return filtered;
};

const sortTasks = (tasks: Task[], sort: TaskSort): Task[] => {
  return [...tasks].sort((a, b) => {
    let aValue: any, bValue: any;

    switch (sort.field) {
      case 'created_at':
        aValue = new Date(a.created_at);
        bValue = new Date(b.created_at);
        break;
      case 'updated_at':
        aValue = new Date(a.updated_at || a.created_at);
        bValue = new Date(b.updated_at || b.created_at);
        break;
      case 'due_date':
        aValue = a.due_date ? new Date(a.due_date) : new Date('9999-12-31');
        bValue = b.due_date ? new Date(b.due_date) : new Date('9999-12-31');
        break;
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        aValue = priorityOrder[a.priority];
        bValue = priorityOrder[b.priority];
        break;
      case 'title':
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      default:
        return 0;
    }

    if (aValue === bValue) return 0;
    const comparison = aValue < bValue ? -1 : 1;
    return sort.direction === 'asc' ? comparison : -comparison;
  });
};

export const useUnifiedTaskStore = create<TaskStoreState & TaskStoreActions>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        tasks: [],
        tasksByDate: {},
        selectedTask: null,
        filters: {},
        sort: { field: 'created_at', direction: 'desc' },
        searchQuery: '',
        isLoading: false,
        error: null,
        lastUpdated: null,
        isOnline: true,
        pendingChanges: 0,
        lastSyncTimestamp: 0,

        // Task CRUD operations
        setTasks: (tasks) => set((state) => ({
          ...state,
          tasks,
          tasksByDate: groupTasksByDate(tasks),
          lastUpdated: Date.now()
        })),

        addTask: (task) => set((state) => {
          const newTasks = [task, ...state.tasks];
          return {
            ...state,
            tasks: newTasks,
            tasksByDate: groupTasksByDate(newTasks),
            lastUpdated: Date.now()
          };
        }),

        updateTask: (taskId, updates) => set((state) => {
          const updatedTasks = state.tasks.map(t =>
            t.id === taskId ? { ...t, ...updates, updated_at: new Date().toISOString() } : t
          );
          return {
            ...state,
            tasks: updatedTasks,
            tasksByDate: groupTasksByDate(updatedTasks),
            selectedTask: state.selectedTask?.id === taskId 
              ? { ...state.selectedTask, ...updates }
              : state.selectedTask,
            lastUpdated: Date.now()
          };
        }),

        deleteTask: (taskId) => set((state) => {
          const filteredTasks = state.tasks.filter(t => t.id !== taskId);
          return {
            ...state,
            tasks: filteredTasks,
            tasksByDate: groupTasksByDate(filteredTasks),
            selectedTask: state.selectedTask?.id === taskId ? null : state.selectedTask,
            lastUpdated: Date.now()
          };
        }),

        // Optimistic operations
        createTaskOptimistic: (taskData) => {
          const tempId = `temp_${Date.now()}`;
          const optimisticTask: Task = {
            ...taskData,
            id: tempId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            user_id: taskData.user_id || '',
            status: taskData.status || 'pending',
          } as Task;

          get().addTask(optimisticTask);
          get().markForSync(tempId, 'create');
          
          return tempId;
        },

        updateTaskOptimistic: (taskId, updates) => {
          get().updateTask(taskId, updates);
          get().markForSync(taskId, 'update');
        },

        deleteTaskOptimistic: (taskId) => {
          get().deleteTask(taskId);
          get().markForSync(taskId, 'delete');
        },

        // Selection and navigation
        setSelectedTask: (task) => set((state) => ({
          ...state,
          selectedTask: task
        })),

        selectTaskById: (taskId) => {
          const task = get().getTaskById(taskId);
          get().setSelectedTask(task || null);
        },

        // Filtering and sorting
        setFilters: (filters) => set((state) => ({
          ...state,
          filters: { ...state.filters, ...filters }
        })),

        setSort: (sort) => set((state) => ({
          ...state,
          sort
        })),

        setSearchQuery: (query) => set((state) => ({
          ...state,
          searchQuery: query
        })),

        resetFilters: () => set((state) => ({
          ...state,
          filters: {},
          searchQuery: '',
          sort: { field: 'created_at', direction: 'desc' }
        })),

        // Computed getters
        getFilteredTasks: () => {
          const { tasks, filters, searchQuery, sort } = get();
          const filtered = filterTasks(tasks, filters, searchQuery);
          return sortTasks(filtered, sort);
        },

        getTasksForDate: (date) => {
          return get().tasksByDate[date] || [];
        },

        getTaskById: (taskId) => {
          return get().tasks.find(task => task.id === taskId);
        },

        getTaskStats: () => {
          const tasks = get().tasks;
          const now = new Date();
          
          const stats = {
            total: tasks.length,
            completed: 0,
            pending: 0,
            overdue: 0,
            byPriority: { low: 0, medium: 0, high: 0 },
            byStatus: { pending: 0, completed: 0, cancelled: 0, 'on-hold': 0 },
          };

          tasks.forEach(task => {
            const status = task.status || 'pending';
            stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
            
            if (status === 'completed') stats.completed++;
            else if (status === 'pending') stats.pending++;

            stats.byPriority[task.priority]++;

            if (task.due_date && status !== 'completed' && new Date(task.due_date) < now) {
              stats.overdue++;
            }
          });

          return stats;
        },

        // Sync operations
        syncWithRemote: async () => {
          set((state) => ({ ...state, isLoading: true }));
          
          try {
            // Mock sync operation
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            set((state) => ({
              ...state,
              lastSyncTimestamp: Date.now(),
              pendingChanges: 0,
              error: null
            }));
          } catch (error) {
            set((state) => ({
              ...state,
              error: error instanceof Error ? error.message : 'Sync failed'
            }));
          } finally {
            set((state) => ({ ...state, isLoading: false }));
          }
        },

        setPendingChanges: (count) => set((state) => ({
          ...state,
          pendingChanges: count
        })),

        markForSync: (taskId, operation) => {
          const currentCount = get().pendingChanges;
          get().setPendingChanges(currentCount + 1);
        },

        // Base store actions
        setLoading: (loading) => set((state) => ({ ...state, isLoading: loading })),
        setError: (error) => set((state) => ({ ...state, error })),
        setLastUpdated: (timestamp) => set((state) => ({ ...state, lastUpdated: timestamp })),
        setOnlineStatus: (online) => set((state) => ({ ...state, isOnline: online })),
        
        reset: () => set({
          tasks: [],
          tasksByDate: {},
          selectedTask: null,
          filters: {},
          sort: { field: 'created_at', direction: 'desc' },
          searchQuery: '',
          isLoading: false,
          error: null,
          lastUpdated: null,
          isOnline: true,
          pendingChanges: 0,
          lastSyncTimestamp: 0
        })
      }),
      {
        name: 'unified-task-store',
        partialize: (state) => ({
          tasks: state.tasks,
          tasksByDate: state.tasksByDate,
          filters: state.filters,
          sort: state.sort,
          searchQuery: state.searchQuery,
          lastSyncTimestamp: state.lastSyncTimestamp,
        }),
      }
    ),
    { name: 'task-store' }
  )
);