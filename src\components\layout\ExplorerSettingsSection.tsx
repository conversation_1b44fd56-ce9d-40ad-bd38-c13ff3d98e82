import React, { useState, useEffect } from 'react';
import {
  Bell,
  Volume2,
  Languages,
  Type,
  Save,
  Download,
  Upload,
  Trash2,
  RefreshCw,
  Info
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface SettingsState {
  notificationsEnabled: boolean;
  soundEnabled: boolean;
  reminderTime: number;
  language: string;
  fontSize: number;
  autoSave: boolean;
}

interface ExplorerSettingsSectionProps {
  className?: string;
}

export const ExplorerSettingsSection: React.FC<ExplorerSettingsSectionProps> = ({
  className
}) => {
  const { toast } = useToast();
  
  const [settings, setSettings] = useState<SettingsState>({
    notificationsEnabled: true,
    soundEnabled: true,
    reminderTime: 15,
    language: 'en',
    fontSize: 16,
    autoSave: true
  });

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('app-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({
          notificationsEnabled: parsed.notificationsEnabled !== false,
          soundEnabled: parsed.soundEnabled !== false,
          reminderTime: parsed.reminderTime || 15,
          language: parsed.language || 'en',
          fontSize: parsed.fontSize || 16,
          autoSave: parsed.autoSave !== false
        });
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }, []);

  // Save settings whenever they change
  useEffect(() => {
    localStorage.setItem('app-settings', JSON.stringify(settings));
    
    // Apply font size
    document.documentElement.style.fontSize = `${settings.fontSize}px`;
  }, [settings]);

  const updateSetting = <K extends keyof SettingsState>(
    key: K,
    value: SettingsState[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Your data export will begin shortly"
    });
  };

  const handleImportData = () => {
    toast({
      title: "Import Ready",
      description: "Select a file to import your data"
    });
  };

  const handleClearData = () => {
    toast({
      title: "Data Cleared",
      description: "All application data has been reset",
      variant: "destructive"
    });
  };

  const handleRestart = () => {
    window.location.reload();
  };

  const settingsCards = [
    {
      id: 'notifications',
      title: 'Notifications',
      icon: Bell,
      content: (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs">Enable Notifications</Label>
            <Switch
              checked={settings.notificationsEnabled}
              onCheckedChange={(checked) => updateSetting('notificationsEnabled', checked)}
            />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Reminder Time</Label>
              <Badge variant="secondary" className="text-xs">
                {settings.reminderTime}min
              </Badge>
            </div>
            <Slider
              value={[settings.reminderTime]}
              onValueChange={(value) => updateSetting('reminderTime', value[0])}
              max={60}
              min={5}
              step={5}
              className="w-full"
              disabled={!settings.notificationsEnabled}
            />
          </div>
        </div>
      )
    },
    {
      id: 'audio',
      title: 'Audio',
      icon: Volume2,
      content: (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs">Sound Effects</Label>
            <Switch
              checked={settings.soundEnabled}
              onCheckedChange={(checked) => updateSetting('soundEnabled', checked)}
            />
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => toast({ title: "Sound Test", description: "Notification sound played" })}
            className="w-full h-7 text-xs"
            disabled={!settings.soundEnabled}
          >
            <Volume2 className="h-3 w-3 mr-1" />
            Test Sound
          </Button>
        </div>
      )
    },
    {
      id: 'display',
      title: 'Display',
      icon: Type,
      content: (
        <div className="space-y-3">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Font Size</Label>
              <Badge variant="secondary" className="text-xs">
                {settings.fontSize}px
              </Badge>
            </div>
            <Slider
              value={[settings.fontSize]}
              onValueChange={(value) => updateSetting('fontSize', value[0])}
              max={20}
              min={12}
              step={1}
              className="w-full"
            />
          </div>
        </div>
      )
    },
    {
      id: 'language',
      title: 'Language',
      icon: Languages,
      content: (
        <div className="space-y-3">
          <Select
            value={settings.language}
            onValueChange={(value) => updateSetting('language', value)}
          >
            <SelectTrigger className="h-7 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="es">Español</SelectItem>
              <SelectItem value="fr">Français</SelectItem>
              <SelectItem value="de">Deutsch</SelectItem>
              <SelectItem value="zh">中文</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )
    },
    {
      id: 'data',
      title: 'Data Management',
      icon: Save,
      content: (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs">Auto Save</Label>
            <Switch
              checked={settings.autoSave}
              onCheckedChange={(checked) => updateSetting('autoSave', checked)}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              className="h-7 text-xs p-1"
            >
              <Download className="h-3 w-3" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleImportData}
              className="h-7 text-xs p-1"
            >
              <Upload className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )
    },
    {
      id: 'system',
      title: 'System',
      icon: RefreshCw,
      content: (
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRestart}
              className="h-7 text-xs p-1"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toast({ title: "App Info", description: "Task Manager v1.0.0" })}
              className="h-7 text-xs p-1"
            >
              <Info className="h-3 w-3" />
            </Button>
          </div>
          
          <Button
            variant="destructive"
            size="sm"
            onClick={handleClearData}
            className="w-full h-7 text-xs"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Clear Data
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className={cn("space-y-3", className)}>
      {settingsCards.map((setting) => {
        const Icon = setting.icon;
        return (
          <Card key={setting.id} className="bg-card/50 backdrop-blur-sm border-l-4 border-l-blue-500">
            <CardContent className="p-0">
              <div className="bg-green-50 dark:bg-green-900/20 px-2 py-2 rounded-t-md">
                <div className="text-xs font-medium flex items-center gap-2">
                  <div className="p-1 rounded-md bg-blue-500/20">
                    <Icon className="h-3 w-3 text-blue-500" />
                  </div>
                  {setting.title}
                </div>
              </div>
              <div className="p-2">
                {setting.content}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};