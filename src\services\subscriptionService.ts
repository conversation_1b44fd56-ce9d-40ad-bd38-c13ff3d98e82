import { useAuth } from '@/contexts/AuthContext';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface CreateCheckoutSessionRequest {
  userId: string;
  email: string;
  name?: string;
  priceId?: string;
  lookupKey?: string;
  billingCycle: 'monthly' | 'yearly';
  successUrl?: string;
  cancelUrl?: string;
}

interface CreateCheckoutSessionResponse {
  sessionId: string;
  url: string;
  customerId: string;
}

interface CreateCustomerPortalRequest {
  customerId: string;
  returnUrl?: string;
}

interface CreateCustomerPortalResponse {
  url: string;
  sessionId: string;
}

export class SubscriptionService {
  private static async fetchAPI<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  /**
   * Creates a Stripe Checkout session for subscription
   */
  static async createCheckoutSession(
    request: CreateCheckoutSessionRequest
  ): Promise<CreateCheckoutSessionResponse> {
    return this.fetchAPI<CreateCheckoutSessionResponse>(
      '/api/stripe/create-checkout-session',
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    );
  }

  /**
   * Creates a Stripe Customer Portal session
   */
  static async createCustomerPortalSession(
    request: CreateCustomerPortalRequest
  ): Promise<CreateCustomerPortalResponse> {
    return this.fetchAPI<CreateCustomerPortalResponse>(
      '/api/stripe/create-customer-portal',
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    );
  }

  /**
   * Redirects to Stripe Checkout for subscription
   */
  static async redirectToCheckout(
    planId: string,
    billingCycle: 'monthly' | 'yearly',
    userId: string,
    email: string,
    name?: string
  ): Promise<void> {
    try {
      const lookupKey = billingCycle === 'yearly' ? `${planId}_yearly` : `${planId}_monthly`;
      
      const response = await this.createCheckoutSession({
        userId,
        email,
        name,
        lookupKey,
        billingCycle,
      });

      // Redirect to Stripe Checkout
      window.location.href = response.url;
    } catch (error) {
      console.error('Error redirecting to checkout:', error);
      throw error;
    }
  }

  /**
   * Redirects to Stripe Customer Portal
   */
  static async redirectToCustomerPortal(customerId: string): Promise<void> {
    try {
      const response = await this.createCustomerPortalSession({
        customerId,
      });

      // Redirect to Stripe Customer Portal
      window.location.href = response.url;
    } catch (error) {
      console.error('Error redirecting to customer portal:', error);
      throw error;
    }
  }
}

// Hook for using subscription service with authenticated user data
export const useSubscriptionService = () => {
  const { user } = useAuth();

  const redirectToCheckout = async (
    planId: string,
    billingCycle: 'monthly' | 'yearly'
  ) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const email = user.email;
    if (!email) {
      throw new Error('User email not found');
    }

    const name = user.user_metadata?.full_name || undefined;

    return SubscriptionService.redirectToCheckout(
      planId,
      billingCycle,
      user.id,
      email,
      name
    );
  };

  const redirectToCustomerPortal = async (customerId: string) => {
    return SubscriptionService.redirectToCustomerPortal(customerId);
  };

  return {
    redirectToCheckout,
    redirectToCustomerPortal,
    user,
  };
};
