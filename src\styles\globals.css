/* Global styles for better cursor visibility */
input, textarea, [contenteditable] {
  caret-color: #000 !important;
}

/* Dark mode cursor */
@media (prefers-color-scheme: dark) {
  input, textarea, [contenteditable] {
    caret-color: #fff !important;
  }
}

/* Ensure cursor is visible on focus */
input:focus, textarea:focus, [contenteditable]:focus {
  caret-color: #3b82f6 !important; /* Blue cursor on focus */
}

/* Fix for any potential cursor issues */
* {
  caret-color: auto;
}

input[type="text"], input[type="email"], input[type="password"], textarea {
  caret-color: #000 !important;
}

.dark input[type="text"], .dark input[type="email"], .dark input[type="password"], .dark textarea {
  caret-color: #fff !important;
/* Modern Task List Enhancements */
@import "./modern-task-list.css";
}
