import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SupabaseStorageService } from '@/services/supabaseStorageService';
import { useAuth } from '@/contexts/AuthContext';
import { 
  HardDrive, 
  Download, 
  Upload, 
  Trash2, 
  Info,
  Database,
  Loader2,
  FileText,
  Music,
  Paperclip,
  Zap
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useDebounceAsync } from '@/hooks/useDebounce';

interface StorageInfoProps {
  onDataChange?: () => void;
}

interface StorageInfo {
  taskCount: number;
  totalSize: number;
  sizeFormatted: string;
  attachmentCount: number;
  voiceNoteCount: number;
}

export const StorageInfo: React.FC<StorageInfoProps> = ({ onDataChange }) => {
  const [storageInfo, setStorageInfo] = useState<StorageInfo>({
    taskCount: 0,
    totalSize: 0,
    sizeFormatted: '0 B',
    attachmentCount: 0,
    voiceNoteCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [cleanupCount, setCleanupCount] = useState<number | null>(null);
  const { user } = useAuth();
  const userId = user?.id;
  const { toast } = useToast();

  const updateStorageInfo = async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      const info = await SupabaseStorageService.getStorageInfo(userId);
      setStorageInfo(info);
    } catch (error) {
      console.error('Error updating storage info:', error);
      toast({
        title: 'Error',
        description: 'Failed to load storage information',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Debounced export function
  const { debouncedFn: debouncedExport, isLoading: isExporting } = useDebounceAsync(
    async () => {
      if (!userId) return;
      const data = await SupabaseStorageService.exportUserData(userId);
      return data;
    },
    500
  );

  // Debounced import function
  const { debouncedFn: debouncedImport, isLoading: isImporting } = useDebounceAsync(
    async (importData: any) => {
      if (!userId) return;
      await SupabaseStorageService.importUserData(userId, importData);
    },
    500
  );

  useEffect(() => {
    if (userId) {
      updateStorageInfo();
      // Update storage info every 5 minutes (much less frequent to prevent refresh issues)
      const interval = setInterval(updateStorageInfo, 300000);
      return () => clearInterval(interval);
    }
  }, [userId]);

  const handleExport = async () => {
    if (!userId) return;
    
    try {
      const data = await debouncedExport();
      const dataStr = JSON.stringify(data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `scheduled-tasks-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Data exported",
        description: `Successfully exported ${data.summary.taskCount} tasks and ${data.summary.attachmentCount} attachments.`,
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && userId) {
        const reader = new FileReader();
        reader.onload = async (e) => {
          try {
            const importData = JSON.parse(e.target?.result as string);
            await debouncedImport(importData);
            await updateStorageInfo();
            onDataChange?.();
            
            toast({
              title: "Data imported",
              description: `Successfully imported ${importData.tasks?.length || 0} tasks.`,
            });
          } catch (error) {
            toast({
              title: "Import failed",
              description: "Invalid JSON file or import failed. Please check the file format.",
              variant: "destructive",
            });
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const handleClearAll = async () => {
    if (!userId) return;
    
    const confirmed = window.confirm(
      'Are you sure you want to delete ALL your data? This includes all tasks, attachments, and voice notes. This action cannot be undone.'
    );
    
    if (confirmed) {
      try {
        await SupabaseStorageService.clearAllUserData(userId);
        await updateStorageInfo();
        onDataChange?.();
        
        toast({
          title: "All data deleted",
          description: "All tasks, attachments, and settings have been cleared.",
        });
      } catch (error) {
        toast({
          title: "Delete failed",
          description: "Failed to delete all data. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleCleanup = async () => {
    if (!userId) return;
    
    try {
      const cleanedCount = await SupabaseStorageService.cleanupOrphanedFiles(userId);
      setCleanupCount(cleanedCount);
      await updateStorageInfo();
      
      toast({
        title: "Cleanup completed",
        description: `Cleaned up ${cleanedCount} orphaned files.`,
      });
    } catch (error) {
      toast({
        title: "Cleanup failed",
        description: "Failed to cleanup orphaned files.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Database className="h-4 w-4" />
          Local Storage
        </CardTitle>
        <CardDescription className="text-xs">
          Your tasks are stored locally in your browser
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Storage Stats */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <HardDrive className="h-4 w-4 text-muted-foreground" />
            <span>Storage Used:</span>
          </div>
          <Badge variant="outline" className="text-xs">
            {storageInfo.sizeFormatted}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Info className="h-4 w-4 text-muted-foreground" />
            <span>Total Tasks:</span>
          </div>
          <Badge variant="outline" className="text-xs">
            {storageInfo.taskCount}
          </Badge>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="flex-1 text-xs"
            disabled={storageInfo.taskCount === 0}
          >
            <Download className="h-3 w-3 mr-1" />
            Export
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleImport}
            className="flex-1 text-xs"
          >
            <Upload className="h-3 w-3 mr-1" />
            Import
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearAll}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 text-xs"
            disabled={storageInfo.taskCount === 0}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>

        {/* Storage Info */}
        <div className="text-xs text-muted-foreground pt-2 border-t">
          <p>💡 <strong>Data Storage:</strong></p>
          <ul className="mt-1 space-y-1 ml-4">
            <li>• Tasks stored locally in your browser</li>
            <li>• Attachments stored as blob URLs</li>
            <li>• Voice notes stored as audio URLs</li>
            <li>• Export/import for backup & sync</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
