{"name": "taskmanagermobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.52.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "expo": "~53.0.20", "expo-auth-session": "^6.2.1", "expo-device": "^7.1.4", "expo-notifications": "^0.31.4", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "metro": "^0.82.0", "papaparse": "^5.5.3", "react": "19.1.0", "react-dom": "^19.1.0", "react-native": "0.79.5", "react-native-audio-recorder-player": "^4.2.0", "react-native-calendars": "^1.1313.0", "react-native-document-picker": "^9.3.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-image-picker": "^8.2.1", "react-native-nitro-modules": "^0.26.4", "react-native-pdf": "^6.7.7", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.1.0", "react-native-sound": "^0.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-video": "^6.16.1", "react-native-widget-extension": "^0.0.6", "xlsx": "^0.18.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}