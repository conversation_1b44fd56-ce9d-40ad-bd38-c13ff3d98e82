import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Settings, BarChart3, User, Palette } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ExplorerAnalyticsSection } from './ExplorerAnalyticsSection';
import { ExplorerSettingsSection } from './ExplorerSettingsSection';
import { ExplorerThemeSection } from './ExplorerThemeSection';
import { ExplorerProfileSection } from './ExplorerProfileSection';
import { Task } from '@/types/task';

interface ExplorerSection {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  isOpen: boolean;
}

interface LeftExplorerPanelProps {
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  selectedDate?: Date | null;
  tasks?: Task[];
  onAnalyticsCardClick?: (cardId: string, tasks: Task[]) => void;
}

export const LeftExplorerPanel: React.FC<LeftExplorerPanelProps> = ({
  className,
  isCollapsed = false,
  onToggleCollapse,
  selectedDate,
  tasks = [],
  onAnalyticsCardClick
}) => {
  const [sections, setSections] = useState<ExplorerSection[]>([
    {
      id: 'analytics',
      title: 'Analytics',
      icon: BarChart3,
      isOpen: false
    },

    {
      id: 'settings',
      title: 'Settings',
      icon: Settings,
      isOpen: false
    },
    {
      id: 'appearance',
      title: 'Appearance',
      icon: Palette,
      isOpen: false
    },
    {
      id: 'profile',
      title: 'Profile',
      icon: User,
      isOpen: false
    }
  ]);

  // Load section states from localStorage
  useEffect(() => {
    const savedStates = localStorage.getItem('explorer-panel-states');
    if (savedStates) {
      try {
        const parsed = JSON.parse(savedStates);
        setSections(prev => prev.map(section => ({
          ...section,
          isOpen: parsed[section.id] !== undefined ? parsed[section.id] : section.isOpen
        })));
      } catch (error) {
        console.error('Error loading explorer panel states:', error);
      }
    }
  }, []);

  // Save section states to localStorage
  const saveSectionStates = (updatedSections: ExplorerSection[]) => {
    const states = updatedSections.reduce((acc, section) => {
      acc[section.id] = section.isOpen;
      return acc;
    }, {} as Record<string, boolean>);
    localStorage.setItem('explorer-panel-states', JSON.stringify(states));
  };

  const toggleSection = (sectionId: string) => {
    const updatedSections = sections.map(section => {
      if (section.id === sectionId) {
        return { ...section, isOpen: !section.isOpen };
      } else {
        // Close all other sections when one is opened
        return { ...section, isOpen: false };
      }
    });
    setSections(updatedSections);
    saveSectionStates(updatedSections);
  };

  const handleCollapsedIconClick = (sectionId: string) => {
    // First, open the specific section
    const updatedSections = sections.map(section => ({
      ...section,
      isOpen: section.id === sectionId
    }));
    setSections(updatedSections);
    saveSectionStates(updatedSections);
    
    // Then expand the panel
    onToggleCollapse?.();
  };

  if (isCollapsed) {
    return (
      <div className={cn(
        "w-12 bg-background border-r border-border flex flex-col items-center py-4 space-y-4 z-20",
        className
      )}>
        {sections.map(section => {
          const Icon = section.icon;
          return (
            <Button
              key={section.id}
              variant="ghost"
              size="sm"
              className="w-8 h-8 p-0"
              onClick={() => handleCollapsedIconClick(section.id)}
              title={section.title}
            >
              <Icon className="h-4 w-4" />
            </Button>
          );
        })}
      </div>
    );
  }

  return (
    <div className={cn(
      "w-80 bg-background border-r border-border flex flex-col h-full z-20",
      className
    )}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-semibold text-foreground">Explorer</h2>
          <Button
            variant="ghost"
            size="sm"
            className="w-6 h-6 p-0"
            onClick={onToggleCollapse}
          >
            <ChevronDown className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Scrollable Content */}
      <ScrollArea className="flex-1">
        <div className="px-3 py-2 space-y-1">
          {sections.map(section => {
            const Icon = section.icon;
            return (
              <Collapsible
                key={section.id}
                open={section.isOpen}
                onOpenChange={() => toggleSection(section.id)}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start h-8 px-2 text-sm font-semibold text-foreground hover:text-foreground hover:bg-accent"
                  >
                    {section.isOpen ? (
                      <ChevronDown className="h-3 w-3 mr-1 text-muted-foreground" />
                    ) : (
                      <ChevronRight className="h-3 w-3 mr-1 text-muted-foreground" />
                    )}
                    <Icon className={cn(
                      "h-3 w-3 mr-2",
                      section.id === 'settings' && "text-blue-500",
                      section.id === 'analytics' && "text-green-500",

                      section.id === 'profile' && "text-purple-500",
                      section.id === 'theme' && "text-orange-500"
                    )} />
                    {section.title}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-1 ml-2 mr-1">
                  {section.id === 'analytics' && (
                    <ExplorerAnalyticsSection 
                      tasks={tasks}
                      onCardClick={onAnalyticsCardClick}
                    />
                  )}

                  {section.id === 'settings' && (
                    <ExplorerSettingsSection />
                  )}
                  {section.id === 'appearance' && (
                    <ExplorerThemeSection />
                  )}
                  {section.id === 'profile' && (
                    <ExplorerProfileSection />
                  )}
                </CollapsibleContent>
              </Collapsible>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
};