import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, LucideIcon, AlertCircle, AlertTriangle, Circle, PauseCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

export interface ExplorerAnalyticsCardData {
  id: string;
  title: string;
  subtitle?: string;
  value: string | number;
  icon: LucideIcon;
  color: string;
  data?: any[];
  isUnified?: boolean;
  unifiedData?: {
    high: { count: number; data: any[] };
    medium: { count: number; data: any[] };
    low: { count: number; data: any[] };
  };
  isStatusUnified?: boolean;
  statusUnifiedData?: {
    total: { count: number; data: any[]; icon: LucideIcon; color: string };
    completed: { count: number; data: any[]; icon: LucideIcon; color: string };
    pending: { count: number; data: any[]; icon: LucideIcon; color: string };
    onHold: { count: number; data: any[]; icon: LucideIcon; color: string };
  };
  isDueDateUnified?: boolean;
  dueDateUnifiedData?: {
    overdue: { count: number; data: any[]; icon: LucideIcon; color: string };
    notYetDue: { count: number; data: any[]; icon: LucideIcon; color: string };
  };
}

interface ExplorerAnalyticsCardProps {
  card: ExplorerAnalyticsCardData;
  onClick?: (card: ExplorerAnalyticsCardData) => void;
  onUnifiedClick?: (priority: 'high' | 'medium' | 'low', data: any[]) => void;
  onStatusUnifiedClick?: (status: 'total' | 'completed' | 'pending' | 'onHold', data: any[]) => void;
  onDueDateUnifiedClick?: (dueDateType: 'overdue' | 'notYetDue', data: any[]) => void;
  isDragging?: boolean;
}

export const ExplorerAnalyticsCard: React.FC<ExplorerAnalyticsCardProps> = ({
  card,
  onClick,
  onUnifiedClick,
  onStatusUnifiedClick,
  onDueDateUnifiedClick,
  isDragging = false
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging
  } = useSortable({ id: card.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const Icon = card.icon;
  const isCurrentlyDragging = isDragging || isSortableDragging;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "group relative",
        isCurrentlyDragging && "z-50 opacity-50"
      )}
    >
      <Card 
        className={cn(
          "transition-all duration-200 hover:shadow-md border-l-4 bg-card/50 backdrop-blur-sm",
          "hover:bg-card/80 hover:border-l-4",
          isCurrentlyDragging && "shadow-lg scale-105",
          !(card.isUnified || card.isStatusUnified || card.isDueDateUnified) && "cursor-pointer"
        )}
        style={{ borderLeftColor: card.color }}
        onClick={() => {
          if (!card.isUnified && !card.isStatusUnified && !card.isDueDateUnified) {
            onClick?.(card);
          }
        }}
      >
        <CardContent className="p-0">
          <div className="bg-green-50 dark:bg-green-900/20 px-3 py-1 rounded-t-md">
            <div className="flex items-center gap-2">
              <div 
                className="p-1 rounded-md flex-shrink-0"
                style={{ backgroundColor: `${card.color}20` }}
              >
                <Icon 
                  className="h-3 w-3" 
                  style={{ color: card.color }}
                />
              </div>
              <h3 className="text-xs font-medium text-foreground truncate">
                {card.title}
              </h3>
            </div>
          </div>
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="px-3 pt-1">
            <div className="space-y-1 mb-2">
              <p className="text-lg font-bold text-foreground leading-none">
                {card.value}
              </p>
              {card.subtitle && (
                <p className="text-xs text-muted-foreground truncate">
                  {card.subtitle}
                </p>
              )}
             </div>
              </div>
                
             {/* Unified Priority Sections */}
                  {card.isUnified && card.unifiedData && (
                   <div className="flex gap-2 mt-1 px-3 pb-2">
                      <TooltipProvider>
                        <Tooltip>
                           <TooltipTrigger asChild>
                             <button
                               onClick={(e) => {
                                 e.stopPropagation();
                                 onUnifiedClick?.('high', card.unifiedData!.high.data);
                               }}
                               className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-red-100 text-red-700 flex flex-col items-center"
                             >
                               <AlertCircle className="h-4 w-4 mb-1" />
                               <span className="text-sm font-bold">{card.unifiedData.high.count}</span>
                             </button>
                           </TooltipTrigger>
                           <TooltipContent>
                             <p>High Priority Tasks</p>
                           </TooltipContent>
                         </Tooltip>
                        <Tooltip>
                           <TooltipTrigger asChild>
                             <button
                               onClick={(e) => {
                                 e.stopPropagation();
                                 onUnifiedClick?.('medium', card.unifiedData!.medium.data);
                               }}
                               className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-yellow-100 text-yellow-600 flex flex-col items-center"
                             >
                               <AlertTriangle className="h-4 w-4 mb-1" />
                               <span className="text-sm font-bold">{card.unifiedData.medium.count}</span>
                             </button>
                           </TooltipTrigger>
                           <TooltipContent>
                             <p>Medium Priority Tasks</p>
                           </TooltipContent>
                         </Tooltip>
                         <Tooltip>
                           <TooltipTrigger asChild>
                             <button
                               onClick={(e) => {
                                 e.stopPropagation();
                                 onUnifiedClick?.('low', card.unifiedData!.low.data);
                               }}
                               className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-emerald-100 text-emerald-600 flex flex-col items-center"
                             >
                               <Circle className="h-4 w-4 mb-1" />
                               <span className="text-sm font-bold">{card.unifiedData.low.count}</span>
                             </button>
                           </TooltipTrigger>
                           <TooltipContent>
                             <p>Low Priority Tasks</p>
                           </TooltipContent>
                         </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}

                  {/* Unified Status Sections */}
                  {card.isStatusUnified && card.statusUnifiedData && (
                   <div className="flex gap-2 mt-1 px-3 pb-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onStatusUnifiedClick?.('total', card.statusUnifiedData!.total.data);
                              }}
                              className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-blue-100 flex flex-col items-center"
                              style={{ color: card.statusUnifiedData.total.color }}
                            >
                              <card.statusUnifiedData.total.icon className="h-4 w-4 mb-1" />
                              <span className="text-sm font-bold">{card.statusUnifiedData.total.count}</span>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Total Tasks</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onStatusUnifiedClick?.('completed', card.statusUnifiedData!.completed.data);
                              }}
                              className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-green-100 flex flex-col items-center"
                              style={{ color: card.statusUnifiedData.completed.color }}
                            >
                              <card.statusUnifiedData.completed.icon className="h-4 w-4 mb-1" />
                              <span className="text-sm font-bold">{card.statusUnifiedData.completed.count}</span>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Completed Tasks</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onStatusUnifiedClick?.('pending', card.statusUnifiedData!.pending.data);
                              }}
                              className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-yellow-100 flex flex-col items-center"
                              style={{ color: card.statusUnifiedData.pending.color }}
                            >
                              <card.statusUnifiedData.pending.icon className="h-4 w-4 mb-1" />
                              <span className="text-sm font-bold">{card.statusUnifiedData.pending.count}</span>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Pending Tasks</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onStatusUnifiedClick?.('onHold', card.statusUnifiedData!.onHold.data);
                              }}
                              className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-purple-100 flex flex-col items-center"
                              style={{ color: card.statusUnifiedData.onHold.color }}
                            >
                              <card.statusUnifiedData.onHold.icon className="h-4 w-4 mb-1" />
                              <span className="text-sm font-bold">{card.statusUnifiedData.onHold.count}</span>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>On Hold Tasks</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}

                  {/* Unified Due Date Sections */}
                  {card.isDueDateUnified && card.dueDateUnifiedData && (
                   <div className="flex gap-2 mt-1 px-3 pb-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onDueDateUnifiedClick?.('overdue', card.dueDateUnifiedData!.overdue.data);
                              }}
                              className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-red-100 flex flex-col items-center"
                              style={{ color: card.dueDateUnifiedData.overdue.color }}
                            >
                              <card.dueDateUnifiedData.overdue.icon className="h-4 w-4 mb-1" />
                              <span className="text-sm font-bold">{card.dueDateUnifiedData.overdue.count}</span>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Overdue</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onDueDateUnifiedClick?.('notYetDue', card.dueDateUnifiedData!.notYetDue.data);
                              }}
                              className="flex-1 px-2 py-2 rounded text-sm font-medium transition-colors hover:bg-blue-100 flex flex-col items-center"
                              style={{ color: card.dueDateUnifiedData.notYetDue.color }}
                            >
                              <card.dueDateUnifiedData.notYetDue.icon className="h-4 w-4 mb-1" />
                              <span className="text-sm font-bold">{card.dueDateUnifiedData.notYetDue.count}</span>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Not Yet Due</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}
            </div>
            
            {/* Drag Handle */}
            <div
              {...attributes}
              {...listeners}
              className={cn(
                "opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing",
                "p-1 rounded hover:bg-accent absolute right-2 top-2",
                isCurrentlyDragging && "opacity-100"
              )}
            >
              <GripVertical className="h-3 w-3 text-muted-foreground" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Sortable wrapper for the analytics card
export const SortableExplorerAnalyticsCard: React.FC<ExplorerAnalyticsCardProps> = (props) => {
  return <ExplorerAnalyticsCard {...props} />;
};