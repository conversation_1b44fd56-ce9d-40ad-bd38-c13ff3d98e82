import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from '@dnd-kit/modifiers';
import { format } from 'date-fns';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Calendar,
  TrendingUp,
  Users,
  Target,
  Zap,
  AlertTriangle,
  PlayCircle,
  PauseCircle,
  XCircle,
  User,

  UserX,
  TrendingDown,
  BarChart3,
  Activity,
  Tag,
  RefreshCw
} from 'lucide-react';
import { ExplorerAnalyticsCard, ExplorerAnalyticsCardData } from './ExplorerAnalyticsCard';
import { Task } from '@/types/task';

interface ExplorerAnalyticsSectionProps {
  tasks: Task[];
  onCardClick?: (cardId: string, tasks: Task[]) => void;
}

export const ExplorerAnalyticsSection: React.FC<ExplorerAnalyticsSectionProps> = ({
  tasks,
  onCardClick,
}) => {
  const [cardOrder, setCardOrder] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasUserCustomized, setHasUserCustomized] = useState(false);

  // Enhanced storage functions that use multiple mechanisms
  const getStorageItem = useCallback((key: string): string | null => {
    try {
      // Try localStorage first
      const localValue = localStorage.getItem(key);
      if (localValue) return localValue;
      
      // Try sessionStorage as fallback
      const sessionValue = sessionStorage.getItem(key);
      if (sessionValue) return sessionValue;
      
      // Try cookie as last resort
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [cookieKey, cookieValue] = cookie.trim().split('=');
        if (cookieKey === key) {
          return decodeURIComponent(cookieValue);
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error accessing storage:', error);
      return null;
    }
  }, []);

  const setStorageItem = useCallback((key: string, value: string): void => {
    try {
      // Save to localStorage
      localStorage.setItem(key, value);
      
      // Save to sessionStorage as backup
      sessionStorage.setItem(key, value);
      
      // Save to cookie as last resort (expires in 30 days)
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30);
      document.cookie = `${key}=${encodeURIComponent(value)}; expires=${expirationDate.toUTCString()}; path=/`;
    } catch (error) {
      console.error('Error saving to storage:', error);
    }
  }, []);

  // Set up drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Calculate analytics data
  const analyticsData = useMemo(() => {
    const today = new Date();
    
    // Filter tasks up to today (cumulative)
    const relevantTasks = tasks.filter(task => {
      const taskDate = new Date(task.createdAt);
      const todayUTC = new Date(Date.UTC(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        23, 59, 59, 999
      ));
      return taskDate <= todayUTC;
    });

    // Calculate basic metrics
    const totalTasks = relevantTasks.length;
    const completedTasks = relevantTasks.filter(task => task.status === 'completed').length;
    const pendingTasks = relevantTasks.filter(task => task.status === 'pending').length;

    const onHoldTasks = relevantTasks.filter(task => task.status === 'on-hold').length;
    const cancelledTasks = relevantTasks.filter(task => task.status === 'cancelled').length;
    
    // Calculate priority metrics
    const highPriorityTasks = relevantTasks.filter(task => task.priority === 'high').length;
    const mediumPriorityTasks = relevantTasks.filter(task => task.priority === 'medium').length;
    const lowPriorityTasks = relevantTasks.filter(task => task.priority === 'low').length;
    

    
    // Calculate tagged tasks metrics
    const taggedTasks = relevantTasks.filter(task => {
      const tags = (task as any).tags;
      return tags && Array.isArray(tags) && tags.length > 0;
    }).length;
    
    // Calculate recurring tasks metrics
    const recurringTasks = relevantTasks.filter(task => {
      return task.recurringPattern !== undefined && task.recurringPattern !== null;
    }).length;
    
    // Calculate overdue and upcoming
    const overdueTasks = relevantTasks.filter(task => {
      if (!task.dueDate) return false;
      const dueDate = new Date(task.dueDate);
      if (isNaN(dueDate.getTime())) return false; // Check if date is valid
      return dueDate < today && task.status !== 'completed';
    }).length;
    
    const upcomingTasks = relevantTasks.filter(task => {
      if (!task.dueDate) return false;
      const dueDate = new Date(task.dueDate);
      if (isNaN(dueDate.getTime())) return false; // Check if date is valid
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const nextWeek = new Date(today);
      nextWeek.setDate(nextWeek.getDate() + 7);
      return dueDate >= tomorrow && dueDate <= nextWeek && task.status !== 'completed';
    }).length;
    
    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    
    return {
      totalTasks,
      completedTasks,
      pendingTasks,

      onHoldTasks,
      cancelledTasks,
      highPriorityTasks,
      mediumPriorityTasks,
      lowPriorityTasks,

      taggedTasks,
      recurringTasks,
      overdueTasks,
      upcomingTasks,
      completionRate,
      relevantTasks
    };
  }, [tasks]);

  // Define default cards
  const defaultCards: ExplorerAnalyticsCardData[] = useMemo(() => [
    {
      id: 'due-date-unified',
      title: 'Due Tasks',
      subtitle: '',
      value: '',
      icon: Calendar,
      color: '#6366f1',
      data: analyticsData.relevantTasks.filter(task => task.dueDate),
      isDueDateUnified: true,
      dueDateUnifiedData: {
        overdue: {
          count: analyticsData.overdueTasks,
          data: analyticsData.relevantTasks.filter(task => {
            if (!task.dueDate) return false;
            const dueDate = new Date(task.dueDate);
            if (isNaN(dueDate.getTime())) return false; // Check if date is valid
            const today = new Date();
            return dueDate < today && task.status !== 'completed';
          }),
          icon: AlertTriangle,
          color: '#ef4444'
        },
        notYetDue: {
          count: analyticsData.relevantTasks.filter(task => {
            if (!task.dueDate) return false;
            const dueDate = new Date(task.dueDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            dueDate.setHours(0, 0, 0, 0);
            return dueDate >= today && task.status !== 'completed' && task.status !== 'cancelled';
          }).length,
          data: analyticsData.relevantTasks.filter(task => {
            if (!task.dueDate) return false;
            const dueDate = new Date(task.dueDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            dueDate.setHours(0, 0, 0, 0);
            return dueDate >= today && task.status !== 'completed' && task.status !== 'cancelled';
          }),
          icon: Calendar,
          color: '#3b82f6'
        }
      }
    },
    {
      id: 'priority-unified',
      title: 'Priority Tasks',
      subtitle: '',
      value: '',
      icon: Target,
      color: '#6b7280',
      data: analyticsData.relevantTasks.filter(task => task.priority),
      isUnified: true,
      unifiedData: {
        high: {
          count: analyticsData.highPriorityTasks,
          data: analyticsData.relevantTasks.filter(task => task.priority === 'high')
        },
        medium: {
          count: analyticsData.mediumPriorityTasks,
          data: analyticsData.relevantTasks.filter(task => task.priority === 'medium')
        },
        low: {
          count: analyticsData.lowPriorityTasks,
          data: analyticsData.relevantTasks.filter(task => task.priority === 'low')
        }
      }
    },

    {
      id: 'tagged-tasks',
      title: 'Tagged Tasks',
      subtitle: 'Has tags',
      value: analyticsData.taggedTasks,
      icon: Tag,
      color: '#f97316',
      data: analyticsData.relevantTasks.filter(task => {
        const tags = (task as any).tags;
        return tags && Array.isArray(tags) && tags.length > 0;
      })
    },
    {
      id: 'recurring-tasks',
      title: 'Recurring Tasks',
      subtitle: 'Has recurring pattern',
      value: analyticsData.recurringTasks,
      icon: RefreshCw,
      color: '#8b5cf6',
      data: analyticsData.relevantTasks.filter(task => {
        return task.recurringPattern !== undefined && task.recurringPattern !== null;
      })
    },
    {
      id: 'status-unified',
      title: 'Task Status Overview',
      subtitle: '',
      value: '',
      icon: BarChart3,
      color: '#6366f1',
      data: analyticsData.relevantTasks,
      isStatusUnified: true,
      statusUnifiedData: {
        total: {
          count: analyticsData.totalTasks,
          data: analyticsData.relevantTasks,
          icon: Calendar,
          color: '#3b82f6'
        },
        completed: {
          count: analyticsData.completedTasks,
          data: analyticsData.relevantTasks.filter(task => task.status === 'completed'),
          icon: CheckCircle,
          color: '#10b981'
        },
        pending: {
          count: analyticsData.pendingTasks,
          data: analyticsData.relevantTasks.filter(task => task.status === 'pending'),
          icon: Clock,
          color: '#f59e0b'
        },
        onHold: {
          count: analyticsData.relevantTasks.filter(task => task.status === 'on-hold').length,
          data: analyticsData.relevantTasks.filter(task => task.status === 'on-hold'),
          icon: PauseCircle,
          color: '#8b5cf6'
        }
      }
    }
  ], [analyticsData]);

  // Load card order from enhanced storage - only run once on mount
  useEffect(() => {
    const savedOrder = getStorageItem('explorer-analytics-card-order');
    const userCustomized = getStorageItem('explorer-analytics-user-customized');
    
    if (savedOrder && userCustomized === 'true') {
      try {
        const parsed = JSON.parse(savedOrder);
        // Check if the new due-date-unified card is in the saved order
        if (!parsed.includes('due-date-unified')) {
          // If not, add it to the beginning of the order
          parsed.unshift('due-date-unified');
          setStorageItem('explorer-analytics-card-order', JSON.stringify(parsed));
        }
        setCardOrder(parsed);
        setHasUserCustomized(true);
        setIsInitialized(true);
      } catch (error) {
        console.error('Error loading card order:', error);
        // If there's an error with saved order, use default order
        setCardOrder(defaultCards.map(card => card.id));
        setIsInitialized(true);
      }
    } else {
      // If no saved order exists or user hasn't customized, use default order for first time
      setCardOrder(defaultCards.map(card => card.id));
      setIsInitialized(true);
    }
  }, [getStorageItem, defaultCards, setStorageItem]); // Empty dependency array - only run once on mount

  // Save card order to enhanced storage
  const saveCardOrder = (order: string[]) => {
    try {
      setStorageItem('explorer-analytics-card-order', JSON.stringify(order));
      // Mark that the user has customized the order
      setStorageItem('explorer-analytics-user-customized', 'true');
      setHasUserCustomized(true);
    } catch (error) {
      console.error('Error saving card order:', error);
    }
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = cardOrder.indexOf(active.id as string);
      const newIndex = cardOrder.indexOf(over.id as string);
      const newOrder = arrayMove(cardOrder, oldIndex, newIndex);
      setCardOrder(newOrder);
      saveCardOrder(newOrder);
    }
  };

  // Get ordered cards
  const orderedCards = useMemo(() => {
    if (!isInitialized || cardOrder.length === 0) {
      // If no saved order exists, show default cards
      return defaultCards;
    }
    
    const ordered = cardOrder
      .map(id => defaultCards.find(card => card.id === id))
      .filter(Boolean) as ExplorerAnalyticsCardData[];
    
    // If user has customized, only show cards that were in the saved order
    // If user hasn't customized, show all default cards
    if (hasUserCustomized) {
      return ordered;
    } else {
      return defaultCards;
    }
  }, [cardOrder, defaultCards, isInitialized, hasUserCustomized]);

  const handleCardClick = (card: ExplorerAnalyticsCardData) => {
    if (onCardClick && card.data) {
      onCardClick(card.id, card.data);
    }
  };

  const handleUnifiedClick = (priority: 'high' | 'medium' | 'low', data: any[]) => {
    if (onCardClick) {
      onCardClick(`${priority}-priority`, data);
    }
  };

  const handleStatusUnifiedClick = (status: 'total' | 'completed' | 'pending' | 'onHold', data: any[]) => {
    if (onCardClick) {
      const cardType = status === 'onHold' ? 'on-hold-tasks' : `${status}-tasks`;
      onCardClick(cardType, data);
    }
  };

  const handleDueDateUnifiedClick = (dueDateType: 'overdue' | 'notYetDue', data: any[]) => {
    if (onCardClick) {
      const cardType = dueDateType === 'overdue' ? 'overdue-tasks' : 'due-tasks';
      onCardClick(cardType, data);
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <SortableContext items={cardOrder} strategy={verticalListSortingStrategy}>
        <div className="space-y-3">
          {orderedCards.map((card) => (
            <ExplorerAnalyticsCard
              key={card.id}
              card={card}
              onClick={handleCardClick}
              onUnifiedClick={handleUnifiedClick}
              onStatusUnifiedClick={handleStatusUnifiedClick}
              onDueDateUnifiedClick={handleDueDateUnifiedClick}
            />
          ))}
        </div>
      </SortableContext>
    </DndContext>
  );
};