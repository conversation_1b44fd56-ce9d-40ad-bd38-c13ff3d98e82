import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  Plus, 
  Bell, 
  Settings, 
  CheckCircle, 
  Users, 
  BarChart3,
  Smartphone,
  ArrowRight,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  action?: () => void;
  completed?: boolean;
  optional?: boolean;
}

interface ProgressiveOnboardingProps {
  onComplete: () => void;
  onSkip: () => void;
}

export const ProgressiveOnboarding: React.FC<ProgressiveOnboardingProps> = ({
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [isVisible, setIsVisible] = useState(true);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Task Manager',
      description: 'Your intelligent task management companion. Let\'s get you started with a quick tour.',
      icon: Calendar,
      completed: true
    },
    {
      id: 'create-task',
      title: 'Create Your First Task',
      description: 'Click the "+" button to create a new task. Tasks can include descriptions, priorities, and due dates.',
      icon: Plus,
      action: () => {
        // Trigger task creation modal
        document.querySelector('[data-onboarding="create-task"]')?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
        setCompletedSteps(prev => new Set([...prev, 'create-task']));
      }
    },
    {
      id: 'calendar-view',
      title: 'Explore Calendar Views',
      description: 'Switch between yearly and monthly calendar views to see your tasks organized by date.',
      icon: Calendar,
      action: () => {
        document.querySelector('[data-onboarding="calendar-toggle"]')?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
        setCompletedSteps(prev => new Set([...prev, 'calendar-view']));
      }
    },
    {
      id: 'notifications',
      title: 'Enable Notifications',
      description: 'Stay updated with task reminders and notifications.',
      icon: Bell,
      action: () => {
        // Request notification permission
        if ('Notification' in window) {
          Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
              setCompletedSteps(prev => new Set([...prev, 'notifications']));
            }
          });
        }
      },
      optional: true
    },
    {
      id: 'collaboration',
      title: 'Invite Team Members',
      description: 'Assign tasks to team members and collaborate in real-time.',
      icon: Users,
      action: () => {
        setCompletedSteps(prev => new Set([...prev, 'collaboration']));
      },
      optional: true
    },
    {
      id: 'analytics',
      title: 'Track Your Progress',
      description: 'View analytics to understand your productivity patterns and task completion rates.',
      icon: BarChart3,
      action: () => {
        document.querySelector('[data-onboarding="analytics"]')?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
        setCompletedSteps(prev => new Set([...prev, 'analytics']));
      }
    },
    {
      id: 'mobile-app',
      title: 'Install Mobile App',
      description: 'Access your tasks on the go with our progressive web app. Add to home screen for the best experience.',
      icon: Smartphone,
      action: () => {
        // Trigger PWA install prompt if available
        if ('serviceWorker' in navigator) {
          setCompletedSteps(prev => new Set([...prev, 'mobile-app']));
        }
      },
      optional: true
    }
  ];

  const progress = (completedSteps.size / steps.length) * 100;
  const requiredStepsCompleted = steps.filter(step => !step.optional && completedSteps.has(step.id)).length;
  const requiredStepsTotal = steps.filter(step => !step.optional).length;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepAction = () => {
    const step = steps[currentStep];
    if (step.action) {
      step.action();
    }
  };

  const canProceed = completedSteps.has(steps[currentStep].id) || steps[currentStep].optional;

  useEffect(() => {
    // Auto-advance for welcome step
    if (currentStep === 0) {
      const timer = setTimeout(() => {
        setCompletedSteps(prev => new Set([...prev, 'welcome']));
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [currentStep]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Getting Started</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>Step {currentStep + 1} of {steps.length}</span>
              <Badge variant="outline">
                {requiredStepsCompleted}/{requiredStepsTotal} required
              </Badge>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              {React.createElement(steps[currentStep].icon, { className: "h-8 w-8 text-primary" })}
            </div>
            
            <div className="space-y-2">
              <h3 className="text-xl font-semibold">{steps[currentStep].title}</h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                {steps[currentStep].description}
              </p>
            </div>

            {steps[currentStep].optional && (
              <Badge variant="secondary" className="text-xs">
                Optional
              </Badge>
            )}
          </div>

          <div className="space-y-3">
            {steps[currentStep].action && !completedSteps.has(steps[currentStep].id) && (
              <Button
                onClick={handleStepAction}
                className="w-full"
                size="lg"
              >
                {steps[currentStep].id === 'create-task' && 'Try Creating a Task'}
                {steps[currentStep].id === 'calendar-view' && 'Explore Calendar Views'}
                {steps[currentStep].id === 'notifications' && 'Enable Notifications'}
                {steps[currentStep].id === 'collaboration' && 'Learn About Collaboration'}
                {steps[currentStep].id === 'analytics' && 'View Analytics'}
                {steps[currentStep].id === 'mobile-app' && 'Add to Home Screen'}
              </Button>
            )}

            {completedSteps.has(steps[currentStep].id) && (
              <div className="flex items-center justify-center gap-2 text-green-600 dark:text-green-400">
                <CheckCircle className="h-5 w-5" />
                <span className="text-sm font-medium">Completed!</span>
              </div>
            )}

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className="flex-1"
              >
                Previous
              </Button>
              
              <Button
                onClick={handleNext}
                disabled={!canProceed && !steps[currentStep].optional}
                className="flex-1"
              >
                {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </div>

            <Button
              variant="ghost"
              onClick={onSkip}
              className="w-full text-sm"
            >
              Skip Tutorial
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};