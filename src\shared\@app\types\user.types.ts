// User-related types
export interface UserProfile {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  avatar_url?: string;

  notification_preferences: {
    email: boolean;
    push: boolean;

    reminder: boolean;
  };
  timezone: string;
  work_hours: {
    start: string;
    end: string;
    days: number[];
  };
  theme_preferences?: {
    theme: 'light' | 'dark' | 'system';
    color_scheme: string;
    font_size: 'small' | 'medium' | 'large';
    reduced_motion: boolean;
    high_contrast: boolean;
  };
  accessibility_preferences?: {
    screen_reader: boolean;
    keyboard_navigation: boolean;
    focus_indicators: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface UserPresence {
  user_id: string;
  status: 'online' | 'offline' | 'away';
  last_seen: string;
}

export interface ProfileData {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
}