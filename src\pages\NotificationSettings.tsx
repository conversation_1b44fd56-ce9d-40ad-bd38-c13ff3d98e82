import { useState, useEffect } from 'react';
import { Bell, Mail, Smartphone, MessageSquare, Clock, Moon, Calendar, Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { unifiedNotificationService, type NotificationSettings } from '@/services/unifiedNotificationService';

export default function NotificationSettings() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [permissionGranted, setPermissionGranted] = useState(false);

  useEffect(() => {
    if (user) {
      loadSettings();
      checkNotificationPermission();
    }
  }, [user]);

  const loadSettings = async () => {
    try {
      await unifiedNotificationService.initialize(user!.id);
      const currentSettings = unifiedNotificationService.getSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error('Failed to load notification settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load notification settings.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const checkNotificationPermission = () => {
    if ('Notification' in window) {
      setPermissionGranted(Notification.permission === 'granted');
    }
  };

  const requestNotificationPermission = async () => {
    const granted = await unifiedNotificationService.requestNotificationPermission();
    setPermissionGranted(granted);
    
    if (granted) {
      toast({
        title: 'Permission Granted',
        description: 'You will now receive browser notifications.',
      });
    } else {
      toast({
        title: 'Permission Denied',
        description: 'Browser notifications are disabled. You can enable them in your browser settings.',
        variant: 'destructive',
      });
    }
  };

  const updateSettings = async (newSettings: Partial<NotificationSettings>) => {
    if (!user || !settings) return;

    setSaving(true);
    try {
      await unifiedNotificationService.updateSettings(user.id, newSettings);
      setSettings({ ...settings, ...newSettings });
      
      toast({
        title: 'Settings Updated',
        description: 'Your notification preferences have been saved.',
      });
    } catch (error) {
      console.error('Failed to update settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update notification settings.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handlePreferenceChange = (key: keyof NotificationSettings['preferences'], value: boolean) => {
    if (!settings) return;
    
    updateSettings({
      preferences: {
        ...settings.preferences,
        [key]: value,
      },
    });
  };

  const handleSettingChange = (key: keyof NotificationSettings, value: any) => {
    if (!settings) return;
    updateSettings({ [key]: value });
  };

  const handleQuietHoursChange = (key: keyof NotificationSettings['quietHours'], value: any) => {
    if (!settings) return;
    
    updateSettings({
      quietHours: {
        ...settings.quietHours,
        [key]: value,
      },
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded w-1/3 animate-pulse" />
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-1/2 animate-pulse" />
                  <div className="h-4 bg-muted rounded w-3/4 animate-pulse" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[1, 2, 3].map((j) => (
                      <div key={j} className="h-10 bg-muted rounded animate-pulse" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive">Error Loading Settings</h1>
          <p className="text-muted-foreground mt-2">Failed to load notification settings.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Notification Settings</h1>
        <p className="text-muted-foreground">
          Customize how and when you receive notifications about your tasks.
        </p>
      </div>

      {/* Master Enable/Disable */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Master Notification Control
          </CardTitle>
          <CardDescription>
            Turn all notifications on or off. When disabled, you won't receive any notifications.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Switch
              id="enabled"
              checked={settings.enabled}
              onCheckedChange={(checked) => handleSettingChange('enabled', checked)}
              disabled={saving}
            />
            <Label htmlFor="enabled" className="text-sm font-medium">
              Enable all notifications
            </Label>
          </div>
        </CardContent>
      </Card>

      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Notification Channels
          </CardTitle>
          <CardDescription>
            Choose how you want to receive notifications.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bell className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label className="text-sm font-medium">In-App Notifications</Label>
                <p className="text-xs text-muted-foreground">Notifications shown within the app</p>
              </div>
            </div>
            <Switch
              checked={settings.preferences.inApp}
              onCheckedChange={(checked) => handlePreferenceChange('inApp', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Smartphone className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label className="text-sm font-medium">Browser Push Notifications</Label>
                <p className="text-xs text-muted-foreground">
                  Desktop notifications even when app is closed
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!permissionGranted && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={requestNotificationPermission}
                  disabled={saving}
                >
                  Enable
                </Button>
              )}
              <Switch
                checked={settings.preferences.push && permissionGranted}
                onCheckedChange={(checked) => handlePreferenceChange('push', checked)}
                disabled={!settings.enabled || !permissionGranted || saving}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label className="text-sm font-medium">Email Notifications</Label>
                <p className="text-xs text-muted-foreground">Notifications sent to your email</p>
              </div>
            </div>
            <Switch
              checked={settings.preferences.email}
              onCheckedChange={(checked) => handlePreferenceChange('email', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label className="text-sm font-medium">SMS Notifications</Label>
                <p className="text-xs text-muted-foreground">Text message notifications (premium feature)</p>
              </div>
            </div>
            <Switch
              checked={settings.preferences.sms}
              onCheckedChange={(checked) => handlePreferenceChange('sms', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Task Notifications */}
      <Card>
        <CardHeader>
          <CardTitle>Task Notifications</CardTitle>
          <CardDescription>
            Configure when you receive notifications about your tasks.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Task Reminders</Label>
              <p className="text-xs text-muted-foreground">Get reminded before tasks are due</p>
            </div>
            <Switch
              checked={settings.taskReminders}
              onCheckedChange={(checked) => handleSettingChange('taskReminders', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>

          {settings.taskReminders && (
            <div className="ml-6 space-y-2">
              <Label className="text-sm">Reminder Time</Label>
              <Select
                value={settings.reminderTime.toString()}
                onValueChange={(value) => handleSettingChange('reminderTime', parseInt(value))}
                disabled={!settings.enabled || saving}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 minutes before</SelectItem>
                  <SelectItem value="15">15 minutes before</SelectItem>
                  <SelectItem value="30">30 minutes before</SelectItem>
                  <SelectItem value="60">1 hour before</SelectItem>
                  <SelectItem value="120">2 hours before</SelectItem>
                  <SelectItem value="1440">1 day before</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Overdue Alerts</Label>
              <p className="text-xs text-muted-foreground">Get alerted when tasks become overdue</p>
            </div>
            <Switch
              checked={settings.overdueAlerts}
              onCheckedChange={(checked) => handleSettingChange('overdueAlerts', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>



          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Completion Notifications</Label>
              <p className="text-xs text-muted-foreground">Get notified when assigned tasks are completed</p>
            </div>
            <Switch
              checked={settings.completionNotifications}
              onCheckedChange={(checked) => handleSettingChange('completionNotifications', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Digest & Reports */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Digest & Reports
          </CardTitle>
          <CardDescription>
            Receive periodic summaries of your task activity.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Daily Digest</Label>
              <p className="text-xs text-muted-foreground">Daily summary of your tasks</p>
            </div>
            <Switch
              checked={settings.dailyDigest}
              onCheckedChange={(checked) => handleSettingChange('dailyDigest', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>

          {settings.dailyDigest && (
            <div className="ml-6 space-y-2">
              <Label className="text-sm">Digest Time</Label>
              <Input
                type="time"
                value={settings.digestTime}
                onChange={(e) => handleSettingChange('digestTime', e.target.value)}
                className="w-[150px]"
                disabled={!settings.enabled || saving}
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Weekly Report</Label>
              <p className="text-xs text-muted-foreground">Weekly analysis of your productivity</p>
            </div>
            <Switch
              checked={settings.weeklyReport}
              onCheckedChange={(checked) => handleSettingChange('weeklyReport', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>

          {settings.weeklyReport && (
            <div className="ml-6 space-y-2">
              <Label className="text-sm">Report Day</Label>
              <Select
                value={settings.weeklyReportDay.toString()}
                onValueChange={(value) => handleSettingChange('weeklyReportDay', parseInt(value))}
                disabled={!settings.enabled || saving}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Sunday</SelectItem>
                  <SelectItem value="1">Monday</SelectItem>
                  <SelectItem value="2">Tuesday</SelectItem>
                  <SelectItem value="3">Wednesday</SelectItem>
                  <SelectItem value="4">Thursday</SelectItem>
                  <SelectItem value="5">Friday</SelectItem>
                  <SelectItem value="6">Saturday</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">System Updates</Label>
              <p className="text-xs text-muted-foreground">Notifications about app updates and maintenance</p>
            </div>
            <Switch
              checked={settings.systemUpdates}
              onCheckedChange={(checked) => handleSettingChange('systemUpdates', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Moon className="h-5 w-5" />
            Quiet Hours
          </CardTitle>
          <CardDescription>
            Set times when you don't want to receive notifications.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Enable Quiet Hours</Label>
              <p className="text-xs text-muted-foreground">Reduce notification frequency during specified hours</p>
            </div>
            <Switch
              checked={settings.quietHours.enabled}
              onCheckedChange={(checked) => handleQuietHoursChange('enabled', checked)}
              disabled={!settings.enabled || saving}
            />
          </div>

          {settings.quietHours.enabled && (
            <div className="ml-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm">Start Time</Label>
                  <Input
                    type="time"
                    value={settings.quietHours.start}
                    onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                    disabled={!settings.enabled || saving}
                  />
                </div>
                <div>
                  <Label className="text-sm">End Time</Label>
                  <Input
                    type="time"
                    value={settings.quietHours.end}
                    onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                    disabled={!settings.enabled || saving}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Save Status */}
      {saving && (
        <div className="text-center text-sm text-muted-foreground">
          Saving changes...
        </div>
      )}
    </div>
  );
}