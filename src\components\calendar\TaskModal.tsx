import React, { useState, useEffect, useRef } from 'react';
import { openDB } from 'idb';
import { format } from 'date-fns';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input'
import { VoiceToText } from '@/components/ui/voice-to-text'
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';

import { Task, FileReference } from '@/types/task';
// Assignment-related imports removed as part of Friends/Teams removal

import { useScreenSize } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { SupabaseFileAttachment } from '@/components/ui/supabase-file-attachment';

import { VoiceNoteService, FileAttachmentService } from '@/services/taskService';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useSupabaseTasks } from '@/hooks/useSupabaseTasks';
import {
  Calendar,
  CalendarIcon,
  Clock,
  CheckCircle,
  AlertCircle,
  Pause,
  Play,
  Trash2,
  Edit,
  X,
  Plus,
  Mic,
  Square,
  ExternalLink,
  Paperclip,
  Save,
  MessageSquare,
  Link,
  Users
} from 'lucide-react';

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (taskData: Partial<Task>) => void;
  selectedDate: Date | null;
  editingTask?: Task | null;
  tasks: Task[];
  onEditTask: (task: Task) => void;
  onDeleteTask: (taskId: string) => void;
  onDeleteAttachment: (taskId: string, attachmentIndex: number) => void;
  onUpdateTaskStatus: (taskId: string, status: 'pending' | 'completed' | 'cancelled' | 'on-hold') => void;
  pendingVoiceNote?: string | null;
}

export const TaskModal: React.FC<TaskModalProps> = ({
  isOpen,
  onClose,
  onSave,
  selectedDate,
  editingTask,
  tasks,
  onEditTask,
  onDeleteTask,
  onDeleteAttachment,
  onUpdateTaskStatus,
  pendingVoiceNote,
}) => {
  const { isMobile, isTablet } = useScreenSize();
  const { user } = useAuth();
  const { toast } = useToast();
  const { uploadFile } = useSupabaseTasks();
  const [showAddForm, setShowAddForm] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [status, setStatus] = useState<'pending' | 'completed' | 'cancelled' | 'on-hold'>('pending');
  const [taskDate, setTaskDate] = useState<Date | null>(null);
  const [attachments, setAttachments] = useState<Array<{name: string; size: number; type: string; file?: File; url?: string | FileReference}>>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [category, setCategory] = useState('General');
  const [customCategories, setCustomCategories] = useState<string[]>(['General', 'Work', 'Personal', 'Health', 'Finance', 'Education']);

  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editCategoryValue, setEditCategoryValue] = useState('');
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  const [playingVoiceNote, setPlayingVoiceNote] = useState<string | null>(null);
  const [dueDate, setDueDate] = useState<Date | null>(null);
  const [voiceNotes, setVoiceNotes] = useState<string[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [playingIndex, setPlayingIndex] = useState<number | null>(null);
  const [isDueDatePickerOpen, setIsDueDatePickerOpen] = useState(false);
  const [isTaskDatePickerOpen, setIsTaskDatePickerOpen] = useState(false);
  const [isRecurringEndDatePickerOpen, setIsRecurringEndDatePickerOpen] = useState(false);
  const [showVoiceToText, setShowVoiceToText] = useState(false);
  const [voiceToTextField, setVoiceToTextField] = useState<'title' | 'description' | null>(null);
  const [recurringPattern, setRecurringPattern] = useState<'none' | 'daily' | 'weekly' | 'monthly' | 'yearly'>('none');
  const [recurringEndDate, setRecurringEndDate] = useState<Date | null>(null);
  // Assignment-related state variables removed as part of Friends/Teams removal
  const [recurringFrequency, setRecurringFrequency] = useState(1);
  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<number[]>([]);
  const [selectedDayOfMonth, setSelectedDayOfMonth] = useState(1);
  const [selectedMonths, setSelectedMonths] = useState<number[]>([]);


  const audioElementsRef = useRef<{ [key: string]: HTMLAudioElement }>({});
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to extract filename from URL
  const extractFilenameFromUrl = (url: string): string => {
    try {
      // Extract filename from storage path in the URL
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      // Look for the filename part after the bucket name
      const filename = pathParts[pathParts.length - 1];
      // Remove any query parameters and decode
      return decodeURIComponent(filename.split('?')[0]) || 'Attachment';
    } catch (error) {
      console.error('Error extracting filename from URL:', error);
      return 'Attachment';
    }
  };

  // Priority colors
  const priorityColors = {
    high: 'bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700',
    medium: 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700',
    low: 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700',
  };

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setTitle('');
      setDescription('');
      setPriority('medium');
      setStatus('pending');
      setTaskDate(null);
      setDueDate(null);
      setAttachments([]);
      setVoiceNotes([]);
      setCategory('General');
      setTags([]);
      setTagInput('');
      setShowAdvanced(false);
      setShowAddForm(false);
      setPlayingIndex(null);
      setIsRecording(false);
      setRecordingTime(0);
      setDateError('');
      setDueDateError('');
      setRecurringPattern('none');
      setRecurringEndDate(null);
      setRecurringFrequency(1);
      setSelectedDaysOfWeek([]);
      setSelectedDayOfMonth(1);
      setSelectedMonths([]);
    }
  }, [isOpen]);

  // Initialize form data and view state (runs after reset)
  useEffect(() => {
    if (isOpen) { // Only run when modal is open
      if (editingTask) {
        setTitle(editingTask.title);
        setDescription(editingTask.description || '');
        setPriority(editingTask.priority);
        setStatus(editingTask.status);
        setTaskDate(new Date(editingTask.date));
        setDueDate(editingTask.dueDate ? new Date(editingTask.dueDate) : null);
        // Always fetch attachments from file_attachments table for existing tasks
        if (user) {
          const fetchAttachmentMetadata = async () => {
            try {
              const { data: attachmentData } = await supabase
                .from('file_attachments')
                .select('filename, file_type, file_size, storage_path')
                .eq('task_id', editingTask.id)
                .eq('user_id', user.id);
              
              if (attachmentData && attachmentData.length > 0) {
                console.log('DEBUG: Found attachments in database for task', editingTask.id, ':', attachmentData);
                // Use the signed URLs from the task if available, otherwise use storage path
                const attachmentsWithMetadata = attachmentData.map((att, index) => ({
                  name: att.filename,
                  type: att.file_type,
                  size: att.file_size,
                  url: editingTask.attachments?.[index] || att.storage_path
                }));
                console.log('DEBUG: Setting attachments to:', attachmentsWithMetadata);
                setAttachments(attachmentsWithMetadata);
              } else {
                setAttachments([]);
              }
            } catch (error) {
              console.error('Error fetching attachment metadata:', error);
              setAttachments([]);
            }
          };
          fetchAttachmentMetadata();
        } else {
          setAttachments([]);
        }
        setVoiceNotes(editingTask.voiceNotes || []);
        setCategory(editingTask.category || 'General');
        setTags(editingTask.tags || []);
        setTagInput((editingTask.tags || []).join(', '));
    
    
        // Initialize recurring pattern
        setRecurringPattern(editingTask.recurringPattern || 'none');
        setRecurringEndDate(editingTask.recurringEndDate ? new Date(editingTask.recurringEndDate) : null);
        
        // Assignment-related initialization removed
        
        setShowAddForm(true);
      } else if (selectedDate) {
        // For new tasks with pre-selected date
        setTaskDate(selectedDate);
        setShowAddForm(true);
      } else {
        // For new tasks without pre-selected date
        setShowAddForm(true);
      }

      if (pendingVoiceNote) {
        setVoiceNotes([pendingVoiceNote]);
      }
    }
  }, [isOpen, editingTask, selectedDate, pendingVoiceNote]);

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const selectedDateTasks = selectedDate ? tasks.filter(task => task.date === format(selectedDate, 'yyyy-MM-dd')) : [];
  const isPastDate = selectedDate ? selectedDate < today : false;

  // Validation states
  const [dateError, setDateError] = useState('');
  const [dueDateError, setDueDateError] = useState('');

  // Validation functions - immediate field-level validation
  const validateDates = () => {
    let hasErrors = false;

    // Check if task date is in the past (for new tasks only)
    // This validation is skipped for editing existing tasks (including past tasks)
    if (!editingTask && taskDate && taskDate < today) {
      setDateError('Cannot create tasks for past dates');
      hasErrors = true;
    } else {
      setDateError('');
    }

    // Check if due date is before task date (applies to ALL tasks - new, current, future, and past)
    // Due date should never be before task date regardless of when the task is
    if (taskDate && dueDate && dueDate < taskDate) {
      setDueDateError('Due date cannot be before task date');
      hasErrors = true;
    } else {
      setDueDateError('');
    }

    return !hasErrors;
  };

  // Helper functions for attachments

  const addAttachmentField = () => setAttachments([...attachments, { name: '', size: 0, type: 'application/octet-stream' }]);
  const removeAttachmentField = async (index: number) => {
    if (editingTask?.id && user) {
      // For existing tasks, delete from database
      const attachment = attachments[index];
      if (attachment && attachment.name) {
        try {
          await FileAttachmentService.deleteTaskAttachment(editingTask.id, attachment.name, user.id);
          toast({
            title: 'Attachment Removed',
            description: `${attachment.name} has been removed successfully.`,
          });
        } catch (error) {
          console.error('Error removing attachment:', error);
          toast({
            title: 'Error',
            description: 'Failed to remove attachment. Please try again.',
            variant: 'destructive',
          });
          return; // Don't remove from UI if database deletion failed
        }
      }
    }
    // Remove from UI state
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  // Category management functions

  const removeCustomCategory = (categoryToRemove: string) => {
    if (categoryToRemove === 'General') return; // Don't allow removing General
    const updatedCategories = customCategories.filter(cat => cat !== categoryToRemove);
    setCustomCategories(updatedCategories);
    if (category === categoryToRemove) {
      setCategory('General');
    }
    localStorage.setItem('taskCategories', JSON.stringify(updatedCategories));
  };

  const editCustomCategory = (oldCategory: string, newCategory: string) => {
    if (oldCategory === 'General') return; // Don't allow editing General
    if (!newCategory.trim() || customCategories.includes(newCategory.trim())) return;
    
    const updatedCategories = customCategories.map(cat => 
      cat === oldCategory ? newCategory.trim() : cat
    );
    setCustomCategories(updatedCategories);
    
    if (category === oldCategory) {
      setCategory(newCategory.trim());
    }
    
    localStorage.setItem('taskCategories', JSON.stringify(updatedCategories));
    setEditingCategory(null);
    setEditCategoryValue('');
  };

  const startEditingCategory = (categoryToEdit: string) => {
    setEditingCategory(categoryToEdit);
    setEditCategoryValue(categoryToEdit);
  };

  const cancelEditingCategory = () => {
    setEditingCategory(null);
    setEditCategoryValue('');
  };

  const addCustomCategory = () => {
    if (!newCategory.trim() || customCategories.includes(newCategory.trim())) {
      setNewCategory('');
      setShowAddCategory(false);
      return;
    }
    
    const updatedCategories = [...customCategories, newCategory.trim()];
    setCustomCategories(updatedCategories);
    setCategory(newCategory.trim());
    localStorage.setItem('taskCategories', JSON.stringify(updatedCategories));
    setNewCategory('');
    setShowAddCategory(false);
  };

  // Assignment-related functionality removed as part of Friends/Teams removal

  // Load custom categories from localStorage on component mount
  useEffect(() => {
    const savedCategories = localStorage.getItem('taskCategories');
    if (savedCategories) {
      try {
        const parsed = JSON.parse(savedCategories);
        if (Array.isArray(parsed) && parsed.length > 0) {
          setCustomCategories(parsed);
        }
      } catch (error) {
        console.error('Error loading saved categories:', error);
      }
    }
  }, []);

  const handleAttachmentsChange = (newAttachments: Array<{name: string; size: number; type: string; file?: File; url?: string | FileReference}>) => {
    setAttachments(newAttachments);
  };

  const handleOpenFile = async (fileRef: { key?: string; name: string }) => {
    try {
      const db = await openDB('TaskAttachments', 1);
      const file = await db.get('attachments', fileRef.key || fileRef.name);
      if (file) {
        const url = URL.createObjectURL(file as Blob);
        window.open(url, '_blank');
        // URL will be automatically revoked when the window closes
      }
    } catch (error) {
      console.error('Error opening file:', error);
    }
  };

  // Handle voice note deletion
  const handleDeleteVoiceNote = async (voiceNoteIndex: number) => {
    if (!editingTask || !user?.id) return;

    const voiceNoteToDelete = voiceNotes[voiceNoteIndex];
    if (!voiceNoteToDelete) return;

    try {
      // Delete from Supabase if it's a stored voice note (not a blob URL)
      if (voiceNoteToDelete.startsWith('http')) {
        await VoiceNoteService.deleteVoiceNote(
          voiceNoteToDelete,
          editingTask.id,
          user.id,
          supabase
        );
      }

      // Remove from local state
      const updatedVoiceNotes = [...voiceNotes];
      updatedVoiceNotes.splice(voiceNoteIndex, 1);
      setVoiceNotes(updatedVoiceNotes);

      toast({
        title: 'Voice Note Deleted',
        description: 'Voice note has been removed successfully',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting voice note:', error);
      toast({
        title: 'Delete Error',
        description: 'Failed to delete voice note',
        variant: 'destructive',
        duration: 3000,
      });
    }
  };

  // Handle voice note playback
  const handlePlayVoiceNote = (voiceNoteIndex: number) => {
    const voiceNoteUrl = voiceNotes[voiceNoteIndex];
    if (!voiceNoteUrl) return;

    // Stop any currently playing voice note
    if (playingIndex !== null && playingIndex !== voiceNoteIndex) {
      const currentAudio = audioElementsRef.current[playingIndex];
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
    }

    // Toggle playback for the selected voice note
    if (playingIndex === voiceNoteIndex) {
      // Currently playing, pause it
      const audio = audioElementsRef.current[voiceNoteIndex];
      if (audio) {
        audio.pause();
        setPlayingIndex(null);
      }
    } else {
      // Start playing
      if (!audioElementsRef.current[voiceNoteIndex]) {
        const audio = new Audio(voiceNoteUrl);
        audio.onended = () => setPlayingIndex(null);
        audio.onerror = () => {
          console.error('Error playing voice note');
          setPlayingIndex(null);
        };
        audioElementsRef.current[voiceNoteIndex] = audio;
      }
      
      const audio = audioElementsRef.current[voiceNoteIndex];
      audio.play();
      setPlayingIndex(voiceNoteIndex);
    }
  };

  const handleVoiceToText = (field: 'title' | 'description') => {
    setVoiceToTextField(field);
    setShowVoiceToText(true);
  };

  const handleVoiceTextGenerated = (text: string) => {
    const cleanedText = text.trim();
    if (voiceToTextField === 'title') {
      setTitle(cleanedText);
    } else if (voiceToTextField === 'description') {
      setDescription(cleanedText);
    }
    setShowVoiceToText(false);
    setVoiceToTextField(null);
  };

  const handleVoiceToTextClose = () => {
    setShowVoiceToText(false);
    setVoiceToTextField(null);
  };

  const startRecording = async () => {
    try {
      setIsRecording(true);
      setRecordingTime(0);
      
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setVoiceNotes([...voiceNotes, audioUrl]);
        
        // Clean up stream
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorder.start();
      
      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      toast({
        title: 'Recording Error',
        description: 'Failed to start voice recording. Please check microphone permissions.',
        variant: 'destructive',
        duration: 3000,
      });
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }
    
    if (recordingIntervalRef.current) {
      clearInterval(recordingIntervalRef.current);
      recordingIntervalRef.current = null;
    }
    
    setIsRecording(false);
    setRecordingTime(0);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          "max-w-4xl max-h-[95vh] overflow-y-auto p-3",
          isMobile && "max-w-[98vw] max-h-[98vh] p-2",
          isTablet && "max-w-[95vw] max-h-[96vh] p-3"
        )}
      >
        <DialogHeader className="pb-2">
          <DialogTitle className="flex items-center gap-2 text-lg">
            <Calendar className="h-4 w-4" />
            {editingTask ? 'Edit Task' : 'Add New Task'}
          </DialogTitle>
          <DialogDescription>
            {editingTask ? 'Modify task details and settings' : 'Create a new task with details, attachments, and scheduling options'}
          </DialogDescription>
        </DialogHeader>

        {/* Show add/edit task form */}
        <div className="space-y-3">
          {/* Date Selection Row - Compact */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 p-2 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
              {/* Task Date */}
              <div className="space-y-2">
                <Label htmlFor="taskDate" className="text-sm font-semibold text-blue-700 dark:text-blue-300">
                  Task Date *
                </Label>
                <Popover open={isTaskDatePickerOpen} onOpenChange={setIsTaskDatePickerOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal bg-white dark:bg-gray-900 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-950/50",
                        !taskDate && "text-muted-foreground",
                        dateError && "border-red-300 dark:border-red-700"
                      )}
                      disabled={Boolean(selectedDate && !editingTask)}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {taskDate ? format(taskDate, "PPP") : <span>Pick a task date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={taskDate}
                      onSelect={(date) => {
                        if (date) {
                          setTaskDate(date);
                          setIsTaskDatePickerOpen(false);
                          // Immediate validation for past dates
                          if (!editingTask && date < today) {
                            setDateError('Cannot create tasks for past dates');
                          } else {
                            setDateError('');
                          }
                          // Validate due date if exists
                          if (dueDate && date && dueDate < date) {
                            setDueDateError('Due date cannot be before task date');
                          } else {
                            setDueDateError('');
                          }
                        }
                      }}
                      disabled={(date) => !editingTask && date < today}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {selectedDate && !editingTask && (
                  <p className="text-xs text-blue-600 dark:text-blue-400 flex items-center gap-1">
                    <CalendarIcon className="h-3 w-3" />
                    Date pre-selected from calendar tile
                  </p>
                )}
                {dateError && (
                  <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {dateError}
                  </p>
                )}
              </div>

              {/* Due Date */}
              <div className="space-y-2">
                <Label htmlFor="dueDate" className="text-sm font-semibold text-blue-700 dark:text-blue-300">Due Date</Label>
                <div className="flex gap-2">
                  <Popover open={isDueDatePickerOpen} onOpenChange={setIsDueDatePickerOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "flex-1 justify-start text-left font-normal bg-white dark:bg-gray-900 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-950/50",
                          !dueDate && "text-muted-foreground",
                          dueDateError && "border-red-300 dark:border-red-700"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dueDate ? format(dueDate, "PPP") : <span>Pick a due date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={dueDate}
                        onSelect={(date) => {
                          setDueDate(date);
                          if (date) {
                            setIsDueDatePickerOpen(false);
                          }
                          if (taskDate && date && date < taskDate) {
                            setDueDateError('Due date cannot be before task date');
                          } else {
                            setDueDateError('');
                          }
                        }}
                        disabled={(date) => taskDate ? date < taskDate : false}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>

                  {dueDate && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setDueDate(null);
                        setDueDateError('');
                      }}
                      className="px-3 text-gray-500 hover:text-red-600 hover:border-red-300 hover:bg-red-50 dark:hover:bg-red-900/20"
                      title="Clear due date"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                {dueDateError && (
                  <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {dueDateError}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Task Title Section */}
          <div className="space-y-2">
            <Label className="text-sm font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Task Title
            </Label>
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
              <div className="flex items-center p-2">
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Type a task..."
                className="flex-[0.99] border-none focus:ring-0 text-base bg-transparent focus:outline-none px-2 py-1"
                style={{ caretColor: 'auto' }}
                autoFocus
              />
              <div className="flex items-center gap-3 ml-2">
                <div className="relative">
                  <input
                    type="file"
                    multiple
                    accept="image/*,.pdf,.doc,.docx,.csv,.txt"
                    onChange={(e) => {
                      console.log('File input change event triggered');
                      const files = e.target.files;
                      if (files) {
                        console.log('Files selected:', files.length);
                        const newAttachments = Array.from(files).map(file => ({
                          name: file.name,
                          size: file.size,
                          type: file.type,
                          file: file
                        }));
                        
                        // Filter out duplicates based on file name and size
                        const filteredAttachments = newAttachments.filter(newAtt => 
                          !attachments.some(existingAtt => 
                            existingAtt.name === newAtt.name && existingAtt.size === newAtt.size
                          )
                        );
                        
                        console.log('Filtered attachments:', filteredAttachments.length);
                        if (filteredAttachments.length > 0) {
                          setAttachments([...attachments, ...filteredAttachments]);
                        }
                        
                        // Reset the input value to allow selecting the same file again
                        e.target.value = '';
                      }
                    }}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    id="file-input"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      console.log('File attachment button clicked');
                      const fileInput = document.getElementById('file-input') as HTMLInputElement;
                      if (fileInput) {
                        fileInput.click();
                      }
                    }}
                    className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded transition-colors relative z-10"
                    title="Attach files"
                  >
                    <Paperclip className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                  </button>
                </div>
                <button
                  type="button"
                  onClick={() => handleVoiceToText('title')}
                  className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded transition-colors"
                  title="Voice input"
                >
                  <Mic className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                </button>
              </div>
            </div>
          </div>
        </div>

          {/* Attachments Display - Single row under task title */}
          {attachments.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                  Attachments ({attachments.length})
                </span>
                {editingTask?.id && user && (
                  <button
                    type="button"
                    onClick={async () => {
                      try {
                        await FileAttachmentService.cleanupDuplicateAttachments(editingTask.id, user.id);
                        toast({
                          title: 'Cleanup Complete',
                          description: 'Duplicate attachments have been removed.',
                        });
                        // Refresh attachments display
                        const { data: attachmentData } = await supabase
                          .from('file_attachments')
                          .select('filename, file_type, file_size, storage_path')
                          .eq('task_id', editingTask.id)
                          .eq('user_id', user.id);
                        
                        if (attachmentData) {
                          const refreshedAttachments = attachmentData.map((att, index) => ({
                            name: att.filename,
                            type: att.file_type,
                            size: att.file_size,
                            url: editingTask.attachments?.[index] || att.storage_path
                          }));
                          setAttachments(refreshedAttachments);
                        }
                      } catch (error) {
                        console.error('Error cleaning up duplicates:', error);
                        toast({
                          title: 'Error',
                          description: 'Failed to cleanup duplicates. Please try again.',
                          variant: 'destructive',
                        });
                      }
                    }}
                    className="text-xs px-2 py-1 bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 rounded hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors"
                    title="Remove duplicate attachments"
                  >
                    Clean Duplicates
                  </button>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {attachments.map((attachment, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    <button
                      type="button"
                      onClick={() => {
                        if (attachment.url) {
                          // Handle both string URLs and FileReference objects
                          if (typeof attachment.url === 'string') {
                            window.open(attachment.url, '_blank');
                          } else {
                            // FileReference object - use the key as the URL
                            window.open(attachment.url.key, '_blank');
                          }
                        } else if (attachment.file) {
                          const url = URL.createObjectURL(attachment.file);
                          window.open(url, '_blank');
                        }
                      }}
                      className="hover:underline"
                    >
                      {attachment.name}
                    </button>
                    <button
                      type="button"
                      onClick={async (e) => {
                        e.stopPropagation();
                        await removeAttachmentField(index);
                      }}
                      className="ml-1 hover:bg-red-200 dark:hover:bg-red-800 rounded-full p-0.5 transition-colors"
                      title="Remove attachment"
                    >
                      <X className="h-3 w-3 text-red-500 dark:text-red-400" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Description and Recurring Tasks Panel */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Description */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 p-3 rounded-lg border border-green-200/50 dark:border-green-800/50">
              <Label htmlFor="description" className="text-sm font-semibold text-green-700 dark:text-green-300 flex items-center gap-2 mb-2">
                <Edit className="h-4 w-4" />
                Description
              </Label>
                <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Add task details..."
                className="w-full text-sm bg-white dark:bg-gray-900 border-green-200 dark:border-green-800 focus:border-green-400 dark:focus:border-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1"
                style={{ caretColor: 'auto' }}
                rows={3}
              />
            </div>

            {/* Recurring Tasks */}
            <div className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/30 dark:to-violet-950/30 p-3 rounded-lg border border-purple-200/50 dark:border-purple-800/50">
              <Label className="text-sm font-semibold text-purple-700 dark:text-purple-300 flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4" />
                Recurring Task
              </Label>
              <div className="space-y-2">
                <Select value={recurringPattern} onValueChange={(value) => setRecurringPattern(value as 'none' | 'daily' | 'weekly' | 'monthly' | 'yearly')}>
                  <SelectTrigger className="bg-white dark:bg-gray-900 border-purple-200 dark:border-purple-800">
                    <SelectValue placeholder="Select pattern" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
                
                {recurringPattern !== 'none' && (
                  <div className="space-y-3">
                    {/* Frequency */}
                    <div>
                      <Label className="text-sm text-purple-700 dark:text-purple-300">
                        Repeat every
                      </Label>
                      <div className="flex items-center gap-2">
                        <input
                          type="number"
                          min="1"
                          max="12"
                          value={recurringFrequency}
                          onChange={(e) => setRecurringFrequency(parseInt(e.target.value) || 1)}
                          className="w-16 px-2 py-1 border rounded text-center"
                        />
                        <span className="text-sm text-muted-foreground">
                          {recurringPattern === 'daily' ? 'day(s)' :
                           recurringPattern === 'weekly' ? 'week(s)' :
                           recurringPattern === 'monthly' ? 'month(s)' :
                           recurringPattern === 'yearly' ? 'year(s)' : ''}
                        </span>
                      </div>
                    </div>

                    {/* Weekly: Days of week */}
                    {recurringPattern === 'weekly' && (
                      <div>
                        <Label className="text-sm text-purple-700 dark:text-purple-300">
                          On days
                        </Label>
                        <div className="flex gap-1 mt-1">
                          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
                            <button
                              key={index}
                              type="button"
                              onClick={() => {
                                setSelectedDaysOfWeek(prev => 
                                  prev.includes(index) 
                                    ? prev.filter(d => d !== index)
                                    : [...prev, index]
                                );
                              }}
                              className={cn(
                                "w-8 h-8 rounded-full text-xs font-medium border",
                                selectedDaysOfWeek.includes(index)
                                  ? "bg-purple-500 text-white border-purple-500"
                                  : "bg-white border-gray-300 text-gray-700"
                              )}
                            >
                              {day}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Monthly: Day of month */}
                    {recurringPattern === 'monthly' && (
                      <div>
                        <Label className="text-sm text-purple-700 dark:text-purple-300">
                          On day of month
                        </Label>
                        <select
                          value={selectedDayOfMonth}
                          onChange={(e) => setSelectedDayOfMonth(parseInt(e.target.value))}
                          className="w-full px-3 py-2 border rounded"
                        >
                          {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                            <option key={day} value={day}>{day}</option>
                          ))}
                        </select>
                      </div>
                    )}

                    {/* Yearly: Months */}
                    {recurringPattern === 'yearly' && (
                      <div>
                        <Label className="text-sm text-purple-700 dark:text-purple-300">
                          In months
                        </Label>
                        <div className="grid grid-cols-3 gap-2 mt-1">
                          {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].map((month, index) => (
                            <button
                              key={index}
                              type="button"
                              onClick={() => {
                                setSelectedMonths(prev => 
                                  prev.includes(index) 
                                    ? prev.filter(m => m !== index)
                                    : [...prev, index]
                                );
                              }}
                              className={cn(
                                "px-2 py-1 rounded text-xs font-medium border",
                                selectedMonths.includes(index)
                                  ? "bg-purple-500 text-white border-purple-500"
                                  : "bg-white border-gray-300 text-gray-700"
                              )}
                            >
                              {month}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
                {recurringPattern !== 'none' && (
                  <Popover open={isRecurringEndDatePickerOpen} onOpenChange={setIsRecurringEndDatePickerOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal bg-white dark:bg-gray-900 border-purple-200 dark:border-purple-800"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {recurringEndDate ? format(recurringEndDate, "PPP") : <span>End date (optional)</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={recurringEndDate}
                        onSelect={(date) => {
                          setRecurringEndDate(date);
                          setIsRecurringEndDatePickerOpen(false);
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                )}
              </div>
            </div>
          </div>

          {/* Voice Notes Display - Single row like attachments */}
          {voiceNotes.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded-lg">
              <div className="flex flex-wrap gap-2">
                {voiceNotes.map((note, index) => (
                  <button
                    key={index}
                    type="button"
                    className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-800 transition-colors flex items-center gap-1"
                    onClick={() => handlePlayVoiceNote(index)}
                  >
                    {playingIndex === index ? (
                      <Pause className="h-3 w-3" />
                    ) : (
                      <Play className="h-3 w-3" />
                    )}
                    Voice {index + 1}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Priority, Status, Category, Tags Row - Compact */}
          <div className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/30 dark:to-amber-950/30 p-2 rounded-lg border border-orange-200/50 dark:border-orange-800/50">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
              <div className="space-y-2">
                <Label className="text-sm font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Priority
                </Label>
                <Select value={priority} onValueChange={(value) => setPriority(value as 'low' | 'medium' | 'high')}>
                  <SelectTrigger className="bg-white dark:bg-gray-900 border-orange-200 dark:border-orange-800">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">
                      <Badge variant="outline" className={priorityColors.low}>Low</Badge>
                    </SelectItem>
                    <SelectItem value="medium">
                      <Badge variant="outline" className={priorityColors.medium}>Medium</Badge>
                    </SelectItem>
                    <SelectItem value="high">
                      <Badge variant="outline" className={priorityColors.high}>High</Badge>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Status
                </Label>
                <Select value={status} onValueChange={(value) => setStatus(value as 'pending' | 'completed' | 'cancelled' | 'on-hold')}>
                  <SelectTrigger className="bg-white dark:bg-gray-900 border-orange-200 dark:border-orange-800">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="on-hold">On Hold</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-2">
                  <Paperclip className="h-4 w-4" />
                  Category
                </Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger className="bg-white dark:bg-gray-900 border-orange-200 dark:border-orange-800">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {customCategories.map((cat) => (
                      <SelectItem key={cat} value={cat} className="group p-0">
                        {editingCategory === cat ? (
                          <div className="flex items-center justify-between w-full px-2 py-1.5" onClick={(e) => e.stopPropagation()}>
                            <Input
                              value={editCategoryValue}
                              onChange={(e) => setEditCategoryValue(e.target.value)}
                              className="h-6 text-xs flex-1 min-w-0 mr-2"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  editCustomCategory(cat, editCategoryValue);
                                } else if (e.key === 'Escape') {
                                  e.preventDefault();
                                  cancelEditingCategory();
                                }
                              }}
                              onBlur={() => editCustomCategory(cat, editCategoryValue)}
                              autoFocus
                            />
                            <div className="flex items-center gap-1 flex-shrink-0">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 hover:bg-green-100 hover:text-green-600"
                                onMouseDown={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  editCustomCategory(cat, editCategoryValue);
                                }}
                              >
                                <CheckCircle className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 hover:bg-gray-100 hover:text-gray-600"
                                onMouseDown={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  cancelEditingCategory();
                                }}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between w-full px-2 py-1.5">
                            <span className="truncate pr-8">{cat}</span>
                            {cat !== 'General' && (
                              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0 ml-4">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 hover:bg-blue-100 hover:text-blue-600"
                                  onMouseDown={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    startEditingCategory(cat);
                                  }}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                                  onMouseDown={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    removeCustomCategory(cat);
                                  }}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        )}
                      </SelectItem>
                    ))}
                    
                    {/* Add new category option */}
                    {showAddCategory ? (
                      <div className="p-2 border-t">
                        <div className="flex items-center gap-2">
                          <Input
                            value={newCategory}
                            onChange={(e) => setNewCategory(e.target.value)}
                            placeholder="Enter new category"
                            className="h-8 text-sm flex-1"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                addCustomCategory();
                              } else if (e.key === 'Escape') {
                                e.preventDefault();
                                setShowAddCategory(false);
                                setNewCategory('');
                              }
                            }}
                            autoFocus
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-green-100 hover:text-green-600"
                            onClick={addCustomCategory}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-gray-100 hover:text-gray-600"
                            onClick={() => {
                              setShowAddCategory(false);
                              setNewCategory('');
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="p-2 border-t">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-full h-8 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 flex items-center gap-2"
                          onClick={() => setShowAddCategory(true)}
                        >
                          <Plus className="h-4 w-4" />
                          Add new category
                        </Button>
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Tags
                </Label>
                <Input
                  placeholder="Add tags (comma separated)"
                  value={tagInput}
                  onChange={(e) => {
                    const value = e.target.value;
                    setTagInput(value);
                    // Process tags in real-time
                    const newTags = value.split(',').map(tag => tag.trim()).filter(Boolean);
                    setTags(newTags);
                  }}
                  onBlur={() => {
                    // Clean up the input on blur
                    const cleanedTags = tagInput.split(',').map(tag => tag.trim()).filter(Boolean);
                    setTags(cleanedTags);
                    setTagInput(cleanedTags.join(', '));
                  }}
                  className="text-sm bg-white dark:bg-gray-900 border-orange-200 dark:border-orange-800 focus:border-orange-400 dark:focus:border-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1 px-2 py-1"
                  style={{ caretColor: 'auto' }}
                />
              </div>
            </div>
          </div>


          {/* Action Buttons - Modern Style */}
          <div className="flex flex-col-reverse sm:flex-row gap-3 pt-2">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1 sm:flex-none border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>

            {/* Assignment button removed as part of Friends/Teams removal */}

            <Button
              onClick={async () => {
                if (!title.trim() || !taskDate) return;

                // Validate dates before saving
                if (!validateDates()) return;

                // Process attachments - upload new files for existing tasks, pass file objects for new tasks
                const processedAttachments = [];
                
                for (const att of attachments) {
                  if (att.file && user) {
                    // New file that needs to be uploaded
                    try {
                      if (editingTask?.id) {
                        // For existing tasks, upload directly to file_attachments table
                        // Don't add to processedAttachments since we use file_attachments table as source of truth
                        await uploadFile(att.file, editingTask.id);
                      } else {
                        // For new tasks, we'll store the file object and handle upload after task creation
                        // Pass the file object to be handled by the parent component
                        processedAttachments.push(att);
                      }
                    } catch (error) {
                      console.error('Error uploading file:', error);
                      toast({
                        title: 'Upload Error',
                        description: `Failed to upload ${att.name}`,
                        variant: 'destructive',
                      });
                    }
                  } else if (!editingTask) {
                    // For new tasks, include non-file attachments (URLs, etc.)
                    if (typeof att === 'string') {
                      processedAttachments.push(att);
                    } else if (att.url && !att.file) {
                      if (typeof att.url === 'string') {
                        processedAttachments.push(att.url);
                      } else {
                        processedAttachments.push(att.url.key);
                      }
                    }
                  }
                  // For existing tasks, ignore all attachment processing since we use file_attachments table
                }

                const taskData = {
                  ...(editingTask ? { id: editingTask.id } : {}),
                  date: format(taskDate, 'yyyy-MM-dd'),
                  title: title.trim(),
                  description: description.trim(),
                  priority,
                  status,
                  dueDate: dueDate ? format(dueDate, 'yyyy-MM-dd') : undefined,
                  // Only include attachments for new tasks, existing tasks use file_attachments table
                  ...(editingTask ? {} : { attachments: processedAttachments }),
                  voiceNotes: voiceNotes.filter(note => note.trim()),
                  category,
                  tags: tags.filter(tag => tag.trim()),
            
                  recurringPattern: recurringPattern !== 'none' ? recurringPattern : undefined,
                  recurringEndDate: recurringEndDate ? format(recurringEndDate, 'yyyy-MM-dd') : undefined,
                  recurringFrequency: recurringPattern !== 'none' ? recurringFrequency : undefined,
                  selectedDaysOfWeek: recurringPattern === 'weekly' ? selectedDaysOfWeek : undefined,
                  selectedDayOfMonth: recurringPattern === 'monthly' ? selectedDayOfMonth : undefined,
                  selectedMonths: recurringPattern === 'yearly' ? selectedMonths : undefined,
                };

                console.log('Saving task:', taskData);
                onSave(taskData);
              }}
              disabled={!title.trim() || !taskDate || (!editingTask && !!dateError) || !!dueDateError}
              className="flex-1 sm:flex-none bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              {editingTask ? 'Update Task' : 'Create Task'}
            </Button>
          </div>

          {/* Keyboard Shortcuts Hint */}
          <div className="text-xs text-muted-foreground text-center pt-2 border-t border-gray-200 dark:border-gray-700">
            💡 <strong>Tip:</strong> Press <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">Ctrl+Enter</kbd> to save, <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">Esc</kbd> to close. Use <MessageSquare className="inline h-3 w-3 mx-1" /> for voice-to-text.
          </div>
        </div>
      </DialogContent>

      {/* Voice-to-Text Modal */}
      {showVoiceToText && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="max-w-2xl w-full mx-4">
            <VoiceToText
              onTextGenerated={handleVoiceTextGenerated}
              onClose={handleVoiceToTextClose}
              placeholder={`Start speaking to dictate ${voiceToTextField === 'title' ? 'task title' : 'task description'}...`}
              className="bg-white dark:bg-gray-900"
            />
          </div>
        </div>
      )}

      {/* Task Assignment Modal removed as part of Friends/Teams removal */}

    </Dialog>
  );
};
