import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Paperclip,
  Trash2,
  Upload,
  File,
  Image,
  FileText,
  Eye,
  AlertCircle,
  X,
  Camera,
  Video,
  Crown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { UnifiedFileService } from '@/services/unifiedFileService';
import { FileReference } from '@/types/unified-task';
import { useSubscriptionStore } from '@/store/subscriptionStore';

interface EnhancedFileAttachmentProps {
  attachments: FileReference[];
  onAttachmentsChange: (attachments: FileReference[]) => void;
  taskId?: string;
  userId: string;
  maxFiles?: number;
  acceptedTypes?: string[];
  disabled?: boolean;
  enableCameraCapture?: boolean;
  enableVideoCapture?: boolean;
}

export const EnhancedFileAttachment: React.FC<EnhancedFileAttachmentProps> = ({
  attachments,
  onAttachmentsChange,
  taskId,
  userId,
  maxFiles = 10,
  acceptedTypes = [],
  disabled = false,
  enableCameraCapture = true,
  enableVideoCapture = true
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [storageWarning, setStorageWarning] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const { subscription, isProUser } = useSubscriptionStore();
  const subscriptionTier = subscription?.tier || 'free';

  const handleFileUpload = async (files: FileList) => {
    if (disabled || !taskId) return;

    setError(null);
    setStorageWarning(null);
    setUploading(true);

    try {
      const newAttachments = [...attachments];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Check file count limit
        if (newAttachments.length >= maxFiles) {
          setError(`Maximum ${maxFiles} files allowed`);
          break;
        }

        // Check file type if restrictions exist
        if (acceptedTypes.length > 0 && !acceptedTypes.some(type => file.type.includes(type))) {
          setError(`File type "${file.type}" is not allowed`);
          continue;
        }

        // Check subscription limits
        const uploadCheck = await UnifiedFileService.canUploadFile(file, userId, subscriptionTier);
        if (!uploadCheck.canUpload) {
          setError(uploadCheck.reason || 'Cannot upload file');
          if (uploadCheck.reason?.includes('storage limit') && !isProUser()) {
            setStorageWarning('Upgrade to Pro for more storage space');
          }
          continue;
        }

        // Upload file
        try {
          const uploadResult = await UnifiedFileService.uploadFile(file, taskId, userId, subscriptionTier);
          
          newAttachments.push({
            key: uploadResult.id,
            name: file.name,
            type: file.type,
            size: file.size,
            uploadedAt: new Date().toISOString(),
            url: uploadResult.url
          });
        } catch (uploadError: any) {
          setError(uploadError.message);
          if (uploadError.message.includes('storage limit') && !isProUser()) {
            setStorageWarning('Upgrade to Pro for more storage space');
          }
        }
      }

      onAttachmentsChange(newAttachments);
    } catch (error: any) {
      console.error('Error handling files:', error);
      setError(error.message || 'Failed to handle files');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteAttachment = async (index: number) => {
    const attachment = attachments[index];
    
    try {
      if (attachment.key && taskId) {
        await UnifiedFileService.deleteFile(attachment.key);
      }
      
      const newAttachments = attachments.filter((_, i) => i !== index);
      onAttachmentsChange(newAttachments);
    } catch (error: any) {
      console.error('Error deleting file:', error);
      setError(error.message || 'Failed to delete file');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
    // Reset input value
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleOpenFile = (attachment: FileReference) => {
    if (attachment.url) {
      window.open(attachment.url, '_blank', 'noopener,noreferrer');
    }
  };

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) {
      return <Image className="h-4 w-4" />;
    } else if (type.startsWith('video/')) {
      return <Video className="h-4 w-4" />;
    } else if (type.includes('text') || type.includes('document') || type.includes('csv') || type.includes('spreadsheet')) {
      return <FileText className="h-4 w-4" />;
    } else {
      return <File className="h-4 w-4" />;
    }
  };

  const getFileTypeColor = (type: string) => {
    if (type.startsWith('image/')) {
      return 'bg-green-100 text-green-700 border-green-300';
    } else if (type.startsWith('video/')) {
      return 'bg-purple-100 text-purple-700 border-purple-300';
    } else if (type.includes('text') || type.includes('document') || type.includes('csv') || type.includes('spreadsheet')) {
      return 'bg-blue-100 text-blue-700 border-blue-300';
    } else if (type.includes('pdf')) {
      return 'bg-red-100 text-red-700 border-red-300';
    } else {
      return 'bg-gray-100 text-gray-700 border-gray-300';
    }
  };

  const maxFileSize = isProUser() ? 25 : 5; // MB

  return (
    <div className="space-y-3">
      <Label className="text-sm font-semibold flex items-center gap-2">
        <Paperclip className="h-4 w-4" />
        File Attachments
        {attachments.length > 0 && (
          <Badge variant="secondary" className="ml-2">
            {attachments.length}
          </Badge>
        )}
        {!isProUser() && (
          <Badge variant="outline" className="ml-2 text-xs">
            Free: {maxFileSize}MB max
          </Badge>
        )}
      </Label>

      {/* Storage Warning */}
      {storageWarning && (
        <Alert>
          <Crown className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{storageWarning}</span>
            <Button variant="outline" size="sm">
              Upgrade Now
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-3 text-center transition-colors",
          dragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary/50 hover:bg-muted/50"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => {
          if (!disabled && fileInputRef.current) {
            fileInputRef.current.click();
          }
        }}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileInputChange}
          className="hidden"
          accept={acceptedTypes.length > 0 ? acceptedTypes.join(',') : undefined}
          disabled={disabled}
        />

        {/* Hidden camera input */}
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        {/* Hidden video input */}
        <input
          ref={videoInputRef}
          type="file"
          accept="video/*"
          capture="environment"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        <div className="space-y-1">
          <Upload className={cn("h-6 w-6 mx-auto", uploading ? "animate-pulse" : "")} />
          <div>
            <p className="text-sm font-medium">
              {uploading ? 'Uploading...' : 'Drop files here or click to browse'}
            </p>
            <p className="text-xs text-muted-foreground">
              Max {maxFiles} files, {maxFileSize}MB each
              {acceptedTypes.length > 0 && ` • ${acceptedTypes.join(', ')}`}
            </p>
          </div>
        </div>
      </div>

      {/* Mobile capture buttons */}
      {(enableCameraCapture || enableVideoCapture) && (
        <div className="flex gap-2 justify-center">
          {enableCameraCapture && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => cameraInputRef.current?.click()}
              disabled={disabled || uploading}
              className="flex items-center gap-2"
            >
              <Camera className="h-4 w-4" />
              Take Photo
            </Button>
          )}
          {enableVideoCapture && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => videoInputRef.current?.click()}
              disabled={disabled || uploading}
              className="flex items-center gap-2"
            >
              <Video className="h-4 w-4" />
              Record Video
            </Button>
          )}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
            className="ml-auto h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Attached Files List */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Attached Files</Label>
          <div className="space-y-2">
            {attachments.map((attachment, index) => (
              <Card key={index} className="p-3">
                <div className="flex items-center justify-between gap-3">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className={cn(
                      "p-2 rounded-lg border",
                      getFileTypeColor(attachment.type)
                    )}>
                      {getFileIcon(attachment.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate" title={attachment.name}>
                        {attachment.name}
                      </p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{UnifiedFileService.formatFileSize(attachment.size)}</span>
                        <span>•</span>
                        <span>{attachment.type}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenFile(attachment)}
                      className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      title="Open file"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAttachment(index)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      title="Delete file"
                      disabled={disabled}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};