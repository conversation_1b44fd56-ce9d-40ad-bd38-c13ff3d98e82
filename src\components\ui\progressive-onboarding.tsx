import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronRight, ChevronLeft, Sparkles, Zap, Target, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  highlight?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  illustration?: string;
}

interface ProgressiveOnboardingProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  steps?: OnboardingStep[];
}

const defaultSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to TaskManager Pro',
    description: 'Your premium task management solution. Let\'s get you set up for maximum productivity.',
    icon: <PERSON>rk<PERSON>,
    illustration: 'photo-1581090464777-f3220bbe1b8b',
    highlight: 'Get started in less than 2 minutes!'
  },
  {
    id: 'tasks',
    title: 'Create & Organize Tasks',
    description: 'Add tasks with rich details, set priorities, and organize them with categories and tags.',
    icon: Target,
    action: {
      label: 'Try Creating a Task',
      onClick: () => console.log('Demo task creation')
    }
  },
  {
    id: 'calendar',
    title: 'Calendar Integration',
    description: 'View your tasks in a beautiful calendar interface. Schedule due dates and track your progress.',
    icon: Target,
    illustration: 'photo-1470813740244-df37b8c1edcb'
  },
  {
    id: 'collaboration',
    title: 'Team Collaboration',
    description: 'Assign tasks to team members, track progress together, and achieve goals as a team.',
    icon: Users,
    highlight: 'Perfect for teams of any size'
  },
  {
    id: 'features',
    title: 'Premium Features',
    description: 'Voice notes, file attachments, analytics, and more premium features to supercharge your productivity.',
    icon: Zap,
    illustration: 'photo-1500375592092-40eb2168fd21'
  }
];

export const ProgressiveOnboarding: React.FC<ProgressiveOnboardingProps> = ({
  isOpen,
  onClose,
  onComplete,
  steps = defaultSteps
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;

  const goToNext = () => {
    if (isLastStep) {
      onComplete();
      onClose();
    } else {
      setDirection('forward');
      setCurrentStep(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (!isFirstStep) {
      setDirection('backward');
      setCurrentStep(prev => prev - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    setDirection(stepIndex > currentStep ? 'forward' : 'backward');
    setCurrentStep(stepIndex);
  };

  const skipOnboarding = () => {
    onComplete();
    onClose();
  };

  const slideVariants = {
    enter: (direction: 'forward' | 'backward') => ({
      x: direction === 'forward' ? 300 : -300,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: 'forward' | 'backward') => ({
      x: direction === 'forward' ? -300 : 300,
      opacity: 0
    })
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={(e) => e.target === e.currentTarget && onClose()}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="w-full max-w-2xl relative"
          >
            <Card className="glass-card border-0 shadow-2xl overflow-hidden">
              {/* Header */}
              <div className="relative p-6 pb-0">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-primary/10 text-primary">
                      Step {currentStep + 1} of {steps.length}
                    </Badge>
                    {currentStepData.highlight && (
                      <Badge variant="outline" className="text-xs">
                        {currentStepData.highlight}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={skipOnboarding}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Skip tour
                    </button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={onClose}
                      className="rounded-full"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="relative w-full h-1 bg-muted rounded-full overflow-hidden">
                  <motion.div
                    className="absolute left-0 top-0 h-full bg-gradient-primary rounded-full"
                    animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                  />
                </div>
              </div>

              {/* Content */}
              <div className="relative overflow-hidden" style={{ minHeight: '400px' }}>
                <AnimatePresence mode="wait" custom={direction}>
                  <motion.div
                    key={currentStep}
                    custom={direction}
                    variants={slideVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    className="p-6 pt-4 text-center"
                  >
                    {/* Icon/Illustration */}
                    <div className="mb-8">
                      {currentStepData.illustration ? (
                        <div className="relative w-32 h-32 mx-auto mb-4">
                          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 rounded-full" />
                          <div className="absolute inset-2 bg-gradient-primary rounded-full flex items-center justify-center">
                            <currentStepData.icon className="w-12 h-12 text-white" />
                          </div>
                          {/* Floating elements */}
                          <motion.div
                            animate={{
                              y: [0, -10, 0],
                              rotate: [0, 10, 0]
                            }}
                            transition={{
                              duration: 3,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg"
                          >
                            <Sparkles className="w-3 h-3 text-yellow-800" />
                          </motion.div>
                        </div>
                      ) : (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                          className="w-20 h-20 mx-auto mb-6 bg-gradient-primary rounded-full flex items-center justify-center shadow-xl"
                        >
                          <currentStepData.icon className="w-10 h-10 text-white" />
                        </motion.div>
                      )}
                    </div>

                    {/* Title and Description */}
                    <motion.h2
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="text-2xl md:text-3xl font-bold text-foreground mb-4"
                    >
                      {currentStepData.title}
                    </motion.h2>

                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-muted-foreground text-lg leading-relaxed max-w-md mx-auto mb-8"
                    >
                      {currentStepData.description}
                    </motion.p>

                    {/* Action Button */}
                    {currentStepData.action && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.3 }}
                      >
                        <Button
                          onClick={currentStepData.action.onClick}
                          variant="outline"
                          className="mb-4"
                        >
                          {currentStepData.action.label}
                        </Button>
                      </motion.div>
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>

              {/* Footer */}
              <div className="p-6 pt-0">
                <div className="flex items-center justify-between">
                  {/* Step Indicators */}
                  <div className="flex items-center gap-2">
                    {steps.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => goToStep(index)}
                        className={cn(
                          "w-2 h-2 rounded-full transition-all duration-200",
                          index === currentStep 
                            ? "bg-primary w-6" 
                            : index < currentStep
                            ? "bg-primary/60"
                            : "bg-muted-foreground/30"
                        )}
                      />
                    ))}
                  </div>

                  {/* Navigation Buttons */}
                  <div className="flex items-center gap-2">
                    {!isFirstStep && (
                      <Button
                        variant="ghost"
                        onClick={goToPrevious}
                        className="flex items-center gap-2"
                      >
                        <ChevronLeft className="w-4 h-4" />
                        Previous
                      </Button>
                    )}
                    
                    <Button
                      onClick={goToNext}
                      variant={isLastStep ? "premium" : "default"}
                      className="flex items-center gap-2"
                    >
                      {isLastStep ? (
                        <>
                          Get Started
                          <Sparkles className="w-4 h-4" />
                        </>
                      ) : (
                        <>
                          Next
                          <ChevronRight className="w-4 h-4" />
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Contextual tooltip for feature highlights
interface FeatureTooltipProps {
  isVisible: boolean;
  title: string;
  description: string;
  position: { x: number; y: number };
  onClose: () => void;
  onNext?: () => void;
  step?: number;
  totalSteps?: number;
}

export const FeatureTooltip: React.FC<FeatureTooltipProps> = ({
  isVisible,
  title,
  description,
  position,
  onClose,
  onNext,
  step,
  totalSteps
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40"
            onClick={onClose}
          />
          
          {/* Tooltip */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 10 }}
            style={{
              position: 'fixed',
              left: position.x,
              top: position.y,
            }}
            className="z-50 w-80 glass-card rounded-lg shadow-2xl border p-4"
          >
            {/* Arrow */}
            <div className="absolute -top-2 left-6 w-4 h-4 bg-background border-l border-t rotate-45" />
            
            <div className="space-y-3">
              {step && totalSteps && (
                <Badge variant="secondary" className="text-xs">
                  {step} of {totalSteps}
                </Badge>
              )}
              
              <h4 className="font-semibold text-foreground">{title}</h4>
              <p className="text-sm text-muted-foreground">{description}</p>
              
              <div className="flex justify-between items-center pt-2">
                <button
                  onClick={onClose}
                  className="text-sm text-muted-foreground hover:text-foreground"
                >
                  Skip
                </button>
                
                {onNext && (
                  <Button size="sm" onClick={onNext}>
                    Next
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};