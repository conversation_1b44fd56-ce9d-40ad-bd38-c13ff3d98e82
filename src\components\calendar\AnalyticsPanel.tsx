import React, { useMemo } from 'react';
import { <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON><PERSON>, Clock, AlertTriangle, Target, Zap, TrendingDown, Calendar } from 'lucide-react';
import { Task } from '@/types/task';
import { Card } from '@/components/ui/card';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface AnalyticsPanelProps {
  tasks: Task[];
  onCardClick?: (cardType: string, data: any) => void;
  onDateSelect?: (date: Date) => void;
  showHeader?: boolean;
}

export const AnalyticsPanel: React.FC<AnalyticsPanelProps> = ({
  tasks,
  onCardClick,
}) => {
  // Get tasks up to today (cumulative)
  const todayTasks = useMemo(() => {
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    
    const filtered = tasks.filter(task => {
      try {
        const taskDate = new Date(task.date);
        if (isNaN(taskDate.getTime())) return false;
        
        // Use UTC dates to avoid timezone issues
        const taskUTC = Date.UTC(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate());
        const todayUTC = Date.UTC(today.getFullYear(), today.getMonth(), today.getDate());
        
        return taskUTC <= todayUTC;
      } catch (error) {
        console.error('Error processing task date:', error, task);
        return false;
      }
    });
    
    return filtered;
  }, [tasks]);



  const metrics = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return [
      {
        id: 'total',
        title: 'Total Tasks',
        value: todayTasks.length,
        icon: BarChart3,
        color: 'text-blue-600 dark:text-blue-400',
        data: todayTasks,
      },
      {
        id: 'completed',
        title: 'Completed',
        value: todayTasks.filter(t => t.status === 'completed').length,
        icon: CheckCircle,
        color: 'text-green-600 dark:text-green-400',
        data: todayTasks.filter(t => t.status === 'completed'),
      },
      {
        id: 'pending',
        title: 'Pending',
        value: todayTasks.filter(t => t.status === 'pending').length,
        icon: Clock,
        color: 'text-orange-600 dark:text-orange-400',
        data: todayTasks.filter(t => t.status === 'pending'),
      },
      {
        id: 'due-tasks',
        title: 'Due Tasks',
        value: todayTasks.filter(t => {
          if (!t.dueDate) return false;
          const dueDate = new Date(t.dueDate);
          dueDate.setHours(0, 0, 0, 0);
          return dueDate >= today && t.status !== 'completed' && t.status !== 'cancelled';
        }).length,
        icon: Calendar,
        color: 'text-blue-600 dark:text-blue-400',
        data: todayTasks.filter(t => {
          if (!t.dueDate) return false;
          const dueDate = new Date(t.dueDate);
          dueDate.setHours(0, 0, 0, 0);
          return dueDate >= today && t.status !== 'completed' && t.status !== 'cancelled';
        }),
      },
    ];
  }, [todayTasks]);

  // Priority analytics
  const priorityAnalytics = useMemo(() => {
    const high = todayTasks.filter(t => t.priority === 'high').length;
    const medium = todayTasks.filter(t => t.priority === 'medium').length;
    const low = todayTasks.filter(t => t.priority === 'low').length;
    const total = high + medium + low;

    return {
      high,
      medium,
      low,
      total,
      priorities: [
        {
          level: 'High',
          count: high,
          color: 'text-red-600 dark:text-red-400',
          icon: AlertTriangle,
          data: todayTasks.filter(t => t.priority === 'high')
        },
        {
          level: 'Medium',
          count: medium,
          color: 'text-orange-600 dark:text-orange-400',
          icon: TrendingDown,
          data: todayTasks.filter(t => t.priority === 'medium')
        },
        {
          level: 'Low',
          count: low,
          color: 'text-green-600 dark:text-green-400',
          icon: Zap,
          data: todayTasks.filter(t => t.priority === 'low')
        }
      ]
    };
  }, [todayTasks]);

  return (
    <div className="h-full overflow-x-hidden overflow-y-auto">
      <div className="p-2 space-y-2 w-full">
        {/* Status metrics */}
        {metrics.map((metric) => {
          const Icon = metric.icon;
          
          return (
            <Card
              key={metric.id}
              className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
              onClick={() => onCardClick?.(metric.id, metric.data)}
            >
              <div className="p-3">
                {/* Compact header with icon and title */}
                <div className="flex items-center gap-2 mb-2">
                  <div className={`p-1.5 rounded-md bg-gray-100 dark:bg-gray-800`}>
                    <Icon className={`h-4 w-4 ${metric.color}`} />
                  </div>
                  <h3 className="text-sm font-semibold text-foreground">
                    {metric.title}
                  </h3>
                </div>

                {/* Compact content with numbers */}
                <div className="flex justify-between items-end">
                  <div className="text-2xl font-bold text-foreground mb-0.5">
                    {metric.value}
                  </div>
                </div>
              </div>
            </Card>
          );
        })}

        {/* Merged Priority Overview Card */}
        <Card
          className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
          onClick={() => onCardClick?.('all-priorities', todayTasks)}
        >
          <div className="p-3">
            {/* Header */}
            <div className="flex items-center gap-2 mb-3">
              <div className="p-1.5 rounded-md bg-gray-100 dark:bg-gray-800">
                <Target className="h-4 w-4 text-slate-600 dark:text-slate-400" />
              </div>
              <h3 className="text-sm font-semibold text-foreground">
                Priority Overview
              </h3>
            </div>

            {/* Total count */}
            <div className="text-2xl font-bold text-foreground mb-3">
              {priorityAnalytics.total}
            </div>

            {/* Priority breakdown */}
            <div className="space-y-2">
              {priorityAnalytics.priorities.map((priority) => {
                const Icon = priority.icon;
                return (
                  <div
                    key={priority.level}
                    className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      onCardClick?.(`${priority.level.toLowerCase()}-priority`, priority.data);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <Icon className={cn("h-3 w-3", priority.color)} />
                      <span className="text-xs font-medium">{priority.level}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-bold">{priority.count}</span>
                      {priorityAnalytics.total > 0 && (
                        <span className="text-xs text-muted-foreground">
                          ({Math.round((priority.count / priorityAnalytics.total) * 100)}%)
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};