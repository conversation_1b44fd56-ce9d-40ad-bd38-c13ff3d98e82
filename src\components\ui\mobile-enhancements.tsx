import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { cn } from '@/lib/utils';
import { ChevronUp, ChevronDown, X } from 'lucide-react';

// Swipeable card component
interface SwipeableCardProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  className?: string;
  swipeThreshold?: number;
}

export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  className,
  swipeThreshold = 100
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const constraintsRef = useRef(null);

  const handleDragEnd = async (event: any, info: any) => {
    const { offset, velocity } = info;
    setIsDragging(false);

    // Determine swipe direction and trigger haptic feedback
    if (Math.abs(offset.x) > swipeThreshold || Math.abs(velocity.x) > 500) {
      if (window.navigator?.userAgent.includes('Mobile')) {
        try {
          await Haptics.impact({ style: ImpactStyle.Light });
        } catch (error) {
          // Haptics not supported
        }
      }

      if (offset.x > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (offset.x < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    }

    if (Math.abs(offset.y) > swipeThreshold || Math.abs(velocity.y) > 500) {
      if (window.navigator?.userAgent.includes('Mobile')) {
        try {
          await Haptics.impact({ style: ImpactStyle.Light });
        } catch (error) {
          // Haptics not supported
        }
      }

      if (offset.y > 0 && onSwipeDown) {
        onSwipeDown();
      } else if (offset.y < 0 && onSwipeUp) {
        onSwipeUp();
      }
    }
  };

  return (
    <motion.div
      ref={constraintsRef}
      className={cn("relative", className)}
    >
      <motion.div
        drag
        dragConstraints={constraintsRef}
        dragElastic={0.2}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={handleDragEnd}
        whileDrag={{ scale: 1.05, rotate: isDragging ? 2 : 0 }}
        className="cursor-grab active:cursor-grabbing"
      >
        {children}
      </motion.div>
    </motion.div>
  );
};

// Pull to refresh component
interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  refreshThreshold?: number;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  onRefresh,
  children,
  refreshThreshold = 80
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [canRefresh, setCanRefresh] = useState(false);

  const handleDrag = (event: any, info: any) => {
    const distance = Math.max(0, info.offset.y);
    setPullDistance(distance);
    setCanRefresh(distance > refreshThreshold);
  };

  const handleDragEnd = async (event: any, info: any) => {
      if (canRefresh && !isRefreshing) {
        setIsRefreshing(true);
        
        if (window.navigator?.userAgent.includes('Mobile')) {
          try {
            await Haptics.impact({ style: ImpactStyle.Medium });
          } catch (error) {
            // Haptics not supported
          }
        }

      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
        setCanRefresh(false);
      }
    } else {
      setPullDistance(0);
      setCanRefresh(false);
    }
  };

  return (
    <div className="relative overflow-hidden">
      {/* Pull indicator */}
      <motion.div
        className="absolute top-0 left-0 right-0 flex items-center justify-center py-4 bg-primary/10 backdrop-blur-sm"
        initial={{ y: -80 }}
        animate={{ 
          y: pullDistance > 0 ? pullDistance - 80 : -80,
          opacity: pullDistance > 20 ? 1 : 0
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <motion.div
          animate={{ rotate: canRefresh ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {isRefreshing ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full"
            />
          ) : (
            <ChevronDown className={cn(
              "w-6 h-6 transition-colors",
              canRefresh ? "text-primary" : "text-muted-foreground"
            )} />
          )}
        </motion.div>
        <span className={cn(
          "ml-2 text-sm font-medium transition-colors",
          canRefresh ? "text-primary" : "text-muted-foreground"
        )}>
          {isRefreshing ? "Refreshing..." : canRefresh ? "Release to refresh" : "Pull to refresh"}
        </span>
      </motion.div>

      {/* Content */}
      <motion.div
        drag="y"
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={{ top: 0.5, bottom: 0 }}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        style={{ y: pullDistance }}
      >
        {children}
      </motion.div>
    </div>
  );
};

// Bottom sheet component
interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  snapPoints?: string[];
  defaultSnapPoint?: number;
}

export const BottomSheet: React.FC<BottomSheetProps> = ({
  isOpen,
  onClose,
  children,
  snapPoints = ['25%', '50%', '90%'],
  defaultSnapPoint = 1
}) => {
  const [currentSnap, setCurrentSnap] = useState(defaultSnapPoint);
  const sheetRef = useRef<HTMLDivElement>(null);

  const handleDragEnd = (event: any, info: any) => {
    const { offset } = info;
    const threshold = 100;

    if (offset.y > threshold) {
      // Swipe down - close or snap to lower point
      if (currentSnap === 0) {
        onClose();
      } else {
        setCurrentSnap(Math.max(0, currentSnap - 1));
      }
    } else if (offset.y < -threshold) {
      // Swipe up - snap to higher point
      setCurrentSnap(Math.min(snapPoints.length - 1, currentSnap + 1));
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          {/* Sheet */}
          <motion.div
            ref={sheetRef}
            initial={{ y: '100%' }}
            animate={{ y: `calc(100% - ${snapPoints[currentSnap]})` }}
            exit={{ y: '100%' }}
            transition={{ type: "spring", damping: 30, stiffness: 300 }}
            className="fixed inset-x-0 bottom-0 z-50 glass-card rounded-t-xl shadow-xl"
            drag="y"
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={{ top: 0, bottom: 0.5 }}
            onDragEnd={handleDragEnd}
          >
            {/* Handle */}
            <div className="flex justify-center py-3">
              <div className="w-12 h-1 bg-muted-foreground/30 rounded-full" />
            </div>

            {/* Content */}
            <div className="px-4 pb-safe-area-inset-bottom max-h-[90vh] overflow-y-auto">
              {children}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// Long press component
interface LongPressProps {
  onLongPress: () => void;
  onPress?: () => void;
  longPressDuration?: number;
  children: React.ReactNode;
  className?: string;
}

export const LongPress: React.FC<LongPressProps> = ({
  onLongPress,
  onPress,
  longPressDuration = 500,
  children,
  className
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const pressTimer = useRef<NodeJS.Timeout | null>(null);
  const [pressProgress, setPressProgress] = useState(0);

  const startPress = () => {
    setIsPressed(true);
    setPressProgress(0);
    
    const startTime = Date.now();
    const updateProgress = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / longPressDuration, 1);
      setPressProgress(progress * 100);
      
      if (progress < 1) {
        requestAnimationFrame(updateProgress);
      }
    };
    
    requestAnimationFrame(updateProgress);

    pressTimer.current = setTimeout(async () => {
      if (window.navigator?.userAgent.includes('Mobile')) {
        try {
          await Haptics.impact({ style: ImpactStyle.Medium });
        } catch (error) {
          // Haptics not supported
        }
      }
      onLongPress();
      setIsPressed(false);
    }, longPressDuration);
  };

  const endPress = () => {
    if (pressTimer.current) {
      clearTimeout(pressTimer.current);
      pressTimer.current = null;
    }
    
    if (isPressed && pressProgress < 100) {
      onPress?.();
    }
    
    setIsPressed(false);
    setPressProgress(0);
  };

  return (
    <motion.div
      className={cn("relative select-none", className)}
      onMouseDown={startPress}
      onMouseUp={endPress}
      onMouseLeave={endPress}
      onTouchStart={startPress}
      onTouchEnd={endPress}
      whileTap={{ scale: 0.98 }}
    >
      {children}
      
      {/* Progress indicator */}
      <AnimatePresence>
        {isPressed && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute inset-0 flex items-center justify-center pointer-events-none"
          >
            <div className="w-16 h-16 relative">
              <svg className="w-full h-full transform -rotate-90">
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="transparent"
                  className="text-muted-foreground opacity-25"
                />
                <motion.circle
                  cx="32"
                  cy="32"
                  r="28"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="transparent"
                  strokeDasharray={`${2 * Math.PI * 28}`}
                  strokeDashoffset={`${2 * Math.PI * 28 * (1 - pressProgress / 100)}`}
                  className="text-primary"
                  strokeLinecap="round"
                />
              </svg>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Gesture-controlled modal
interface GestureModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
}

export const GestureModal: React.FC<GestureModalProps> = ({
  isOpen,
  onClose,
  children,
  title
}) => {
  const handleDragEnd = (event: any, info: any) => {
    if (info.offset.y > 150 || info.velocity.y > 300) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed inset-x-4 top-[10%] z-50 glass-card rounded-xl shadow-xl max-h-[80vh] overflow-hidden"
            drag="y"
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={{ top: 0, bottom: 0.7 }}
            onDragEnd={handleDragEnd}
          >
            {/* Handle */}
            <div className="flex justify-center py-3 cursor-grab active:cursor-grabbing">
              <div className="w-12 h-1 bg-muted-foreground/30 rounded-full" />
            </div>

            {/* Header */}
            {title && (
              <div className="flex items-center justify-between px-6 pb-4">
                <h2 className="text-xl font-semibold">{title}</h2>
                <button
                  onClick={onClose}
                  className="w-8 h-8 rounded-full bg-muted hover:bg-muted-foreground/20 flex items-center justify-center transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Content */}
            <div className="px-6 pb-6 overflow-y-auto">
              {children}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};