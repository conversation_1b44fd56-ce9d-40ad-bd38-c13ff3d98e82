{"compilerOptions": {"strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "moduleResolution": "node", "target": "es2017", "lib": ["es2017", "dom"], "jsx": "react-native"}, "include": ["src/**/*", "App.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base"}