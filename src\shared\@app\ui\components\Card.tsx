// Unified Card component for web and mobile platforms
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

const isReactNative = typeof window === 'undefined' || !!global.navigator?.product?.match(/ReactNative/);

const cardVariants = cva(
  'rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'border-border',
        elevated: 'shadow-lg border-0 bg-background',
        outlined: 'border-2 border-border shadow-none',
        filled: 'bg-muted border-0',
      },
      padding: {
        none: 'p-0',
        sm: 'p-3',
        md: 'p-6',
        lg: 'p-8',
      },
      interactive: {
        true: 'hover:shadow-md cursor-pointer transition-shadow',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      padding: 'md',
      interactive: false,
    },
  }
);

export interface CardProps extends VariantProps<typeof cardVariants> {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
  testId?: string;
}

export interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export interface CardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

// Web implementations
const WebCard: React.FC<CardProps> = ({
  children,
  onClick,
  className,
  style,
  testId,
  variant,
  padding,
  interactive,
  ...props
}) => {
  return (
    <div
      onClick={onClick}
      data-testid={testId}
      className={cardVariants({ variant, padding, interactive, className })}
      style={style}
      {...props}
    >
      {children}
    </div>
  );
};

const WebCardHeader: React.FC<CardHeaderProps> = ({ children, className }) => (
  <div className={`flex flex-col space-y-1.5 ${className || ''}`}>
    {children}
  </div>
);

const WebCardTitle: React.FC<CardTitleProps> = ({ children, className, as: Component = 'h3' }) => (
  <Component className={`text-2xl font-semibold leading-none tracking-tight ${className || ''}`}>
    {children}
  </Component>
);

const WebCardDescription: React.FC<CardDescriptionProps> = ({ children, className }) => (
  <p className={`text-sm text-muted-foreground ${className || ''}`}>
    {children}
  </p>
);

const WebCardContent: React.FC<CardContentProps> = ({ children, className }) => (
  <div className={`pt-0 ${className || ''}`}>
    {children}
  </div>
);

const WebCardFooter: React.FC<CardFooterProps> = ({ children, className }) => (
  <div className={`flex items-center pt-0 ${className || ''}`}>
    {children}
  </div>
);

// React Native implementations (placeholder)
const NativeCard: React.FC<CardProps> = ({ children, onClick, style }) => (
  <div
    onClick={onClick}
    style={{
      borderRadius: 8,
      backgroundColor: 'white',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      padding: 16,
      ...style,
    }}
  >
    {children}
  </div>
);

const NativeCardHeader: React.FC<CardHeaderProps> = ({ children }) => (
  <div style={{ marginBottom: 8 }}>{children}</div>
);

const NativeCardTitle: React.FC<CardTitleProps> = ({ children }) => (
  <div style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 4 }}>
    {children}
  </div>
);

const NativeCardDescription: React.FC<CardDescriptionProps> = ({ children }) => (
  <div style={{ fontSize: 14, color: '#666', marginBottom: 8 }}>
    {children}
  </div>
);

const NativeCardContent: React.FC<CardContentProps> = ({ children }) => (
  <div>{children}</div>
);

const NativeCardFooter: React.FC<CardFooterProps> = ({ children }) => (
  <div style={{ marginTop: 16, flexDirection: 'row', alignItems: 'center' }}>
    {children}
  </div>
);

// Unified exports
export const Card: React.FC<CardProps> = (props) => {
  if (isReactNative) {
    return <NativeCard {...props} />;
  }
  return <WebCard {...props} />;
};

export const CardHeader: React.FC<CardHeaderProps> = (props) => {
  if (isReactNative) {
    return <NativeCardHeader {...props} />;
  }
  return <WebCardHeader {...props} />;
};

export const CardTitle: React.FC<CardTitleProps> = (props) => {
  if (isReactNative) {
    return <NativeCardTitle {...props} />;
  }
  return <WebCardTitle {...props} />;
};

export const CardDescription: React.FC<CardDescriptionProps> = (props) => {
  if (isReactNative) {
    return <NativeCardDescription {...props} />;
  }
  return <WebCardDescription {...props} />;
};

export const CardContent: React.FC<CardContentProps> = (props) => {
  if (isReactNative) {
    return <NativeCardContent {...props} />;
  }
  return <WebCardContent {...props} />;
};

export const CardFooter: React.FC<CardFooterProps> = (props) => {
  if (isReactNative) {
    return <NativeCardFooter {...props} />;
  }
  return <WebCardFooter {...props} />;
};

export { cardVariants };