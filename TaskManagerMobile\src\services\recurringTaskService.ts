import { supabase } from '../lib/supabase';
import { Task } from '../types/task';
import { addDays, addWeeks, addMonths, addYears, format, parseISO } from 'date-fns';

export interface RecurringTask {
  id: string;
  task_id: string;
  pattern: RecurringPattern;
  frequency: number;
  start_date: string;
  end_date?: string;
  next_occurrence: string;
  is_active: boolean;
}

export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
  daysOfWeek?: number[]; // 0-6 for weekly
  dayOfMonth?: number; // 1-31 for monthly
  months?: number[]; // 1-12 for yearly
  interval?: number; // For custom patterns
}

export class RecurringTaskService {
  static async createRecurringTask(
    baseTask: Omit<Task, 'id' | 'created_at' | 'updated_at'>,
    pattern: RecurringPattern,
    frequency: number = 1,
    endDate?: Date
  ): Promise<string> {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      // Create the base task
      const { data: task, error: taskError } = await supabase
        .from('tasks')
        .insert({
          ...baseTask,
          user_id: user.user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (taskError) throw taskError;

      // Create recurring task record
      const nextOccurrence = this.calculateNextOccurrence(
        new Date(),
        pattern,
        frequency
      );

      const { data: recurringTask, error: recurringError } = await supabase
        .from('recurring_tasks')
        .insert({
          task_id: task.id,
          pattern,
          frequency,
          start_date: new Date().toISOString(),
          end_date: endDate?.toISOString(),
          next_occurrence: nextOccurrence.toISOString(),
          is_active: true,
        })
        .select()
        .single();

      if (recurringError) throw recurringError;

      return recurringTask.id;
    } catch (error) {
      console.error('Error creating recurring task:', error);
      throw error;
    }
  }

  static async generateNextOccurrence(recurringTaskId: string): Promise<Task | null> {
    try {
      const { data: recurringTask } = await supabase
        .from('recurring_tasks')
        .select('*, task:tasks(*)')
        .eq('id', recurringTaskId)
        .single();

      if (!recurringTask || !recurringTask.is_active) return null;

      const baseTask = recurringTask.task;
      const nextDate = new Date(recurringTask.next_occurrence);

      // Check if we should stop generating (end date reached)
      if (recurringTask.end_date && nextDate > new Date(recurringTask.end_date)) {
        await this.deactivateRecurringTask(recurringTaskId);
        return null;
      }

      // Create new task instance
      const newTask = {
        ...baseTask,
        date: nextDate.toISOString(),
        due_date: nextDate.toISOString(),
        status: 'pending',
        completed_at: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      delete (newTask as any).id;
      delete (newTask as any).created_at;
      delete (newTask as any).updated_at;

      const { data: createdTask, error } = await supabase
        .from('tasks')
        .insert(newTask)
        .select()
        .single();

      if (error) throw error;

      // Update next occurrence
      const nextNextOccurrence = this.calculateNextOccurrence(
        nextDate,
        recurringTask.pattern,
        recurringTask.frequency
      );

      await supabase
        .from('recurring_tasks')
        .update({ next_occurrence: nextNextOccurrence.toISOString() })
        .eq('id', recurringTaskId);

      return createdTask;
    } catch (error) {
      console.error('Error generating next occurrence:', error);
      throw error;
    }
  }

  static calculateNextOccurrence(
    currentDate: Date,
    pattern: RecurringPattern,
    frequency: number
  ): Date {
    switch (pattern.type) {
      case 'daily':
        return addDays(currentDate, frequency);
      case 'weekly':
        return addWeeks(currentDate, frequency);
      case 'monthly':
        return addMonths(currentDate, frequency);
      case 'yearly':
        return addYears(currentDate, frequency);
      case 'custom':
        if (pattern.interval) {
          return addDays(currentDate, pattern.interval * frequency);
        }
        return addDays(currentDate, frequency);
      default:
        return addDays(currentDate, frequency);
    }
  }

  static async getRecurringTasks(): Promise<RecurringTask[]> {
    try {
      const { data, error } = await supabase
        .from('recurring_tasks')
        .select('*')
        .eq('is_active', true)
        .order('next_occurrence', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting recurring tasks:', error);
      return [];
    }
  }

  static async getRecurringTaskById(id: string): Promise<RecurringTask | null> {
    try {
      const { data, error } = await supabase
        .from('recurring_tasks')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting recurring task:', error);
      return null;
    }
  }

  static async updateRecurringTask(
    id: string,
    updates: Partial<RecurringTask>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('recurring_tasks')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating recurring task:', error);
      throw error;
    }
  }

  static async deactivateRecurringTask(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('recurring_tasks')
        .update({ is_active: false })
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deactivating recurring task:', error);
      throw error;
    }
  }

  static async deleteRecurringTask(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('recurring_tasks')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting recurring task:', error);
      throw error;
    }
  }

  static async processDueRecurringTasks(): Promise<Task[]> {
    try {
      const recurringTasks = await this.getRecurringTasks();
      const now = new Date();
      const dueTasks = recurringTasks.filter(
        task => new Date(task.next_occurrence) <= now
      );

      const generatedTasks: Task[] = [];
      for (const task of dueTasks) {
        const newTask = await this.generateNextOccurrence(task.id);
        if (newTask) {
          generatedTasks.push(newTask);
        }
      }

      return generatedTasks;
    } catch (error) {
      console.error('Error processing due recurring tasks:', error);
      return [];
    }
  }

  static getPatternDescription(pattern: RecurringPattern, frequency: number): string {
    const freqText = frequency === 1 ? '' : `every ${frequency} `;

    switch (pattern.type) {
      case 'daily':
        return `${freqText}daily`;
      case 'weekly':
        if (pattern.daysOfWeek && pattern.daysOfWeek.length > 0) {
          const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
          const dayNames = pattern.daysOfWeek.map(d => days[d]).join(', ');
          return `${freqText}weekly on ${dayNames}`;
        }
        return `${freqText}weekly`;
      case 'monthly':
        if (pattern.dayOfMonth) {
          return `${freqText}monthly on day ${pattern.dayOfMonth}`;
        }
        return `${freqText}monthly`;
      case 'yearly':
        if (pattern.months && pattern.months.length > 0) {
          const months = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
          ];
          const monthNames = pattern.months.map(m => months[m - 1]).join(', ');
          return `${freqText}yearly in ${monthNames}`;
        }
        return `${freqText}yearly`;
      case 'custom':
        return `${freqText}custom interval`;
      default:
        return `${freqText}${pattern.type}`;
    }
  }

  static createDailyPattern(): RecurringPattern {
    return { type: 'daily' };
  }

  static createWeeklyPattern(daysOfWeek: number[]): RecurringPattern {
    return { type: 'weekly', daysOfWeek };
  }
  static createMonthlyPattern(dayOfMonth: number): RecurringPattern {
    return { type: 'monthly', dayOfMonth };
  }
  static createYearlyPattern(months: number[]): RecurringPattern {
    return { type: 'yearly', months };
  }
  static createCustomPattern(interval: number): RecurringPattern {
    return { type: 'custom', interval };
  }
}