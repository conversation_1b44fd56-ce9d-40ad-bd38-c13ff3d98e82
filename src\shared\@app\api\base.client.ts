import { ApiResponse, PaginatedResponse, SearchParams } from '../types';

export interface ApiConfig {
  baseUrl: string;
  apiKey?: string;
  timeout?: number;
  retries?: number;
}

export abstract class BaseApiClient {
  protected config: ApiConfig;
  protected retryDelays = [1000, 2000, 4000]; // Exponential backoff

  constructor(config: ApiConfig) {
    this.config = {
      timeout: 10000,
      retries: 3,
      ...config
    };
  }

  protected async request<T>(
    endpoint: string,
    options: RequestInit = {},
    attempt = 1
  ): Promise<ApiResponse<T>> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers
      };

      if (this.config.apiKey) {
        headers['Authorization'] = `Bearer ${this.config.apiKey}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: true,
        data,
        message: 'Request successful'
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (attempt < (this.config.retries || 3) && this.shouldRetry(error)) {
        await this.delay(this.retryDelays[attempt - 1] || 4000);
        return this.request<T>(endpoint, options, attempt + 1);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Request failed'
      };
    }
  }

  protected async get<T>(endpoint: string, params?: SearchParams): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${this.buildQueryString(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }

  protected async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  protected async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  protected async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  protected async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  private shouldRetry(error: unknown): boolean {
    if (error instanceof Error) {
      // Retry on network errors or 5xx server errors
      return error.name === 'AbortError' || 
             error.message.includes('fetch') ||
             error.message.includes('5');
    }
    return false;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private buildQueryString(params: SearchParams): string {
    const searchParams = new URLSearchParams();

    if (params.query) {
      searchParams.append('q', params.query);
    }

    if (params.pagination) {
      if (params.pagination.page) {
        searchParams.append('page', params.pagination.page.toString());
      }
      if (params.pagination.limit) {
        searchParams.append('limit', params.pagination.limit.toString());
      }
      if (params.pagination.offset) {
        searchParams.append('offset', params.pagination.offset.toString());
      }
    }

    if (params.sort) {
      searchParams.append('sort', params.sort.field);
      searchParams.append('order', params.sort.direction);
    }

    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(`${key}[]`, v.toString()));
        } else if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
    }

    return searchParams.toString();
  }
}