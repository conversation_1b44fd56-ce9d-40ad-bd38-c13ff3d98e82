import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://dltiijiloisdhppovdvk.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRsdGlpamlsb2lzZGhwcG92ZHZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3NDg0NjksImV4cCI6MjA2ODMyNDQ2OX0.apv31VTXHw3i46XxRfjJWC9qUOpeGTs-sYZX9H1Ikds';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('Testing Supabase connection...');
  console.log('URL:', supabaseUrl);
  console.log('Key:', supabaseKey.substring(0, 20) + '...');
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('Connection error:', error);
      return false;
    }
    
    console.log('✅ Connection successful!');
    
    // Test table structure
    const tables = ['profiles', 'tasks', 'file_attachments', 'notifications', 'recurring_tasks'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.error(`❌ Table ${table} error:`, error.message);
        } else {
          console.log(`✅ Table ${table} accessible`);
        }
      } catch (err) {
        console.error(`❌ Table ${table} exception:`, err.message);
      }
    }
    
    return true;
  } catch (err) {
    console.error('Connection exception:', err);
    return false;
  }
}

testConnection().then(success => {
  console.log('Test completed:', success ? 'SUCCESS' : 'FAILED');
  process.exit(success ? 0 : 1);
});