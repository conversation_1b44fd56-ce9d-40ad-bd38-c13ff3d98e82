-- Migration to fix duplicate file attachments issue
-- This adds a unique constraint and cleans up existing duplicates

-- First, remove duplicate entries keeping only the oldest one for each file
WITH duplicates AS (
  SELECT 
    id,
    ROW_NUMBER() OVER (
      PARTITION BY task_id, filename, user_id 
      ORDER BY uploaded_at ASC
    ) as rn
  FROM file_attachments
)
DELETE FROM file_attachments 
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- Add unique constraint to prevent future duplicates
ALTER TABLE file_attachments 
ADD CONSTRAINT unique_file_per_task_user 
UNIQUE (task_id, filename, user_id);

-- Add comment explaining the constraint
COMMENT ON CONSTRAINT unique_file_per_task_user ON file_attachments IS 
'Prevents duplicate file attachments for the same task and user';