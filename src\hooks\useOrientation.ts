import { useEffect, useState, useCallback } from 'react'

export type OrientationType = 'portrait-primary' | 'portrait-secondary' | 'landscape-primary' | 'landscape-secondary'

export interface OrientationState {
  orientation: OrientationType
  isPortrait: boolean
  isLandscape: boolean
  angle: number
  isLandscapeLeft: boolean
  isLandscapeRight: boolean
}

export function useOrientation() {
  const [orientationState, setOrientationState] = useState<OrientationState>(() => {
    const getInitialOrientation = (): OrientationState => {
      const orientation = screen.orientation?.type || 'portrait-primary'
      const angle = screen.orientation?.angle || 0
      
      return {
        orientation: orientation as OrientationType,
        isPortrait: orientation.includes('portrait'),
        isLandscape: orientation.includes('landscape'),
        angle,
        isLandscapeLeft: orientation === 'landscape-primary',
        isLandscapeRight: orientation === 'landscape-secondary'
      }
    }

    return getInitialOrientation()
  })

  const updateOrientation = useCallback(() => {
    const orientation = screen.orientation?.type || 'portrait-primary'
    const angle = screen.orientation?.angle || 0
    
    setOrientationState({
      orientation: orientation as OrientationType,
      isPortrait: orientation.includes('portrait'),
      isLandscape: orientation.includes('landscape'),
      angle,
      isLandscapeLeft: orientation === 'landscape-primary',
      isLandscapeRight: orientation === 'landscape-secondary'
    })
  }, [])

  useEffect(() => {
    if (screen.orientation) {
      screen.orientation.addEventListener('change', updateOrientation)
      return () => screen.orientation.removeEventListener('change', updateOrientation)
    } else {
      // Fallback for browsers without Screen Orientation API
      const handleOrientationChange = () => {
        // Use a timeout to ensure the window dimensions are updated
        setTimeout(updateOrientation, 100)
      }
      
      window.addEventListener('orientationchange', handleOrientationChange)
      window.addEventListener('resize', handleOrientationChange)
      
      return () => {
        window.removeEventListener('orientationchange', handleOrientationChange)
        window.removeEventListener('resize', handleOrientationChange)
      }
    }
  }, [updateOrientation])

  return orientationState
}

// Hook for landscape-specific layout adjustments
export function useLandscapeLayout() {
  const { isLandscape, isPortrait, orientation } = useOrientation()
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const getLayoutClasses = useCallback(() => {
    const classes = []
    
    if (isLandscape) {
      classes.push('landscape-mode')
      if (isMobile) {
        classes.push('mobile-landscape')
      }
    } else {
      classes.push('portrait-mode')
      if (isMobile) {
        classes.push('mobile-portrait')
      }
    }
    
    return classes.join(' ')
  }, [isLandscape, isMobile])

  const getContainerStyles = useCallback(() => {
    if (isLandscape && isMobile) {
      return {
        flexDirection: 'row' as const,
        height: '100vh',
        overflow: 'hidden'
      }
    }
    
    return {
      flexDirection: 'column' as const,
      minHeight: '100vh'
    }
  }, [isLandscape, isMobile])

  const getSidebarStyles = useCallback(() => {
    if (isLandscape && isMobile) {
      return {
        width: '280px',
        height: '100vh',
        borderRight: '1px solid hsl(var(--border))',
        borderBottom: 'none'
      }
    }
    
    return {
      width: '100%',
      height: 'auto',
      borderRight: 'none',
      borderBottom: '1px solid hsl(var(--border))'
    }
  }, [isLandscape, isMobile])

  const getMainContentStyles = useCallback(() => {
    if (isLandscape && isMobile) {
      return {
        flex: 1,
        height: '100vh',
        overflow: 'auto'
      }
    }
    
    return {
      flex: 1,
      minHeight: 'auto'
    }
  }, [isLandscape, isMobile])

  return {
    orientation,
    isLandscape,
    isPortrait,
    isMobile,
    getLayoutClasses,
    getContainerStyles,
    getSidebarStyles,
    getMainContentStyles
  }
}

// Hook for orientation-specific UI adjustments
export function useOrientationUI() {
  const { isLandscape, isPortrait } = useOrientation()
  const [windowDimensions, setWindowDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  })

  useEffect(() => {
    const handleResize = () => {
      setWindowDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const getCalendarLayoutProps = useCallback(() => {
    if (isLandscape && windowDimensions.width < 768) {
      return {
        showWeekNumbers: false,
        compactView: true,
        showAdjacentMonths: false,
        className: 'landscape-calendar'
      }
    }
    
    return {
      showWeekNumbers: true,
      compactView: false,
      showAdjacentMonths: true,
      className: 'portrait-calendar'
    }
  }, [isLandscape, windowDimensions.width])

  const getTaskListLayoutProps = useCallback(() => {
    if (isLandscape && windowDimensions.width < 768) {
      return {
        layout: 'horizontal' as const,
        itemsPerRow: 2,
        compactMode: true,
        className: 'landscape-task-list'
      }
    }
    
    return {
      layout: 'vertical' as const,
      itemsPerRow: 1,
      compactMode: false,
      className: 'portrait-task-list'
    }
  }, [isLandscape, windowDimensions.width])

  const getModalLayoutProps = useCallback(() => {
    if (isLandscape && windowDimensions.width < 768) {
      return {
        size: 'full',
        position: 'center',
        className: 'landscape-modal'
      }
    }
    
    return {
      size: 'default',
      position: 'center',
      className: 'portrait-modal'
    }
  }, [isLandscape, windowDimensions.width])

  return {
    isLandscape,
    isPortrait,
    windowDimensions,
    getCalendarLayoutProps,
    getTaskListLayoutProps,
    getModalLayoutProps
  }
}
