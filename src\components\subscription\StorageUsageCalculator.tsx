import React, { useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  HardDrive, 
  Trash2, 
  Upload, 
  Calendar,
  FileText,
  Image,
  Mic
} from 'lucide-react';
import { useSubscriptionStore } from '@/store/subscriptionStore';
import { formatFileSize, formatPrice } from '@/config/subscription';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface StorageUsageCalculatorProps {
  onCleanupClick?: () => void;
  onUpgradeClick?: () => void;
}

export const StorageUsageCalculator: React.FC<StorageUsageCalculatorProps> = ({
  onCleanupClick,
  onUpgradeClick,
}) => {
  const {
    storageUsage,
    subscription,
    getCurrentPlan,
    isProUser,
    getStoragePercentage,
  } = useSubscriptionStore();

  const [showDetails, setShowDetails] = useState(false);
  const plan = getCurrentPlan();
  const percentage = getStoragePercentage();
  const isNearLimit = percentage > 80;
  const isOverLimit = percentage > 100;

  if (!storageUsage || !subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Storage Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Loading storage information...
          </div>
        </CardContent>
      </Card>
    );
  }

  const getProgressColor = () => {
    if (isOverLimit) return 'bg-red-500';
    if (isNearLimit) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getStatusBadge = () => {
    if (isOverLimit) {
      return <Badge variant="destructive">Over Limit</Badge>;
    }
    if (isNearLimit) {
      return <Badge variant="secondary">Near Limit</Badge>;
    }
    return <Badge variant="default">Good</Badge>;
  };

  const getFileTypeBreakdown = () => {
    // This would typically come from your API
    // For now, we'll use mock data
    return [
      { type: 'Documents', icon: FileText, size: storageUsage.used * 0.4, count: Math.floor(storageUsage.filesCount * 0.5) },
      { type: 'Images', icon: Image, size: storageUsage.used * 0.3, count: Math.floor(storageUsage.filesCount * 0.3) },
      { type: 'Voice Notes', icon: Mic, size: storageUsage.used * 0.3, count: Math.floor(storageUsage.filesCount * 0.2) },
    ];
  };

  const getRetentionInfo = () => {
    const retentionDays = plan.limits.fileRetentionDays;
    if (retentionDays === -1) return 'Files kept permanently';
    return `Files deleted after ${retentionDays} days`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Storage Usage
          </div>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Used: {formatFileSize(storageUsage.used)}</span>
            <span>Limit: {formatFileSize(storageUsage.limit)}</span>
          </div>
          <Progress value={Math.min(100, percentage)} className="h-2" />
          <div className="text-xs text-muted-foreground text-center">
            {percentage.toFixed(1)}% used ({storageUsage.filesCount} files)
          </div>
        </div>

        {/* Alerts */}
        {isOverLimit && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You've exceeded your storage limit. Please clean up files or upgrade your plan.
            </AlertDescription>
          </Alert>
        )}

        {isNearLimit && !isOverLimit && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You're approaching your storage limit. Consider cleaning up files or upgrading.
            </AlertDescription>
          </Alert>
        )}

        {/* File Type Breakdown */}
        {showDetails && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm">File Breakdown</h4>
            {getFileTypeBreakdown().map((item) => (
              <div key={item.type} className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <item.icon className="h-4 w-4 text-muted-foreground" />
                  <span>{item.type}</span>
                </div>
                <div className="text-right">
                  <div>{formatFileSize(item.size)}</div>
                  <div className="text-xs text-muted-foreground">{item.count} files</div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Plan Information */}
        <div className="pt-2 border-t space-y-2">
          <div className="flex justify-between text-sm">
            <span>Current Plan:</span>
            <span className="font-medium">{plan.name}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>File Size Limit:</span>
            <span>{formatFileSize(plan.limits.maxFileSize)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Retention:</span>
            <span>{getRetentionInfo()}</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col gap-2 pt-2">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="flex-1"
            >
              {showDetails ? 'Hide' : 'Show'} Details
            </Button>
            {onCleanupClick && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCleanupClick}
                className="flex-1"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clean Up
              </Button>
            )}
          </div>
          
          {!isProUser() && (isNearLimit || isOverLimit) && onUpgradeClick && (
            <Button
              onClick={onUpgradeClick}
              className="w-full"
              variant={isOverLimit ? "destructive" : "default"}
            >
              <Upload className="h-4 w-4 mr-2" />
              Upgrade to Pro - {formatPrice(plan.price.monthly)}/month
            </Button>
          )}
        </div>

        {/* Oldest Files Warning */}
        {storageUsage.oldestFileDate && (
          <Alert>
            <Calendar className="h-4 w-4" />
            <AlertDescription>
              Your oldest files from {new Date(storageUsage.oldestFileDate).toLocaleDateString()} 
              may be deleted soon based on your retention policy.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};
