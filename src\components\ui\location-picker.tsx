import React, { useState } from 'react';
import { MapP<PERSON>, Loader2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LocationService, LocationData } from '@/services/locationService';
import { useToast } from '@/hooks/use-toast';

interface LocationPickerProps {
  value?: string;
  onChange?: (location: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export const LocationPicker: React.FC<LocationPickerProps> = ({
  value = '',
  onChange,
  placeholder = 'Add location...',
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const { toast } = useToast();

  const handleGetCurrentLocation = async () => {
    if (disabled) return;

    setIsLoading(true);
    try {
      const location = await LocationService.getCurrentLocation();
      
      if (location) {
        setCurrentLocation(location);
        const displayLocation = LocationService.formatLocationForDisplay(location);
        onChange?.(displayLocation);
        
        toast({
          title: "Location Added",
          description: "Current location has been added to your task.",
        });
      }
    } catch (error: any) {
      toast({
        title: "Location Error",
        description: error.message || "Failed to get current location.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualInput = (inputValue: string) => {
    setCurrentLocation(null);
    onChange?.(inputValue);
  };

  const handleClear = () => {
    setCurrentLocation(null);
    onChange?.('');
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Input
            value={value}
            onChange={(e) => handleManualInput(e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            className="pr-8"
          />
          {value && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-destructive/10"
              onClick={handleClear}
              disabled={disabled}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleGetCurrentLocation}
          disabled={disabled || isLoading}
          className="flex items-center gap-1.5 px-3"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <MapPin className="h-4 w-4" />
          )}
          {isLoading ? 'Getting...' : 'Current'}
        </Button>
      </div>

      {currentLocation && (
        <div className="flex items-center gap-2 text-xs text-muted-foreground bg-muted/50 rounded-md p-2">
          <MapPin className="h-3 w-3" />
          <span>
            Coordinates: {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
            {currentLocation.accuracy && (
              <span className="ml-2">
                (±{Math.round(currentLocation.accuracy)}m)
              </span>
            )}
          </span>
        </div>
      )}
    </div>
  );
};