-- Create enhanced notifications table with unified structure
CREATE TABLE IF NOT EXISTS unified_notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id text NOT NULL,
  type text NOT NULL CHECK (type IN ('task_reminder', 'task_overdue', 'task_assigned', 'task_completed', 'daily_digest', 'weekly_report', 'system_update')),
  title text NOT NULL,
  message text NOT NULL,
  data jsonb DEFAULT '{}',
  channels text[] NOT NULL DEFAULT '{"in_app"}',
  priority text NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  read boolean NOT NULL DEFAULT false,
  sent_at timestamp with time zone,
  delivered_channels text[] NOT NULL DEFAULT '{}',
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Enable RLS on unified_notifications
ALTER TABLE unified_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for unified_notifications
CREATE POLICY "Users can view their own notifications" 
ON unified_notifications 
FOR SELECT 
USING (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own notifications" 
ON unified_notifications 
FOR UPDATE 
USING (auth.uid()::text = user_id);

-- Create indexes for better performance
CREATE INDEX idx_unified_notifications_user_id ON unified_notifications(user_id);
CREATE INDEX idx_unified_notifications_type ON unified_notifications(type);
CREATE INDEX idx_unified_notifications_read ON unified_notifications(read);
CREATE INDEX idx_unified_notifications_created_at ON unified_notifications(created_at DESC);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_unified_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_unified_notifications_updated_at
  BEFORE UPDATE ON unified_notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_unified_notifications_updated_at();

-- Update profiles table to include comprehensive notification preferences
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS notification_preferences jsonb DEFAULT '{
  "enabled": true,
  "preferences": {
    "email": true,
    "push": true,
    "inApp": true,
    "sms": false
  },
  "taskReminders": true,
  "overdueAlerts": true,
  "assignmentNotifications": true,
  "completionNotifications": true,
  "dailyDigest": false,
  "weeklyReport": false,
  "systemUpdates": true,
  "reminderTime": 15,
  "quietHours": {
    "enabled": false,
    "start": "22:00",
    "end": "08:00"
  },
  "digestTime": "09:00",
  "weeklyReportDay": 1
}';

-- Enable realtime for unified_notifications
ALTER PUBLICATION supabase_realtime ADD TABLE unified_notifications;