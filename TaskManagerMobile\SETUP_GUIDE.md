# 🚀 Task Manager Mobile - Setup Guide

## Step 1: Install Dependencies
```bash
cd TaskManagerMobile
npm install
```

## Step 2: Configure Supabase

### Get Your Supabase Credentials:
1. Go to [supabase.com](https://supabase.com)
2. Sign in and go to your project dashboard
3. Click on "Settings" → "API"
4. Copy these two values:
   - **Project URL** (looks like: `https://abcdefg.supabase.co`)
   - **Anon public key** (starts with: `eyJhbGciOiJIUzI1NiIs...`)

### Update .env file:
1. Open the `.env` file in the TaskManagerMobile folder
2. Replace the placeholder values:
```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Step 3: Start the App
```bash
npm start
```

## Step 4: Open on Device
- **For Phone**: Download "Expo Go" app and scan the QR code
- **For Simulator**: Press 'i' for iOS or 'a' for Android
- **For Web**: Press 'w' to open in browser

## Troubleshooting

### If you see "Supabase connection failed":
1. Double-check your .env file has the correct URLs
2. Make sure there are no extra spaces
3. Restart the app with `npm start`

### If dependencies fail to install:
```bash
npm cache clean --force
rm -rf node_modules
npm install
```

### If Expo won't start:
```bash
npm install -g @expo/cli
npx expo start
```

## 🎉 You're Ready!
Once the app loads, you should see the Task Manager interface with three tabs:
- 📋 Tasks
- 👥 Assignments  
- ⚙️ Settings