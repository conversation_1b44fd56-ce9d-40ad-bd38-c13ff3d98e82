import { createClient } from '@supabase/supabase-js';

// Read from .env file
import { readFileSync } from 'fs';

let supabaseUrl, supabaseKey;
try {
  const envContent = readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n');
  
  for (const line of envLines) {
    if (line.startsWith('VITE_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].trim();
    }
    if (line.startsWith('VITE_SUPABASE_ANON_KEY=')) {
      supabaseKey = line.split('=')[1].trim();
    }
  }
} catch (err) {
  console.error('Error reading .env file:', err.message);
  process.exit(1);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing VITE_SUPABASE_URL or VITE_SUPABASE_ANON_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyDatabaseSchema() {
  console.log('🔍 Verifying database schema...');
  
  // List of tables to check based on TypeScript types
  const tablesToCheck = [
    'profiles',
    'tasks', 
    'file_attachments',
    'notifications',
    'recurring_tasks',
    'subscribers',
    'unified_notifications', 
    'user_relationships',
    'user_groups',
    'group_memberships',
    'assignee_groups',
    'assignees',
    'assignee_invitations',
    'task_assignments',
    'assignment_notifications'
  ];
  
  const existingTables = [];
  const missingTables = [];
  
  for (const table of tablesToCheck) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table '${table}' does not exist or is not accessible:`, error.message);
        missingTables.push(table);
      } else {
        console.log(`✅ Table '${table}' exists and is accessible`);
        existingTables.push(table);
      }
    } catch (err) {
      console.log(`❌ Error checking table '${table}':`, err.message);
      missingTables.push(table);
    }
  }
  
  // Check for views
  console.log('\n🔍 Checking views...');
  try {
    const { data, error } = await supabase
      .from('task_analytics')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log(`❌ View 'task_analytics' does not exist:`, error.message);
    } else {
      console.log(`✅ View 'task_analytics' exists and is accessible`);
    }
  } catch (err) {
    console.log(`❌ Error checking view 'task_analytics':`, err.message);
  }
  
  // Check for functions
  console.log('\n🔍 Checking functions...');
  const functionsToCheck = ['send_relationship_request', 'respond_to_relationship_request'];
  
  for (const func of functionsToCheck) {
    try {
      const { data, error } = await supabase.rpc(func, {});
      if (error && !error.message.includes('missing')) {
        console.log(`✅ Function '${func}' exists (got expected parameter error)`);
      } else if (error) {
        console.log(`❌ Function '${func}' does not exist:`, error.message);
      } else {
        console.log(`✅ Function '${func}' exists`);
      }
    } catch (err) {
      console.log(`❌ Error checking function '${func}':`, err.message);
    }
  }
  
  console.log('\n📊 Summary:');
  console.log(`✅ Existing tables (${existingTables.length}):`, existingTables.join(', '));
  console.log(`❌ Missing tables (${missingTables.length}):`, missingTables.join(', '));
  
  return { existingTables, missingTables };
}

verifyDatabaseSchema().catch(console.error);