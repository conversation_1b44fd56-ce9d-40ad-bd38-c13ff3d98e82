// Unified subscription store with cross-platform Stripe integration
import { UserSubscription, SubscriptionTier, SubscriptionStatus, StorageUsage, SubscriptionPlan } from '@/shared/@app/types';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface SubscriptionStoreState {
  subscription: UserSubscription | null;
  storageUsage: StorageUsage | null;
  stripeCustomerId: string | null;
  availablePlans: SubscriptionPlan[];
  currentPlan: SubscriptionPlan | null;
  features: {
    hasVoiceFeatures: boolean;
    hasAdvancedAnalytics: boolean;
    hasUnlimitedStorage: boolean;
    hasCollaboration: boolean;
  };
  isCheckingSubscription: boolean;
  subscriptionError: string | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
  isOnline: boolean;
}

interface SubscriptionStoreActions {
  setSubscription: (subscription: UserSubscription | null) => void;
  setStorageUsage: (usage: StorageUsage) => void;
  checkSubscriptionStatus: () => Promise<UserSubscription | null>;
  refreshSubscriptionData: () => Promise<void>;
  canUseFeature: (feature: keyof SubscriptionStoreState['features']) => boolean;
  canUploadFile: (fileSize: number) => boolean;
  getCurrentPlan: () => SubscriptionPlan | null;
  isSubscriptionActive: () => boolean;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLastUpdated: (timestamp: number) => void;
  setOnlineStatus: (online: boolean) => void;
  reset: () => void;
}

const DEFAULT_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Basic task management',
    price: { monthly: 0, yearly: 0 },
    stripePriceIds: { monthly: '', yearly: '' },
    features: ['Up to 100 tasks', '10MB storage'],
    limits: {
      maxFileSize: 5 * 1024 * 1024,
      maxStorageTotal: 10 * 1024 * 1024,
      maxCommentsPerTask: 5,
      hasVoiceFeatures: false,
      hasAdvancedAnalytics: false,
      fileRetentionDays: 30,
      notificationTypes: ['email']
    }
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Advanced features',
    price: { monthly: 9.99, yearly: 99.99 },
    stripePriceIds: { monthly: 'price_pro_monthly', yearly: 'price_pro_yearly' },
    features: ['Unlimited tasks', '1GB storage', 'Voice notes', 'Analytics'],
    limits: {
      maxFileSize: 100 * 1024 * 1024,
      maxStorageTotal: 1024 * 1024 * 1024,
      maxCommentsPerTask: -1,
      hasVoiceFeatures: true,
      hasAdvancedAnalytics: true,
      fileRetentionDays: -1,
      notificationTypes: ['email', 'sms']
    },
    popular: true
  }
];

export const useUnifiedSubscriptionStore = create<SubscriptionStoreState & SubscriptionStoreActions>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        subscription: null,
        storageUsage: null,
        stripeCustomerId: null,
        availablePlans: DEFAULT_PLANS,
        currentPlan: null,
        features: {
          hasVoiceFeatures: false,
          hasAdvancedAnalytics: false,
          hasUnlimitedStorage: false,
          hasCollaboration: false
        },
        isCheckingSubscription: false,
        subscriptionError: null,
        isLoading: false,
        error: null,
        lastUpdated: null,
        isOnline: true,

        // Actions
        setSubscription: (subscription) => set((state) => ({
          ...state,
          subscription,
          currentPlan: subscription 
            ? state.availablePlans.find(p => p.id === subscription.tier) || null
            : null,
          features: subscription ? {
            hasVoiceFeatures: subscription.tier === 'pro',
            hasAdvancedAnalytics: subscription.tier === 'pro',
            hasUnlimitedStorage: subscription.tier === 'pro',
            hasCollaboration: subscription.tier === 'pro'
          } : {
            hasVoiceFeatures: false,
            hasAdvancedAnalytics: false,
            hasUnlimitedStorage: false,
            hasCollaboration: false
          },
          lastUpdated: Date.now()
        })),

        setStorageUsage: (usage) => set((state) => ({
          ...state,
          storageUsage: usage
        })),

        checkSubscriptionStatus: async () => {
          set((state) => ({ 
            ...state,
            isCheckingSubscription: true,
            subscriptionError: null
          }));

          try {
            // Mock API call - replace with actual Stripe integration
            await new Promise(resolve => setTimeout(resolve, 1000));
            const mockSubscription: UserSubscription = {
              id: 'mock_sub',
              tier: 'free',
              status: 'active',
              trialEndsAt: null,
              stripeCustomerId: null,
              stripeSubscriptionId: null,
              storageUsedBytes: 0,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            get().setSubscription(mockSubscription);
            return mockSubscription;
          } catch (error) {
            set((state) => ({
              ...state,
              subscriptionError: error instanceof Error ? error.message : 'Status check failed'
            }));
            return null;
          } finally {
            set((state) => ({
              ...state,
              isCheckingSubscription: false
            }));
          }
        },

        refreshSubscriptionData: async () => {
          await get().checkSubscriptionStatus();
        },

        canUseFeature: (feature) => get().features[feature],

        canUploadFile: (fileSize) => {
          const { currentPlan, storageUsage } = get();
          if (!currentPlan) return false;
          if (fileSize > currentPlan.limits.maxFileSize) return false;
          if (currentPlan.limits.maxStorageTotal === -1) return true;
          if (storageUsage) {
            return (storageUsage.used + fileSize) <= currentPlan.limits.maxStorageTotal;
          }
          return true;
        },

        getCurrentPlan: () => get().currentPlan,

        isSubscriptionActive: () => {
          const { subscription } = get();
          return subscription?.status === 'active' || subscription?.status === 'trial';
        },

        // Base store actions
        setLoading: (loading) => set((state) => ({ ...state, isLoading: loading })),
        setError: (error) => set((state) => ({ ...state, error })),
        setLastUpdated: (timestamp) => set((state) => ({ ...state, lastUpdated: timestamp })),
        setOnlineStatus: (online) => set((state) => ({ ...state, isOnline: online })),
        
        reset: () => set({
          subscription: null,
          storageUsage: null,
          stripeCustomerId: null,
          availablePlans: DEFAULT_PLANS,
          currentPlan: DEFAULT_PLANS.find(p => p.id === 'free') || null,
          features: {
            hasVoiceFeatures: false,
            hasAdvancedAnalytics: false,
            hasUnlimitedStorage: false,
            hasCollaboration: false
          },
          isCheckingSubscription: false,
          subscriptionError: null,
          isLoading: false,
          error: null,
          lastUpdated: null,
          isOnline: true
        })
      }),
      {
        name: 'unified-subscription-store',
        partialize: (state) => ({
          subscription: state.subscription,
          storageUsage: state.storageUsage,
          currentPlan: state.currentPlan,
          features: state.features,
          lastUpdated: state.lastUpdated
        }),
      }
    ),
    { name: 'subscription-store' }
  )
);