-- Fix task_analytics RLS security issue
ALTER TABLE public.task_analytics ENABLE ROW LEVEL SECURITY;

-- Add RLS policy for task_analytics
CREATE POLICY "Users can view own analytics" 
ON public.task_analytics 
FOR SELECT 
USING (user_id = auth.uid()::text);

CREATE POLICY "Users can insert own analytics" 
ON public.task_analytics 
FOR INSERT 
WITH CHECK (user_id = auth.uid()::text);

CREATE POLICY "Users can update own analytics" 
ON public.task_analytics 
FOR UPDATE 
USING (user_id = auth.uid()::text);

CREATE POLICY "Users can delete own analytics" 
ON public.task_analytics 
FOR DELETE 
USING (user_id = auth.uid()::text);