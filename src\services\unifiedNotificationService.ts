import { supabase } from '@/lib/supabase';
import { Task } from '@/types/unified-task';

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  inApp: boolean;
  sms: boolean;
}

export interface NotificationSettings {
  // General preferences
  enabled: boolean;
  preferences: NotificationPreferences;
  
  // Task-related notifications
  taskReminders: boolean;
  overdueAlerts: boolean;
  completionNotifications: boolean;
  
  // System notifications
  dailyDigest: boolean;
  weeklyReport: boolean;
  systemUpdates: boolean;
  
  // Timing settings
  reminderTime: number; // minutes before due time
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string;   // HH:MM format
  };
  
  // Frequency settings
  digestTime: string; // HH:MM format for daily digest
  weeklyReportDay: number; // 0-6 (Sunday-Saturday)
}

export interface UnifiedNotification {
  id: string;
  user_id: string;
  type: 'task_reminder' | 'task_overdue' | 'task_completed' | 'daily_digest' | 'weekly_report' | 'system_update';
  title: string;
  message: string;
  data?: {
    task_id?: string;

    action_url?: string;
    [key: string]: any;
  };
  channels: ('email' | 'push' | 'in_app' | 'sms')[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  sent_at?: string;
  delivered_channels: string[];
  created_at: string;
  updated_at: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export class UnifiedNotificationService {
  private static instance: UnifiedNotificationService;
  private settings: NotificationSettings | null = null;
  private realtimeChannel: any = null;
  private scheduledNotifications: Map<string, number> = new Map();

  private constructor() {}

  static getInstance(): UnifiedNotificationService {
    if (!UnifiedNotificationService.instance) {
      UnifiedNotificationService.instance = new UnifiedNotificationService();
    }
    return UnifiedNotificationService.instance;
  }

  /**
   * Initialize the notification service for a user
   */
  async initialize(userId: string): Promise<void> {
    await this.loadSettings(userId);
    await this.setupRealtimeSubscription(userId);
    this.startPeriodicChecks();
  }

  /**
   * Load user notification settings
   */
  private async loadSettings(userId: string): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('notification_preferences')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (data?.notification_preferences) {
        this.settings = data.notification_preferences as unknown as NotificationSettings;
      } else {
        // Set default settings
        this.settings = this.getDefaultSettings();
        await this.updateSettings(userId, this.settings);
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
      this.settings = this.getDefaultSettings();
    }
  }

  /**
   * Get default notification settings
   */
  private getDefaultSettings(): NotificationSettings {
    return {
      enabled: true,
      preferences: {
        email: true,
        push: true,
        inApp: true,
        sms: false,
      },
      taskReminders: true,
      overdueAlerts: true,
      completionNotifications: true,
      dailyDigest: false,
      weeklyReport: false,
      systemUpdates: true,
      reminderTime: 15,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
      },
      digestTime: '09:00',
      weeklyReportDay: 1, // Monday
    };
  }

  /**
   * Update notification settings
   */
  async updateSettings(userId: string, newSettings: Partial<NotificationSettings>): Promise<void> {
    this.settings = { ...this.settings!, ...newSettings };

    const { error } = await supabase
      .from('profiles')
      .update({ notification_preferences: this.settings as any })
      .eq('id', userId);

    if (error) throw error;

    // Restart periodic checks with new settings
    if (this.settings.enabled) {
      this.startPeriodicChecks();
    } else {
      this.stopPeriodicChecks();
    }
  }

  /**
   * Get current settings
   */
  getSettings(): NotificationSettings | null {
    return this.settings;
  }

  /**
   * Setup real-time subscription for notifications
   */
  private async setupRealtimeSubscription(userId: string): Promise<void> {
    if (this.realtimeChannel) {
      await supabase.removeChannel(this.realtimeChannel);
    }

    this.realtimeChannel = supabase
      .channel(`notifications_${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'unified_notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          this.handleRealtimeNotification(payload.new as UnifiedNotification);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'unified_notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          this.handleNotificationUpdate(payload.new as UnifiedNotification);
        }
      )
      .subscribe();
  }

  /**
   * Handle real-time notification
   */
  private handleRealtimeNotification(notification: UnifiedNotification): void {
    // Show in-app notification if enabled
    if (this.shouldShowInApp(notification)) {
      this.showInAppNotification(notification);
    }

    // Show browser push notification if enabled
    if (this.shouldShowPush(notification)) {
      this.showPushNotification(notification);
    }

    // Trigger custom event for UI updates
    window.dispatchEvent(new CustomEvent('unifiedNotification', {
      detail: notification
    }));
  }

  /**
   * Handle notification update
   */
  private handleNotificationUpdate(notification: UnifiedNotification): void {
    window.dispatchEvent(new CustomEvent('notificationUpdate', {
      detail: notification
    }));
  }

  /**
   * Send notification through multiple channels
   */
  async sendNotification(
    userId: string,
    type: UnifiedNotification['type'],
    title: string,
    message: string,
    data?: UnifiedNotification['data'],
    priority: UnifiedNotification['priority'] = 'medium'
  ): Promise<string> {
    if (!this.settings?.enabled) return '';

    const channels = this.getChannelsForNotification(type) as ('email' | 'push' | 'in_app' | 'sms')[];
    if (channels.length === 0) return '';

    // Check quiet hours
    if (this.isQuietHours()) {
      priority = 'low'; // Lower priority during quiet hours
    }

    // Create notification record
    const notification: Omit<UnifiedNotification, 'id' | 'created_at' | 'updated_at'> = {
      user_id: userId,
      type,
      title,
      message,
      data,
      channels,
      priority,
      read: false,
      delivered_channels: ['in_app'], // Always create in-app notification
    };

    const { data: created, error } = await supabase
      .from('unified_notifications')
      .insert(notification)
      .select()
      .single();

    if (error) throw error;

    // Send through external channels (email, SMS) via edge functions
    await this.sendExternalNotifications(created as UnifiedNotification, channels);

    return created.id;
  }

  /**
   * Send external notifications (email, SMS)
   */
  private async sendExternalNotifications(
    notification: UnifiedNotification,
    channels: string[]
  ): Promise<void> {
    const externalChannels = channels.filter(c => c === 'email' || c === 'sms');
    
    for (const channel of externalChannels) {
      try {
        if (channel === 'email') {
          await this.sendEmailNotification(notification);
        } else if (channel === 'sms') {
          await this.sendSMSNotification(notification);
        }

        // Update delivered channels
        await this.updateDeliveredChannels(notification.id, channel);
      } catch (error) {
        console.error(`Failed to send ${channel} notification:`, error);
      }
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notification: UnifiedNotification): Promise<void> {
    const template = this.getEmailTemplate(notification);
    
    const { error } = await supabase.functions.invoke('send-notification-email', {
      body: {
        notification_id: notification.id,
        user_id: notification.user_id,
        subject: template.subject,
        html: template.html,
        text: template.text,
      },
    });

    if (error) throw error;
  }

  /**
   * Send SMS notification
   */
  private async sendSMSNotification(notification: UnifiedNotification): Promise<void> {
    const { error } = await supabase.functions.invoke('send-notification-sms', {
      body: {
        notification_id: notification.id,
        user_id: notification.user_id,
        message: `${notification.title}: ${notification.message}`,
      },
    });

    if (error) throw error;
  }

  /**
   * Update delivered channels
   */
  private async updateDeliveredChannels(notificationId: string, channel: string): Promise<void> {
    const { data: notification } = await supabase
      .from('unified_notifications')
      .select('delivered_channels')
      .eq('id', notificationId)
      .single();

    if (notification) {
      const updatedChannels = [...new Set([...notification.delivered_channels, channel])];
      
      await supabase
        .from('unified_notifications')
        .update({ delivered_channels: updatedChannels })
        .eq('id', notificationId);
    }
  }

  /**
   * Get email template for notification
   */
  private getEmailTemplate(notification: UnifiedNotification): EmailTemplate {
    const baseUrl = window.location.origin;
    
    switch (notification.type) {
      case 'task_reminder':
        return {
          subject: `Task Reminder: ${notification.title}`,
          html: `
            <h2>Task Reminder</h2>
            <p>${notification.message}</p>
            ${notification.data?.action_url ? `<a href="${baseUrl}${notification.data.action_url}">View Task</a>` : ''}
          `,
        };
      

      case 'daily_digest':
        return {
          subject: 'Daily Task Digest',
          html: `
            <h2>Your Daily Task Summary</h2>
            <p>${notification.message}</p>
            <a href="${baseUrl}">View Tasks</a>
          `,
        };
      
      default:
        return {
          subject: notification.title,
          html: `
            <h2>${notification.title}</h2>
            <p>${notification.message}</p>
          `,
        };
    }
  }

  /**
   * Determine channels for notification type
   */
  private getChannelsForNotification(type: UnifiedNotification['type']): string[] {
    if (!this.settings) return [];

    const channels: string[] = [];
    const { preferences } = this.settings;

    // Always include in-app if enabled
    if (preferences.inApp) {
      channels.push('in_app');
    }

    // Add other channels based on type and preferences
    switch (type) {
      case 'task_reminder':
        if (this.settings.taskReminders) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
        }
        break;
      
      case 'task_overdue':
        if (this.settings.overdueAlerts) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
          if (preferences.sms) channels.push('sms');
        }
        break;
      

      case 'task_completed':
        if (this.settings.completionNotifications) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
        }
        break;
      
      case 'daily_digest':
        if (this.settings.dailyDigest) {
          if (preferences.email) channels.push('email');
        }
        break;
      
      case 'weekly_report':
        if (this.settings.weeklyReport) {
          if (preferences.email) channels.push('email');
        }
        break;
      
      case 'system_update':
        if (this.settings.systemUpdates) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
        }
        break;
    }

    return channels;
  }

  /**
   * Check if it's quiet hours
   */
  private isQuietHours(): boolean {
    if (!this.settings?.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const start = this.parseTime(this.settings.quietHours.start);
    const end = this.parseTime(this.settings.quietHours.end);

    if (start > end) {
      // Quiet hours span midnight
      return currentTime >= start || currentTime <= end;
    } else {
      return currentTime >= start && currentTime <= end;
    }
  }

  /**
   * Parse time string (HH:MM) to minutes
   */
  private parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Check if should show in-app notification
   */
  private shouldShowInApp(notification: UnifiedNotification): boolean {
    return this.settings?.preferences.inApp && 
           notification.channels.includes('in_app');
  }

  /**
   * Check if should show push notification
   */
  private shouldShowPush(notification: UnifiedNotification): boolean {
    return this.settings?.preferences.push && 
           notification.channels.includes('push') &&
           'Notification' in window &&
           Notification.permission === 'granted';
  }

  /**
   * Show in-app notification
   */
  private showInAppNotification(notification: UnifiedNotification): void {
    // This will be handled by UI components listening to the custom event
    console.log('In-app notification:', notification);
  }

  /**
   * Show browser push notification
   */
  private showPushNotification(notification: UnifiedNotification): void {
    if (!this.shouldShowPush(notification)) return;

    const browserNotification = new Notification(notification.title, {
      body: notification.message,
      icon: '/favicon.svg',
      badge: '/favicon-32x32.svg',
      tag: notification.id,
      requireInteraction: notification.priority === 'urgent',
      silent: this.isQuietHours(),
    });

    // Auto-close after 8 seconds for non-urgent notifications
    if (notification.priority !== 'urgent') {
      setTimeout(() => {
        browserNotification.close();
      }, 8000);
    }

    // Handle click action
    browserNotification.onclick = () => {
      if (notification.data?.action_url) {
        window.focus();
        window.location.href = notification.data.action_url;
      }
      browserNotification.close();
    };
  }

  /**
   * Schedule task-related notifications
   */
  async scheduleTaskNotifications(task: Task): Promise<void> {
    if (!this.settings?.enabled) return;

    this.clearTaskNotifications(task.id);

    if (task.due_date && task.status !== 'completed') {
      const dueTime = new Date(task.due_date).getTime();
      const reminderTime = dueTime - (this.settings.reminderTime * 60 * 1000);
      const now = Date.now();

      // Schedule reminder notification
      if (reminderTime > now && this.settings.taskReminders) {
        const timeoutId = window.setTimeout(async () => {
          await this.sendNotification(
            task.user_id,
            'task_reminder',
            'Task Reminder',
            `"${task.title}" is due in ${this.settings!.reminderTime} minutes`,
            { task_id: task.id, action_url: `/tasks/${task.id}` },
            'medium'
          );
        }, reminderTime - now);

        this.scheduledNotifications.set(`${task.id}-reminder`, timeoutId);
      }

      // Schedule overdue check
      if (dueTime > now && this.settings.overdueAlerts) {
        const timeoutId = window.setTimeout(async () => {
          // Check if task is still not completed
          const { data: currentTask } = await supabase
            .from('tasks')
            .select('status')
            .eq('id', task.id)
            .single();

          if (currentTask && currentTask.status !== 'completed') {
            await this.sendNotification(
              task.user_id,
              'task_overdue',
              'Task Overdue',
              `"${task.title}" is now overdue`,
              { task_id: task.id, action_url: `/tasks/${task.id}` },
              'high'
            );
          }
        }, dueTime - now + 60000); // 1 minute after due time

        this.scheduledNotifications.set(`${task.id}-overdue`, timeoutId);
      }
    }
  }

  /**
   * Clear scheduled notifications for a task
   */
  clearTaskNotifications(taskId: string): void {
    const keys = Array.from(this.scheduledNotifications.keys())
      .filter(key => key.startsWith(taskId));
    
    keys.forEach(key => {
      const timeoutId = this.scheduledNotifications.get(key);
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.scheduledNotifications.delete(key);
      }
    });
  }

  /**
   * Start periodic checks for digest and reports
   */
  private startPeriodicChecks(): void {
    // Check every hour for scheduled digests/reports
    setInterval(() => {
      this.checkScheduledNotifications();
    }, 60 * 60 * 1000);
  }

  /**
   * Stop periodic checks
   */
  private stopPeriodicChecks(): void {
    // Clear all scheduled notifications
    this.scheduledNotifications.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    this.scheduledNotifications.clear();
  }

  /**
   * Check for scheduled digest and report notifications
   */
  private async checkScheduledNotifications(): Promise<void> {
    if (!this.settings?.enabled) return;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const digestTime = this.parseTime(this.settings.digestTime);

    // Check for daily digest
    if (this.settings.dailyDigest && Math.abs(currentTime - digestTime) < 30) {
      await this.sendDailyDigest();
    }

    // Check for weekly report (only on the specified day)
    if (this.settings.weeklyReport && 
        now.getDay() === this.settings.weeklyReportDay && 
        Math.abs(currentTime - digestTime) < 30) {
      await this.sendWeeklyReport();
    }
  }

  /**
   * Send daily digest
   */
  private async sendDailyDigest(): Promise<void> {
    // Implementation would fetch today's tasks and send digest
    console.log('Sending daily digest...');
  }

  /**
   * Send weekly report
   */
  private async sendWeeklyReport(): Promise<void> {
    // Implementation would fetch week's tasks and send report
    console.log('Sending weekly report...');
  }

  /**
   * Get notifications for user
   */
  async getNotifications(userId: string, limit: number = 50): Promise<UnifiedNotification[]> {
    const { data, error } = await supabase
      .from('unified_notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return (data || []) as UnifiedNotification[];
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('unified_notifications')
      .update({ read: true })
      .eq('id', notificationId);

    if (error) throw error;
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(userId: string): Promise<void> {
    const { error } = await supabase
      .from('unified_notifications')
      .update({ read: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) throw error;
  }

  /**
   * Get unread count
   */
  async getUnreadCount(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('unified_notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) throw error;
    return count || 0;
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      try {
        const permission = await Notification.requestPermission();
        return permission === 'granted';
      } catch (error) {
        console.error('Failed to request notification permission:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Cleanup on destruction
   */
  destroy(): void {
    if (this.realtimeChannel) {
      supabase.removeChannel(this.realtimeChannel);
    }
    this.stopPeriodicChecks();
  }
}

// Export singleton instance
export const unifiedNotificationService = UnifiedNotificationService.getInstance();