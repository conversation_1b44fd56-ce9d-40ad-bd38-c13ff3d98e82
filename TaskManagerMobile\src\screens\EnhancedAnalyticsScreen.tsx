import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTaskStore } from '../stores/taskStore';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isWithinInterval, subDays } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  AnalyticsDetail: { filterType: string; title: string };
  export: { filterType: string; title: string };
};

type AnalyticsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AnalyticsDetail'>;

const screenWidth = Dimensions.get('window').width;

export default function EnhancedAnalyticsScreen() {
  const { tasks, loadTasks } = useTaskStore();
  const navigation = useNavigation<AnalyticsScreenNavigationProp>();
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year' | 'all'>('week');
  const [viewType, setViewType] = useState<'overview' | 'detailed' | 'trend'>('overview');

  useEffect(() => {
    loadTasks();
  }, []);

  const getFilteredTasks = () => {
    const now = new Date();
    
    switch (timeRange) {
      case 'week':
        const weekStart = startOfWeek(now);
        const weekEnd = endOfWeek(now);
        return tasks.filter(task => 
          isWithinInterval(new Date(task.date), { start: weekStart, end: weekEnd })
        );
      case 'month':
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        return tasks.filter(task => 
          isWithinInterval(new Date(task.date), { start: monthStart, end: monthEnd })
        );
      case 'quarter':
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);
        return tasks.filter(task => 
          isWithinInterval(new Date(task.date), { start: quarterStart, end: quarterEnd })
        );
      case 'year':
        const yearStart = new Date(now.getFullYear(), 0, 1);
        const yearEnd = new Date(now.getFullYear(), 11, 31);
        return tasks.filter(task => 
          isWithinInterval(new Date(task.date), { start: yearStart, end: yearEnd })
        );
      default:
        return tasks;
    }
  };

  const filteredTasks = getFilteredTasks();

  const getTaskStats = () => {
    const total = filteredTasks.length;
    const completed = filteredTasks.filter(t => t.status === 'completed').length;
    const inProgress = filteredTasks.filter(t => t.status === 'in-progress').length;
    const pending = filteredTasks.filter(t => t.status === 'pending').length;
    const onHold = filteredTasks.filter(t => t.status === 'on-hold').length;
    const cancelled = filteredTasks.filter(t => t.status === 'cancelled').length;
    
    const highPriority = filteredTasks.filter(t => t.priority === 'high').length;
    const mediumPriority = filteredTasks.filter(t => t.priority === 'medium').length;
    const lowPriority = filteredTasks.filter(t => t.priority === 'low').length;
    
    const overdue = filteredTasks.filter(t => 
      t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'completed'
    ).length;

    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

    // Category breakdown
    const categories = filteredTasks.reduce((acc, task) => {
      const cat = task.category || 'General';
      acc[cat] = (acc[cat] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Daily completion trend for last 7 days
    const dailyTrend = [];
    for (let i = 6; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const dateStr = format(date, 'MMM dd');
      const dayTasks = filteredTasks.filter(t => 
        format(new Date(t.date), 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
      );
      const completed = dayTasks.filter(t => t.status === 'completed').length;
      dailyTrend.push({ date: dateStr, completed, total: dayTasks.length });
    }

    return {
      total,
      completed,
      inProgress,
      pending,
      onHold,
      cancelled,
      highPriority,
      mediumPriority,
      lowPriority,
      overdue,
      completionRate,
      categories,
      dailyTrend,
    };
  };

  const stats = getTaskStats();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10b981';
      case 'in-progress': return '#3b82f6';
      case 'pending': return '#6b7280';
      case 'on-hold': return '#8b5cf6';
      case 'cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const ProgressBar = ({ value, max, color, label }: { value: number; max: number; color: string; label: string }) => {
    const percentage = max > 0 ? (value / max) * 100 : 0;
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressLabel}>{label}</Text>
          <Text style={styles.progressValue}>{value} ({Math.round(percentage)}%)</Text>
        </View>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${percentage}%`, backgroundColor: color }]} />
        </View>
      </View>
    );
  };

  const CircularProgress = ({ value, max, color, size = 80 }: { value: number; max: number; color: string; size?: number }) => {
    const percentage = max > 0 ? (value / max) * 100 : 0;
    const radius = size / 2 - 10;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    return (
      <View style={[styles.circularProgress, { width: size, height: size }]}>
        <View style={styles.circularProgressContainer}>
          <Text style={styles.circularProgressText}>{Math.round(percentage)}%</Text>
          <Text style={styles.circularProgressLabel}>{value}/{max}</Text>
        </View>
      </View>
    );
  };

  const StatCard = ({ title, value, color, subtitle, icon, onPress }: any) => (
    <TouchableOpacity
      style={[styles.statCard, { borderLeftColor: color }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.statHeader}>
        <Text style={styles.statTitle}>{title}</Text>
        {icon && <Ionicons name={icon} size={20} color={color} />}
      </View>
      <Text style={[styles.statValue, { color }]}>{value}</Text>
      {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Enhanced Analytics</Text>
        <View style={styles.timeRangeContainer}>
          {(['week', 'month', 'quarter', 'year', 'all'] as const).map((range) => (
            <TouchableOpacity
              key={range}
              style={[
                styles.timeRangeButton,
                timeRange === range && styles.activeTimeRange
              ]}
              onPress={() => setTimeRange(range)}
            >
              <Text
                style={[
                  styles.timeRangeText,
                  timeRange === range && styles.activeTimeRangeText
                ]}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Overview Cards */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="Total Tasks"
              value={stats.total}
              color="#3b82f6"
              icon="document-text-outline"
            />
            <StatCard
              title="Completion Rate"
              value={`${stats.completionRate}%`}
              color="#10b981"
              icon="checkmark-circle-outline"
            />
            <StatCard
              title="Overdue"
              value={stats.overdue}
              color="#ef4444"
              icon="alert-circle-outline"
            />
          </View>
        </View>

        {/* Status Distribution */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Task Status Distribution</Text>
          <View style={styles.chartContainer}>
            <ProgressBar
              value={stats.completed}
              max={stats.total}
              color="#10b981"
              label="Completed"
            />
            <ProgressBar
              value={stats.inProgress}
              max={stats.total}
              color="#3b82f6"
              label="In Progress"
            />
            <ProgressBar
              value={stats.pending}
              max={stats.total}
              color="#6b7280"
              label="Pending"
            />
            <ProgressBar
              value={stats.onHold}
              max={stats.total}
              color="#8b5cf6"
              label="On Hold"
            />
            <ProgressBar
              value={stats.cancelled}
              max={stats.total}
              color="#ef4444"
              label="Cancelled"
            />
          </View>
        </View>

        {/* Priority Distribution */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Priority Distribution</Text>
          <View style={styles.priorityGrid}>
            <View style={[styles.priorityCard, { backgroundColor: '#fef2f2', borderColor: '#ef4444' }]}>
              <Text style={[styles.priorityLabel, { color: '#ef4444' }]}>High Priority</Text>
              <Text style={[styles.priorityValue, { color: '#ef4444' }]}>{stats.highPriority}</Text>
              <CircularProgress
                value={stats.highPriority}
                max={stats.total}
                color="#ef4444"
                size={60}
              />
            </View>
            <View style={[styles.priorityCard, { backgroundColor: '#fffbeb', borderColor: '#f59e0b' }]}>
              <Text style={[styles.priorityLabel, { color: '#f59e0b' }]}>Medium Priority</Text>
              <Text style={[styles.priorityValue, { color: '#f59e0b' }]}>{stats.mediumPriority}</Text>
              <CircularProgress
                value={stats.mediumPriority}
                max={stats.total}
                color="#f59e0b"
                size={60}
              />
            </View>
            <View style={[styles.priorityCard, { backgroundColor: '#f0fdf4', borderColor: '#10b981' }]}>
              <Text style={[styles.priorityLabel, { color: '#10b981' }]}>Low Priority</Text>
              <Text style={[styles.priorityValue, { color: '#10b981' }]}>{stats.lowPriority}</Text>
              <CircularProgress
                value={stats.lowPriority}
                max={stats.total}
                color="#10b981"
                size={60}
              />
            </View>
          </View>
        </View>

        {/* Daily Trend */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Completion Trend</Text>
          <View style={styles.trendContainer}>
            {stats.dailyTrend.map((day, index) => (
              <View key={index} style={styles.trendItem}>
                <Text style={styles.trendDate}>{day.date}</Text>
                <View style={styles.trendBarContainer}>
                  <View
                    style={[
                      styles.trendBar,
                      {
                        height: Math.max(4, (day.completed / Math.max(...stats.dailyTrend.map(d => d.completed), 1)) * 40),
                        backgroundColor: '#10b981',
                      },
                    ]}
                  />
                </View>
                <Text style={styles.trendValue}>{day.completed}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Category Breakdown */}
        {Object.keys(stats.categories).length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Category Breakdown</Text>
            <View style={styles.categoryContainer}>
              {Object.entries(stats.categories).map(([category, count]) => (
                <View key={category} style={styles.categoryItem}>
                  <Text style={styles.categoryName}>{category}</Text>
                  <ProgressBar
                    value={count}
                    max={stats.total}
                    color="#8b5cf6"
                    label=""
                  />
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 4,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTimeRange: {
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  timeRangeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
  },
  activeTimeRangeText: {
    color: '#1f2937',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  statsGrid: {
    gap: 12,
  },
  statCard: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 12,
    color: '#9ca3af',
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  progressLabel: {
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '500',
  },
  progressValue: {
    fontSize: 14,
    color: '#6b7280',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  circularProgress: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  circularProgressContainer: {
    alignItems: 'center',
  },
  circularProgressText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  circularProgressLabel: {
    fontSize: 10,
    color: '#6b7280',
  },
  priorityGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  priorityCard: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  priorityLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  priorityValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingVertical: 16,
  },
  trendItem: {
    alignItems: 'center',
    flex: 1,
  },
  trendDate: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 4,
  },
  trendBarContainer: {
    height: 40,
    justifyContent: 'flex-end',
    marginBottom: 4,
  },
  trendBar: {
    width: 20,
    borderRadius: 2,
  },
  trendValue: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  categoryContainer: {
    gap: 8,
  },
  categoryItem: {
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  chartContainer: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
});