-- COMPLETE FRESH START - Deploy proper Clerk-compatible schema
-- Run this in Supabase SQL Editor

-- 1. Reset database completely
DROP SCHEMA IF EXISTS public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;
GRANT USAGE ON SCHEMA public TO anon, authenticated, service_role;

-- 2. Clean storage
DELETE FROM storage.objects WHERE bucket_id IN ('task-attachments', 'voice-notes');
DELETE FROM storage.buckets WHERE id IN ('task-attachments', 'voice-notes');

-- 3. Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 4. Create RLS context function for <PERSON> JWT
CREATE OR REPLACE FUNCTION auth.uid() 
RETURNS text 
LANGUAGE sql 
STABLE
AS $$
  SELECT 
    COALESCE(
      current_setting('request.jwt.claims', true)::json ->> 'sub',
      current_setting('request.jwt.sub', true),
      (current_setting('request.headers', true)::json ->> 'x-user-id')
    );
$$;

-- 5. <PERSON><PERSON> custom types
CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high');
CREATE TYPE task_status AS ENUM ('pending', 'in-progress', 'completed', 'cancelled', 'on-hold');

-- 6. Create profiles table
CREATE TABLE profiles (
  id TEXT PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create tasks table
CREATE TABLE tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL,
  date DATE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  priority task_priority NOT NULL DEFAULT 'medium',
  status task_status NOT NULL DEFAULT 'pending',
  completed_at TIMESTAMP WITH TIME ZONE,
  due_date DATE,
  category TEXT,
  tags TEXT[],
  comments JSONB DEFAULT '[]'::jsonb,
  links TEXT[],
  attachments TEXT[],
  voice_notes TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create file_attachments table
CREATE TABLE file_attachments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL,
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  filename TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  storage_path TEXT NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Add indexes
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_date ON tasks(date);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_file_attachments_task_id ON file_attachments(task_id);
CREATE INDEX idx_file_attachments_user_id ON file_attachments(user_id);

-- 10. Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at 
  BEFORE UPDATE ON profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at 
  BEFORE UPDATE ON tasks 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 11. Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_attachments ENABLE ROW LEVEL SECURITY;

-- 12. Create RLS policies that work with Clerk JWT
-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Tasks policies
CREATE POLICY "Users can view own tasks" ON tasks
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own tasks" ON tasks
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tasks" ON tasks
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tasks" ON tasks
  FOR DELETE USING (auth.uid() = user_id);

-- File attachments policies
CREATE POLICY "Users can view own file attachments" ON file_attachments
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own file attachments" ON file_attachments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own file attachments" ON file_attachments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own file attachments" ON file_attachments
  FOR DELETE USING (auth.uid() = user_id);

-- 13. Create storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('task-attachments', 'task-attachments', false) 
ON CONFLICT (id) DO NOTHING;

-- 14. Storage policies
CREATE POLICY "Users can upload own files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'task-attachments' AND 
    auth.uid() = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view own files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'task-attachments' AND 
    auth.uid() = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update own files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'task-attachments' AND 
    auth.uid() = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete own files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'task-attachments' AND 
    auth.uid() = (storage.foldername(name))[1]
  );
