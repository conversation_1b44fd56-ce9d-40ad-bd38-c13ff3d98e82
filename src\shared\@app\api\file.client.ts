import { BaseApiClient } from './base.client';
import { FileUploadResult, StorageUsage, ApiResponse } from '../types';

export class FileApiClient extends BaseApiClient {
  async uploadFile(file: FormData, taskId: string): Promise<ApiResponse<FileUploadResult>> {
    // Override the content type for file uploads
    return this.request<FileUploadResult>(`/files/upload/${taskId}`, {
      method: 'POST',
      body: file,
      headers: {} // Let browser set content-type for FormData
    });
  }

  async deleteFile(fileId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/files/${fileId}`);
  }

  async getTaskFiles(taskId: string): Promise<ApiResponse<FileUploadResult[]>> {
    return this.get<FileUploadResult[]>(`/files/task/${taskId}`);
  }

  async getStorageUsage(userId: string): Promise<ApiResponse<StorageUsage>> {
    return this.get<StorageUsage>(`/files/storage/${userId}`);
  }

  async getFileUrl(fileId: string): Promise<ApiResponse<{ url: string }>> {
    return this.get<{ url: string }>(`/files/${fileId}/url`);
  }

  async getFileMetadata(fileId: string): Promise<ApiResponse<{
    id: string;
    name: string;
    size: number;
    type: string;
    uploadedAt: string;
  }>> {
    return this.get(`/files/${fileId}/metadata`);
  }
}