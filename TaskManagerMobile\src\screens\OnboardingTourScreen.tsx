import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

interface OnboardingTourScreenProps {
  onComplete: () => void;
  onSkip: () => void;
}

const TOUR_STEPS = [
  {
    id: 'welcome',
    title: 'Welcome to Your Task Calendar!',
    description: 'Let\'s take a quick tour to get you started',
    icon: 'hand-left-outline',
    content: 'This quick tour will show you the key features of your task calendar. It will only take about 2 minutes.',
  },
  {
    id: 'calendar',
    title: 'Your Task Calendar',
    description: 'This is where all your tasks are displayed',
    icon: 'calendar-outline',
    content: 'Tap any date to view tasks. Colored dots indicate tasks with different priority levels.',
  },
  {
    id: 'add-task',
    title: 'Adding New Tasks',
    description: 'Multiple ways to create tasks quickly',
    icon: 'add-circle-outline',
    content: 'Use the + button, tap any date, or use voice input to quickly add new tasks.',
  },
  {
    id: 'task-details',
    title: 'Task Organization',
    description: 'Keep your tasks organized with priorities, categories, and more',
    icon: 'list-outline',
    content: 'Set priorities, add categories, due dates, and notes to keep your tasks organized.',
  },
  {
    id: 'settings',
    title: 'Customize Your Experience',
    description: 'Adjust settings to match your preferences',
    icon: 'settings-outline',
    content: 'Customize themes, notifications, and backup options to match your workflow.',
  },
  {
    id: 'complete',
    title: 'You\'re All Set!',
    description: 'Ready to start managing your tasks like a pro',
    icon: 'rocket-outline',
    content: 'You now know the basics! Swipe left on tasks to complete them and check settings for help.',
  },
];

export const OnboardingTourScreen: React.FC<OnboardingTourScreenProps> = ({
  onComplete,
  onSkip,
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => {
    if (currentStep < TOUR_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = ((currentStep + 1) / TOUR_STEPS.length) * 100;
  const step = TOUR_STEPS[currentStep];

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={true}
      onRequestClose={onSkip}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Ionicons name={step.icon as any} size={48} color="#3b82f6" />
            <Text style={styles.title}>{step.title}</Text>
            <Text style={styles.description}>{step.description}</Text>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={styles.contentText}>{step.content}</Text>
          </View>

          {/* Progress */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.progressText}>
              Step {currentStep + 1} of {TOUR_STEPS.length}
            </Text>
          </View>

          {/* Navigation */}
          <View style={styles.navigation}>
            <TouchableOpacity onPress={onSkip}>
              <Text style={styles.skipText}>Skip Tour</Text>
            </TouchableOpacity>

            <View style={styles.buttonGroup}>
              <TouchableOpacity
                style={[styles.navButton, currentStep === 0 && styles.disabledButton]}
                onPress={handlePrevious}
                disabled={currentStep === 0}
              >
                <Ionicons name="chevron-back" size={24} color="#6b7280" />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.navButton, styles.primaryButton]}
                onPress={handleNext}
              >
                <Text style={styles.primaryButtonText}>
                  {currentStep === TOUR_STEPS.length - 1 ? 'Finish' : 'Next'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Step indicators */}
          <View style={styles.stepIndicators}>
            {TOUR_STEPS.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.stepDot,
                  index === currentStep && styles.activeStepDot,
                  index < currentStep && styles.completedStepDot,
                ]}
              />
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    width: width - 40,
    maxHeight: height * 0.8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 8,
    marginTop: 12,
  },
  description: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  content: {
    marginBottom: 24,
  },
  contentText: {
    fontSize: 16,
    color: '#1f2937',
    textAlign: 'center',
    lineHeight: 24,
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#e5e7eb',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3b82f6',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  skipText: {
    fontSize: 14,
    color: '#6b7280',
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
  },
  navButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  primaryButton: {
    backgroundColor: '#3b82f6',
  },
  primaryButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  stepIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 6,
    marginTop: 16,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#d1d5db',
  },
  activeStepDot: {
    backgroundColor: '#3b82f6',
    width: 24,
  },
  completedStepDot: {
    backgroundColor: '#10b981',
  },
});

export default OnboardingTourScreen;