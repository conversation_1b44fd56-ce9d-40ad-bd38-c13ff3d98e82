import { useState, useEffect, useMemo } from 'react';
import { format, isWithinInterval } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { Task } from '@/types/task';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-mobile';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Calendar as CalendarIcon,
  CheckCircle,
  Clock,
  TrendingUp,
  AlertCircle,
  Target,
  BarChart3,
  Activity,
  GripVertical,
  Plus,
  Eye,
  Zap,
  X,
  User,
  ChevronLeft,
  ChevronRight,
  Settings
} from 'lucide-react';
import { ThemeToggle } from './ThemeToggle';

interface AnalyticsDashboardProps {
  tasks: Task[];
  onCardClick: (cardType: string, data: any) => void;
  onAddTask?: () => void;
  onViewTaskDetails?: () => void;
  showHeader?: boolean;
  year?: number;
  onYearChange?: (year: number) => void;
  onSettingsClick?: () => void;
  activeTab?: 'tasks' | 'analytics';
  onTabChange?: (tab: 'tasks' | 'analytics') => void;
  onSignOut?: () => void;
}



interface AnalyticsCard {
  id: string;
  title: string;
  subtitle?: string;
  value: string | number;
  icon: any;
  color: string;
  bgColor: string;
  trend?: any;
  data: any;
}

// Sortable Card Component
interface SortableCardProps {
  card: AnalyticsCard;
  onCardClick: (cardType: string, data: any) => void;
  isDragging?: boolean;
}

const SortableCard: React.FC<SortableCardProps> = ({ card, onCardClick, isDragging }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: card.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: sortableIsDragging ? 0.5 : 1,
  };

  const Icon = card.icon;

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 relative group min-h-[120px]",
        card.bgColor,
        sortableIsDragging && "shadow-lg scale-105"
      )}
      onClick={() => onCardClick(card.id, card.data)}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing p-1 rounded hover:bg-black/10"
        onClick={(e) => e.stopPropagation()}
      >
        <GripVertical className="h-4 w-4 text-muted-foreground" />
      </div>

      <CardContent className="px-2 py-4">
        <div className="flex items-center justify-between">
          <div className="flex-1 pr-8">
            <p className="text-base font-bold text-foreground">
              {card.title}
            </p>
            {card.subtitle && (
              <p className="text-xs text-muted-foreground/80 mb-1">
                {card.subtitle}
              </p>
            )}
            <p className="text-2xl font-bold">{card.value}</p>
            {card.trend && (
              <p className={cn(
                "text-xs",
                card.trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
              )}>
                {card.trend.label}
              </p>
            )}
          </div>
          <Icon className={cn("h-8 w-8", card.color)} />
        </div>
      </CardContent>
    </Card>
  );
};

// Merged Priority Card Component
const MergedPriorityCard: React.FC<{
  analytics: any;
  periodTasks: Task[];
  onCardClick: (cardType: string, data: any) => void;
}> = ({ analytics, periodTasks, onCardClick }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: 'merged-priority' });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: sortableIsDragging ? 0.5 : 1,
  };

  const priorityData = [
    {
      level: 'High',
      count: analytics.high,
      color: 'text-red-600',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      icon: Zap,
      data: periodTasks.filter(task => task.priority === 'high')
    },
    {
      level: 'Medium', 
      count: analytics.medium,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
      icon: BarChart3,
      data: periodTasks.filter(task => task.priority === 'medium')
    },
    {
      level: 'Low',
      count: analytics.low,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      icon: Activity,
      data: periodTasks.filter(task => task.priority === 'low')
    }
  ];

  const totalPriorityTasks = analytics.high + analytics.medium + analytics.low;

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={cn(
        "transition-all duration-200 hover:shadow-md hover:scale-105 relative group min-h-[120px]",
        "bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50",
        sortableIsDragging && "shadow-lg scale-105"
      )}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing p-1 rounded hover:bg-black/10"
        onClick={(e) => e.stopPropagation()}
      >
        <GripVertical className="h-4 w-4 text-muted-foreground" />
      </div>

      <CardContent className="p-0">
        <div className="px-4 pt-4">
          <div className="flex items-center justify-between mb-3">
            <div>
              <p className="text-base font-bold text-foreground">Priority Overview</p>
              <p className="text-xs text-muted-foreground/80">Task distribution</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold">{totalPriorityTasks}</p>
              <p className="text-xs text-muted-foreground">Total</p>
            </div>
          </div>
        </div>
        
        <div className="space-y-2 px-4 pb-4">
          {priorityData.map((priority) => {
            const Icon = priority.icon;
            return (
              <div 
                key={priority.level}
                className="flex items-center justify-between p-2 rounded-md hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onCardClick(`${priority.level.toLowerCase()}-priority`, priority.data);
                }}
              >
                <div className="flex items-center gap-2">
                  <Icon className={cn("h-4 w-4", priority.color)} />
                  <span className="text-sm font-medium">{priority.level}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-bold">{priority.count}</span>
                  {totalPriorityTasks > 0 && (
                    <span className="text-xs text-muted-foreground">
                      ({Math.round((priority.count / totalPriorityTasks) * 100)}%)
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  tasks,
  onCardClick,
  onAddTask,
  onViewTaskDetails,
  showHeader = true,
  year = new Date().getFullYear(),
  onYearChange,
  activeTab = 'analytics',
  onTabChange,
  onSettingsClick,
  onSignOut,
}) => {
  const { isMobile, isTablet } = useScreenSize();

  // Helper function to generate subtitle text for cards
  const getCardSubtitle = (cardType: string) => {
    switch (cardType) {
      case 'total':
        return 'As of today';
      case 'pending':
        return 'As of today';
      case 'completed':
        return 'As of today';
      case 'overdue':
        return 'As of today';
      case 'upcoming':
        return 'Scheduled for future';

      case 'categories':
        return 'As of today';
      default:
        return 'As of today';
    }
  };
  const [cardOrder, setCardOrder] = useState<string[]>([]);
  const [statusCardOrder, setStatusCardOrder] = useState<string[]>([]);
  const [priorityCardOrder, setPriorityCardOrder] = useState<string[]>([]);


  const [validationError, setValidationError] = useState<string | null>(null);



  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Get tasks up to today
  const periodTasks = useMemo(() => {
    const today = new Date();
    today.setHours(23, 59, 59, 999); // Include the entire current day

    return tasks.filter(task => {
      const taskDate = new Date(task.date);
      taskDate.setHours(0, 0, 0, 0); // Normalize task date to start of day
      return taskDate <= today;
    });
  }, [tasks]);

  // Define today outside useMemo so it can be used in card data
  const today = useMemo(() => {
    const date = new Date();
    date.setHours(0, 0, 0, 0);
    return date;
  }, []);

  // Get the reference date based on the current mode
  const referenceDate = useMemo(() => {
    const date = new Date();
    date.setHours(0, 0, 0, 0);
    return date;
  }, []);

  // Memoized analytics calculations for performance
  const analytics = useMemo(() => {
    // For upcoming tasks, we need to get all tasks (not just periodTasks)
    // to find tasks scheduled after the reference date
    const upcomingTasks = tasks.filter(task => {
      if (task.status === 'completed' || task.status === 'cancelled') return false;
      const taskDate = new Date(task.date);
      taskDate.setHours(0, 0, 0, 0);
      return taskDate > referenceDate;
    });

    return {
      total: periodTasks.length,
      // Pending: all tasks that are NOT completed, NOT cancelled (includes on-hold and no status)
      pending: periodTasks.filter(task => 
        task.status !== 'completed' && 
        task.status !== 'cancelled'
      ).length,
      completed: periodTasks.filter(task => task.status === 'completed').length,
      onHold: periodTasks.filter(task => task.status === 'on-hold').length,
      cancelled: periodTasks.filter(task => task.status === 'cancelled').length,
      // Overdue: tasks with due dates before the reference date
      overdue: periodTasks.filter(task => {
        if (task.status === 'completed' || task.status === 'cancelled') return false;
        if (!task.dueDate) return false;
        const dueDate = new Date(task.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate < referenceDate;
      }).length,
      // Upcoming: tasks scheduled after the reference date
      upcoming: upcomingTasks.length,
      high: periodTasks.filter(task => task.priority === 'high').length,
      medium: periodTasks.filter(task => task.priority === 'medium').length,
      low: periodTasks.filter(task => task.priority === 'low').length,
    };
  }, [periodTasks, tasks, referenceDate]);

  const completionRate = useMemo(() =>
    analytics.total > 0 ? Math.round((analytics.completed / analytics.total) * 100) : 0,
    [analytics.total, analytics.completed]
  );

  // Validation function for past date task creation
  const validateTaskCreation = () => {
    const targetDate = getTargetDate();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const target = new Date(targetDate);
    target.setHours(0, 0, 0, 0);

    if (target < today) {
      setValidationError(`Cannot add new tasks to past dates. Selected date: ${format(targetDate, 'MMM d, yyyy')}`);
      return false;
    }

    setValidationError(null);
    return true;
  };

  // Get the target date for task creation based on current mode
  const getTargetDate = () => {
    return new Date(); // Today
  };

  // Handle Add Task button click with validation
  const handleAddTaskClick = () => {
    if (validateTaskCreation()) {
      onAddTask?.();
    }
  };

  // Auto-clear validation error after 5 seconds
  useEffect(() => {
    if (validationError) {
      const timer = setTimeout(() => {
        setValidationError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [validationError]);

  // Get trend indicator - real calculation based on previous period
  const getTrendIndicator = (current: number, label: string) => {
    // For now, return null to hide trends until we have historical data
    // In a real implementation, this would compare with the previous period
    return null;
  };

  // Row 1: Main Overview Cards (3 cards)
  const defaultCards = useMemo(() => [
    {
      id: 'total',
      title: 'Total Tasks',
      subtitle: getCardSubtitle('total'),
      value: analytics.total,
      icon: CalendarIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      trend: getTrendIndicator(analytics.total, 'total'),
      data: periodTasks
    },
    {
      id: 'pending',
      title: 'Pending',
      subtitle: getCardSubtitle('pending'),
      value: analytics.pending,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
      trend: getTrendIndicator(analytics.pending, 'pending'),
      data: periodTasks.filter(task => task.status === 'pending' || !task.status)
    },
    {
      id: 'completed',
      title: 'Completed',
      subtitle: getCardSubtitle('completed'),
      value: analytics.completed,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      trend: getTrendIndicator(analytics.completed, 'completed'),
      data: periodTasks.filter(task => task.status === 'completed')
    }
  ], [analytics.total, analytics.pending, analytics.completed, periodTasks]);

  // Row 2: Status & Timeline Cards (3 cards)
  const defaultStatusCards = useMemo(() => [
    {
      id: 'overdue',
      title: 'Overdue',
      subtitle: getCardSubtitle('overdue'),
      value: analytics.overdue,
      icon: AlertCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      trend: getTrendIndicator(analytics.overdue, 'overdue'),
      data: periodTasks.filter(task => {
        if (task.status === 'completed' || task.status === 'cancelled') return false;
        if (!task.dueDate) return false;
        const dueDate = new Date(task.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate < referenceDate;
      })
    },
    {
      id: 'upcoming',
      title: 'Upcoming',
      subtitle: getCardSubtitle('upcoming'),
      value: analytics.upcoming,
      icon: Clock,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      trend: getTrendIndicator(analytics.upcoming, 'upcoming'),
      data: tasks.filter(task => {
        if (task.status === 'completed' || task.status === 'cancelled') return false;
        const taskDate = new Date(task.date);
        taskDate.setHours(0, 0, 0, 0);
        return taskDate > referenceDate; // All future-scheduled tasks regardless of due date
      })
    },
    {
      id: 'categories',
      title: 'Categories',
      subtitle: getCardSubtitle('categories'),
      value: (() => {
        const categoryStats = periodTasks.reduce((acc, task) => {
          const category = (task as any).category || 'General';
          acc[category] = (acc[category] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        return Object.keys(categoryStats).length;
      })(),
      icon: Target,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      trend: getTrendIndicator(Object.keys(periodTasks.reduce((acc, task) => {
        const category = (task as any).category || 'General';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)).length, 'categories'),
      data: periodTasks // Pass all tasks, let the modal handle category filtering
    }
  ], [analytics.overdue, analytics.upcoming, periodTasks, tasks, referenceDate]);

  // Row 3: Merged Priority Card (1 card)
  const defaultPriorityCards = useMemo(() => [
    {
      id: 'merged-priority',
      title: 'Priority Overview',
      value: analytics.high + analytics.medium + analytics.low,
      icon: Target,
      color: 'text-slate-600',
      bgColor: 'bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50',
      data: periodTasks
    }
  ], [analytics.high, analytics.medium, analytics.low, periodTasks]);



  // Load card order from localStorage on mount
  useEffect(() => {
    const savedMainOrder = localStorage.getItem('analytics-main-card-order');
    const savedStatusOrder = localStorage.getItem('analytics-status-card-order');
    const savedPriorityOrder = localStorage.getItem('analytics-priority-card-order');


    if (savedMainOrder) {
      setCardOrder(JSON.parse(savedMainOrder));
    } else {
      setCardOrder(defaultCards.map(card => card.id));
    }

    if (savedStatusOrder) {
      setStatusCardOrder(JSON.parse(savedStatusOrder));
    } else {
      setStatusCardOrder(defaultStatusCards.map(card => card.id));
    }

    if (savedPriorityOrder) {
      setPriorityCardOrder(JSON.parse(savedPriorityOrder));
    } else {
      setPriorityCardOrder(defaultPriorityCards.map(card => card.id));
    }


  }, []); // Remove dependencies to prevent infinite loop

  // Initialize card orders when arrays are available
  useEffect(() => {
    if (defaultCards.length > 0 && cardOrder.length === 0) {
      setCardOrder(defaultCards.map(card => card.id));
    }
    if (defaultStatusCards.length > 0 && statusCardOrder.length === 0) {
      setStatusCardOrder(defaultStatusCards.map(card => card.id));
    }
    if (defaultPriorityCards.length > 0 && priorityCardOrder.length === 0) {
      setPriorityCardOrder(defaultPriorityCards.map(card => card.id));
    }
  }, [defaultCards, defaultStatusCards, defaultPriorityCards, cardOrder.length, statusCardOrder.length, priorityCardOrder.length]);

  // Save card order to localStorage when it changes
  useEffect(() => {
    if (cardOrder.length > 0) {
      localStorage.setItem('analytics-main-card-order', JSON.stringify(cardOrder));
    }
  }, [cardOrder]);

  useEffect(() => {
    if (statusCardOrder.length > 0) {
      localStorage.setItem('analytics-status-card-order', JSON.stringify(statusCardOrder));
    }
  }, [statusCardOrder]);

  useEffect(() => {
    if (priorityCardOrder.length > 0) {
      localStorage.setItem('analytics-priority-card-order', JSON.stringify(priorityCardOrder));
    }
  }, [priorityCardOrder]);



  // Sort cards according to saved order
  const sortedCards = cardOrder.length > 0
    ? cardOrder.map(id => defaultCards.find(card => card.id === id)).filter(Boolean) as AnalyticsCard[]
    : defaultCards;

  const sortedStatusCards = statusCardOrder.length > 0
    ? statusCardOrder.map(id => defaultStatusCards.find(card => card.id === id)).filter(Boolean) as AnalyticsCard[]
    : defaultStatusCards;

  const sortedPriorityCards = priorityCardOrder.length > 0
    ? priorityCardOrder.map(id => defaultPriorityCards.find(card => card.id === id)).filter(Boolean) as AnalyticsCard[]
    : defaultPriorityCards;



  // Handle drag end for main cards
  const handleMainDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = cardOrder.indexOf(active.id as string);
      const newIndex = cardOrder.indexOf(over.id as string);
      setCardOrder(arrayMove(cardOrder, oldIndex, newIndex));
    }
  };

  // Handle drag end for status cards
  const handleStatusDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = statusCardOrder.indexOf(active.id as string);
      const newIndex = statusCardOrder.indexOf(over.id as string);
      setStatusCardOrder(arrayMove(statusCardOrder, oldIndex, newIndex));
    }
  };

  // Handle drag end for priority cards
  const handlePriorityDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = priorityCardOrder.indexOf(active.id as string);
      const newIndex = priorityCardOrder.indexOf(over.id as string);
      setPriorityCardOrder(arrayMove(priorityCardOrder, oldIndex, newIndex));
    }
  };



  return (
    <div
      data-analytics-dashboard
      key={`analytics-${new Date().toISOString()}`}
      className={cn(
        "w-full mx-auto h-[calc(100vh-150px)] overflow-y-auto panel-scrollbar",
        isMobile ? "max-w-full px-2" : "max-w-4xl"
      )}
    >
      {/* Header with year navigation, tabs, and control buttons - when Analytics tab is active */}
      {showHeader && onTabChange && (
        <div className="flex items-center justify-between mb-4">
          <h2 className={cn(
            "text-2xl font-bold",
            year === new Date().getFullYear() && "text-green-600 dark:text-green-400"
          )}>
            Analytics Dashboard {year}
          </h2>
          <div className="flex items-center gap-2">
            {onYearChange && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onYearChange(year - 1)}
                  className={cn(
                    "touch-manipulation focus-ring",
                    isMobile && "btn-touch px-3 py-2"
                  )}
                  aria-label={`Go to year ${year - 1}`}
                >
                  <ChevronLeft className="h-4 w-4" />
                  {year - 1}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onYearChange(year + 1)}
                  className={cn(
                    "touch-manipulation focus-ring",
                    year === new Date().getFullYear() && "bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700",
                    isMobile && "btn-touch px-3 py-2"
                  )}
                  aria-label={`Go to year ${year + 1}`}
                >
                  {year + 1}
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </>
            )}
            
            {/* Tasks/Analytics Tabs */}
            <div className="flex rounded-lg bg-muted p-1 ml-2">
              <Button
                variant={activeTab === 'tasks' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onTabChange('tasks')}
              >
                Tasks
              </Button>
              <Button
                variant={activeTab === 'analytics' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onTabChange('analytics')}
              >
                Analytics
              </Button>
            </div>
            
            <div className="flex items-center gap-1 ml-2">
              <ThemeToggle />
              {onSettingsClick && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onSettingsClick}
                  className="touch-manipulation focus-ring p-2"
                  title="Settings"
                  aria-label="Open settings"
                >
                  <Settings className="h-4 w-4" />
                </Button>
              )}
              {onSignOut && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onSignOut}
                  className="touch-manipulation focus-ring p-2"
                  title="Sign Out"
                  aria-label="Sign out"
                >
                  <User className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Analytics Controls - Only show if showHeader is true */}
      {showHeader && !onTabChange && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Analytics Dashboard</h3>
          <div className="flex items-center gap-2">



            

          {/* Add Task Button */}
          {onAddTask && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddTaskClick}
              className={cn(
                "flex items-center gap-2 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 touch-manipulation focus-ring",
                isMobile && "btn-touch px-4 py-3"
              )}
              aria-label="Add a new task"
            >
              <Plus className="h-4 w-4" />
              Add Task
            </Button>
          )}

          {/* View Task Details Button */}
          {onViewTaskDetails && (
            <Button
              variant="outline"
              size="sm"
              onClick={onViewTaskDetails}
              className="flex items-center gap-2 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <Eye className="h-4 w-4" />
              View Task Details
            </Button>
          )}
          </div>
        </div>
      )}

      {/* Validation Error Display */}
      {validationError && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
              <p className="text-red-700 dark:text-red-300 font-medium">
                Cannot Add Task to Past Date
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setValidationError(null)}
              className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
              aria-label="Dismiss error"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-red-600 dark:text-red-400 text-sm mt-2">
            {validationError}
          </p>
          <p className="text-red-500 dark:text-red-400 text-xs mt-1">
            Please select today's date or a future date to add new tasks.
          </p>
        </div>
      )}

      {/* Row 1: Main Overview Cards */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleMainDragEnd}
      >
        <SortableContext items={cardOrder} strategy={rectSortingStrategy}>
          <div className={cn(
            "grid gap-4 mb-4 pt-4",
            isMobile ? "grid-cols-1" : isTablet ? "grid-cols-2" : "grid-cols-3"
          )}>
            {sortedCards.map((card) => (
              <SortableCard
                key={card.id}
                card={card}
                onCardClick={onCardClick}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>

      {/* Row 2: Status & Timeline Cards */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleStatusDragEnd}
      >
        <SortableContext items={statusCardOrder} strategy={rectSortingStrategy}>
          <div className={cn(
            "grid gap-4 mb-4 pt-2",
            isMobile ? "grid-cols-1" : isTablet ? "grid-cols-2" : "grid-cols-3"
          )}>
            {sortedStatusCards.map((card) => (
              <SortableCard
                key={card.id}
                card={card}
                onCardClick={onCardClick}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>

      {/* Row 3: Merged Priority Card */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handlePriorityDragEnd}
      >
        <SortableContext items={priorityCardOrder} strategy={rectSortingStrategy}>
          <div className={cn(
            "grid gap-4 pt-2",
            isMobile ? "grid-cols-1" : isTablet ? "grid-cols-1" : "grid-cols-1"
          )}>
            {sortedPriorityCards.map((card) => (
              card.id === 'merged-priority' ? (
                <MergedPriorityCard
                  key={card.id}
                  analytics={analytics}
                  periodTasks={periodTasks}
                  onCardClick={onCardClick}
                />
              ) : (
                <SortableCard
                  key={card.id}
                  card={card}
                  onCardClick={onCardClick}
                />
              )
            ))}
          </div>
        </SortableContext>
      </DndContext>


    </div>
  );
};
