import { Task } from '@/types/task';

export interface OnboardingProgress {
  currentStep: 'signup' | 'welcome' | 'tour' | 'complete';
  completedSteps: string[];
  hasCompletedOnboarding: boolean;
  samplesLoaded: boolean;
  tourCompleted: boolean;
  startedAt: string;
  completedAt?: string;
}

export class OnboardingService {
  private static STORAGE_KEY = 'onboarding-progress';
  private static COMPLETED_KEY = 'onboarding-completed';

  /**
   * Check if user has completed onboarding
   */
  static hasCompletedOnboarding(userId: string): boolean {
    try {
      const completed = localStorage.getItem(`${this.COMPLETED_KEY}-${userId}`);
      return completed === 'true';
    } catch (error) {
      console.error('Error checking onboarding completion:', error);
      return false;
    }
  }

  /**
   * Mark onboarding as completed
   */
  static markOnboardingComplete(userId: string): void {
    try {
      localStorage.setItem(`${this.COMPLETED_KEY}-${userId}`, 'true');
      
      // Update progress
      const progress = this.getProgress(userId);
      progress.hasCompletedOnboarding = true;
      progress.completedAt = new Date().toISOString();
      progress.currentStep = 'complete';
      this.saveProgress(userId, progress);
    } catch (error) {
      console.error('Error marking onboarding complete:', error);
    }
  }

  /**
   * Get onboarding progress for user
   */
  static getProgress(userId: string): OnboardingProgress {
    try {
      const stored = localStorage.getItem(`${this.STORAGE_KEY}-${userId}`);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error getting onboarding progress:', error);
    }

    // Default progress
    return {
      currentStep: 'signup',
      completedSteps: [],
      hasCompletedOnboarding: false,
      samplesLoaded: false,
      tourCompleted: false,
      startedAt: new Date().toISOString(),
    };
  }

  /**
   * Save onboarding progress
   */
  static saveProgress(userId: string, progress: OnboardingProgress): void {
    try {
      localStorage.setItem(`${this.STORAGE_KEY}-${userId}`, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
    }
  }

  /**
   * Update progress step
   */
  static updateProgress(userId: string, step: OnboardingProgress['currentStep']): void {
    const progress = this.getProgress(userId);
    progress.currentStep = step;
    
    if (!progress.completedSteps.includes(step)) {
      progress.completedSteps.push(step);
    }

    this.saveProgress(userId, progress);
  }

  /**
   * Generate sample tasks for new users
   */
  static generateSampleTasks(): Omit<Task, 'id' | 'createdAt' | 'updatedAt'>[] {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
    
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    return [
      {
        date: today.toISOString().split('T')[0],
        title: 'Welcome to TaskCalendar! 🎉',
        description: 'You\'ve successfully created your first task! This is a sample task to help you get started.',
        priority: 'high',
        status: 'pending',
        category: 'Getting Started',
        tags: ['welcome', 'first-task', 'sample'],
        comments: [
          {
            id: '1',
            text: 'Welcome to your task calendar! You can add comments to any task.',
            timestamp: new Date().toISOString(),
          }
        ]
      },
      {
        date: today.toISOString().split('T')[0],
        title: 'Morning Workout 💪',
        description: 'Start your day with a 30-minute workout routine. Exercise is great for productivity!',
        priority: 'medium',
        status: 'pending',
        category: 'Health & Fitness',
        tags: ['fitness', 'morning', 'health'],
        dueDate: new Date(today.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
      },
      {
        date: today.toISOString().split('T')[0],
        title: 'Review Project Proposal',
        description: 'Review the Q1 project proposal and prepare feedback for the team meeting.',
        priority: 'high',
        status: 'pending',
        category: 'Work',
        tags: ['project', 'review', 'meeting'],
        dueDate: new Date(today.getTime() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours from now
      },
      {
        date: tomorrow.toISOString().split('T')[0],
        title: 'Grocery Shopping 🛒',
        description: 'Buy ingredients for the weekend meal prep. Don\'t forget the fresh vegetables!',
        priority: 'low',
        status: 'pending',
        category: 'Personal',
        tags: ['shopping', 'food', 'weekend'],
        links: ['https://example.com/shopping-list']
      },
      {
        date: tomorrow.toISOString().split('T')[0],
        title: 'Team Stand-up Meeting',
        description: 'Daily team sync to discuss progress and blockers.',
        priority: 'medium',
        status: 'pending',
        category: 'Work',
        tags: ['meeting', 'standup', 'team'],
        dueDate: new Date(tomorrow.getTime() + 10 * 60 * 60 * 1000).toISOString(), // 10 AM tomorrow
      },
      {
        date: dayAfterTomorrow.toISOString().split('T')[0],
        title: 'Doctor Appointment 🏥',
        description: 'Annual health check-up at 3 PM. Bring insurance card and previous test results.',
        priority: 'high',
        status: 'pending',
        category: 'Health',
        tags: ['appointment', 'health', 'checkup'],
        dueDate: new Date(dayAfterTomorrow.getTime() + 15 * 60 * 60 * 1000).toISOString(), // 3 PM
      },
      {
        date: dayAfterTomorrow.toISOString().split('T')[0],
        title: 'Call Mom 📞',
        description: 'Weekly check-in call with mom. Ask about her garden and the new book she\'s reading.',
        priority: 'medium',
        status: 'pending',
        category: 'Personal',
        tags: ['family', 'call', 'weekly'],
      },
      {
        date: nextWeek.toISOString().split('T')[0],
        title: 'Plan Weekend Trip 🗺️',
        description: 'Research and plan the weekend getaway. Check weather, book accommodation, and create itinerary.',
        priority: 'low',
        status: 'pending',
        category: 'Travel',
        tags: ['travel', 'planning', 'weekend'],
        links: ['https://example.com/travel-guide']
      },
      {
        date: nextWeek.toISOString().split('T')[0],
        title: 'Complete Monthly Report',
        description: 'Compile and submit the monthly progress report. Include metrics and next month\'s goals.',
        priority: 'high',
        status: 'pending',
        category: 'Work',
        tags: ['report', 'monthly', 'deadlines'],
        dueDate: new Date(nextWeek.getTime() + 17 * 60 * 60 * 1000).toISOString(), // 5 PM
      },
      {
        date: nextWeek.toISOString().split('T')[0],
        title: 'Learn TaskCalendar Features 📚',
        description: 'Explore advanced features like voice notes, file attachments, and analytics dashboard.',
        priority: 'medium',
        status: 'pending',
        category: 'Learning',
        tags: ['learning', 'features', 'productivity'],
        comments: [
          {
            id: '2',
            text: 'Try uploading a file attachment to see how it works!',
            timestamp: new Date().toISOString(),
          }
        ]
      }
    ];
  }

  /**
   * Mark samples as loaded
   */
  static markSamplesLoaded(userId: string): void {
    const progress = this.getProgress(userId);
    progress.samplesLoaded = true;
    this.saveProgress(userId, progress);
  }

  /**
   * Mark tour as completed
   */
  static markTourCompleted(userId: string): void {
    const progress = this.getProgress(userId);
    progress.tourCompleted = true;
    this.saveProgress(userId, progress);
  }

  /**
   * Reset onboarding (for testing/debugging)
   */
  static resetOnboarding(userId: string): void {
    try {
      localStorage.removeItem(`${this.COMPLETED_KEY}-${userId}`);
      localStorage.removeItem(`${this.STORAGE_KEY}-${userId}`);
    } catch (error) {
      console.error('Error resetting onboarding:', error);
    }
  }

  /**
   * Get onboarding tips based on user progress
   */
  static getContextualTips(userId: string): string[] {
    const progress = this.getProgress(userId);
    const tips: string[] = [];

    if (!progress.samplesLoaded) {
      tips.push('💡 Try loading sample tasks to see how the calendar works');
    }

    if (!progress.tourCompleted) {
      tips.push('🎯 Take the interactive tour to learn all the features');
    }

    if (progress.hasCompletedOnboarding) {
      tips.push('🚀 Use Ctrl+N to quickly add new tasks');
      tips.push('📊 Check the analytics dashboard to track your progress');
      tips.push('🔄 Your tasks sync automatically across all devices');
    }

    return tips;
  }

  /**
   * Get user's onboarding stats
   */
  static getStats(userId: string): {
    daysWithApp: number;
    completedSteps: number;
    totalSteps: number;
    completionRate: number;
  } {
    const progress = this.getProgress(userId);
    const startDate = new Date(progress.startedAt);
    const today = new Date();
    const daysWithApp = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const totalSteps = 4; // signup, welcome, tour, complete
    const completedSteps = progress.completedSteps.length;
    const completionRate = Math.round((completedSteps / totalSteps) * 100);

    return {
      daysWithApp,
      completedSteps,
      totalSteps,
      completionRate,
    };
  }
}
