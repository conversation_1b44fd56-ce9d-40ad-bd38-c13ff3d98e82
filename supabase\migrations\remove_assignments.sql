-- Remove assignment-related features from the database
-- This migration removes the task_assignments table and assignment-related columns from tasks

-- Drop task_assignments table and all related objects
DROP TABLE IF EXISTS task_assignments CASCADE;

-- Remove assignment-related columns from tasks table
ALTER TABLE tasks 
DROP COLUMN IF EXISTS assigned_to,
DROP COLUMN IF EXISTS assignment_status,
DROP COLUMN IF EXISTS assignment_comments;

-- Remove assignment-related fields from profiles
ALTER TABLE profiles 
DROP COLUMN IF EXISTS accept_assignments;

-- Update notification_preferences to remove assignment notifications
UPDATE profiles 
SET notification_preferences = notification_preferences - 'assignment'
WHERE notification_preferences ? 'assignment';

-- Remove assignment-related indexes (if they exist on tasks table)
DROP INDEX IF EXISTS idx_tasks_assigned_to;
DROP INDEX IF EXISTS idx_tasks_assignment_status;

-- Remove assignment-related triggers and functions
DROP TRIGGER IF EXISTS notify_assignment_created ON task_assignments;
DROP TRIGGER IF EXISTS notify_assignment_updated ON task_assignments;
DROP FUNCTION IF EXISTS notify_assignment_changes();

-- Clean up any remaining assignment-related policies
DROP POLICY IF EXISTS "Users can view their assignments" ON task_assignments;
DROP POLICY IF EXISTS "Users can create assignments" ON task_assignments;
DROP POLICY IF EXISTS "Users can update assignment status" ON task_assignments;