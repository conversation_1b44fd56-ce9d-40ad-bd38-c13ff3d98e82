import React, { useState, useEffect } from 'react';
import { <PERSON>, Moon, <PERSON>, <PERSON><PERSON>, Eye, Contrast } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

type Theme = 'light' | 'dark' | 'system';

interface ThemeSettings {
  theme: Theme;
  highContrast: boolean;
  reducedMotion: boolean;
}

interface ExplorerThemeSectionProps {
  className?: string;
}

export const ExplorerThemeSection: React.FC<ExplorerThemeSectionProps> = ({
  className
}) => {
  const [themeSettings, setThemeSettings] = useState<ThemeSettings>({
    theme: 'system',
    highContrast: false,
    reducedMotion: false
  });

  // Load theme settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('app-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setThemeSettings({
          theme: parsed.theme || 'system',
          highContrast: parsed.highContrast || false,
          reducedMotion: parsed.reducedMotion || false
        });
      } catch (error) {
        console.error('Error loading theme settings:', error);
      }
    }
  }, []);

  // Apply theme changes
  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement;

    if (newTheme === 'dark') {
      root.classList.add('dark');
    } else if (newTheme === 'light') {
      root.classList.remove('dark');
    } else {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    }

    // Save to settings
    const currentSettings = localStorage.getItem('app-settings');
    let settings = {};
    if (currentSettings) {
      try {
        settings = JSON.parse(currentSettings);
      } catch {
        settings = {};
      }
    }
    
    const updatedSettings = { ...settings, theme: newTheme };
    localStorage.setItem('app-settings', JSON.stringify(updatedSettings));
    
    setThemeSettings(prev => ({ ...prev, theme: newTheme }));
  };

  const updateAccessibilitySetting = (key: 'highContrast' | 'reducedMotion', value: boolean) => {
    const currentSettings = localStorage.getItem('app-settings');
    let settings = {};
    if (currentSettings) {
      try {
        settings = JSON.parse(currentSettings);
      } catch {
        settings = {};
      }
    }
    
    const updatedSettings = { ...settings, [key]: value };
    localStorage.setItem('app-settings', JSON.stringify(updatedSettings));
    
    setThemeSettings(prev => ({ ...prev, [key]: value }));
    
    // Apply accessibility settings
    const root = document.documentElement;
    if (key === 'highContrast') {
      if (value) {
        root.classList.add('high-contrast');
      } else {
        root.classList.remove('high-contrast');
      }
    }
    
    if (key === 'reducedMotion') {
      if (value) {
        root.classList.add('reduce-motion');
      } else {
        root.classList.remove('reduce-motion');
      }
    }
  };

  const themeOptions = [
    { value: 'light' as Theme, icon: Sun, label: 'Light', color: '#f59e0b' },
    { value: 'dark' as Theme, icon: Moon, label: 'Dark', color: '#6366f1' },
    { value: 'system' as Theme, icon: Monitor, label: 'Auto', color: '#8b5cf6' }
  ];

  const themeCards = [
    {
      id: 'theme-selector',
      title: 'Theme Mode',
      icon: Palette,
      content: (
        <div className="space-y-2">
          <div className="grid grid-cols-3 gap-1">
            {themeOptions.map(({ value, icon: Icon, label, color }) => (
              <Button
                key={value}
                variant={themeSettings.theme === value ? 'default' : 'outline'}
                size="sm"
                onClick={() => applyTheme(value)}
                className={cn(
                  "h-8 text-xs flex flex-col items-center justify-center p-1",
                  themeSettings.theme === value && "ring-2 ring-primary"
                )}
              >
                <Icon className="h-3 w-3 mb-1" style={{ color: themeSettings.theme === value ? 'currentColor' : color }} />
                <span className="text-xs leading-none">{label}</span>
              </Button>
            ))}
          </div>
          
          <div className="text-center">
            <Badge variant="secondary" className="text-xs">
              Current: {themeSettings.theme}
            </Badge>
          </div>
        </div>
      )
    },
    {
      id: 'accessibility',
      title: 'Accessibility',
      icon: Eye,
      content: (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Contrast className="h-3 w-3" />
              <Label className="text-xs">High Contrast</Label>
            </div>
            <Switch
              checked={themeSettings.highContrast}
              onCheckedChange={(checked) => updateAccessibilitySetting('highContrast', checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Eye className="h-3 w-3" />
              <Label className="text-xs">Reduced Motion</Label>
            </div>
            <Switch
              checked={themeSettings.reducedMotion}
              onCheckedChange={(checked) => updateAccessibilitySetting('reducedMotion', checked)}
            />
          </div>
        </div>
      )
    }
  ];

  return (
    <div className={cn("space-y-3", className)}>
      {themeCards.map((card) => {
        const Icon = card.icon;
        return (
          <Card key={card.id} className="bg-card/50 backdrop-blur-sm border-l-4 border-l-purple-500">
            <CardContent className="p-0">
              <div className="bg-green-50 dark:bg-green-900/20 px-2 py-2 rounded-t-md">
                <div className="text-xs font-medium flex items-center gap-2">
                  <div className="p-1 rounded-md bg-purple-500/20">
                    <Icon className="h-3 w-3 text-purple-500" />
                  </div>
                  {card.title}
                </div>
              </div>
              <div className="p-2">
                {card.content}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};