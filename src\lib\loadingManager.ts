// Centralized Loading State Management System
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// Loading state types
export interface LoadingState {
  id: string;
  label?: string;
  progress?: number; // 0-100 for progress bars
  type: LoadingType;
  startTime: Date;
  timeout?: number; // Auto-clear after ms
}

export enum LoadingType {
  GLOBAL = 'GLOBAL',           // Full screen overlay
  COMPONENT = 'COMPONENT',     // Component-level loading
  BUTTON = 'BUTTON',           // Button loading state
  BACKGROUND = 'BACKGROUND',   // Silent background operation
  PROGRESS = 'PROGRESS'        // Progress-based loading
}

interface LoadingStore {
  loadingStates: Map<string, LoadingState>;
  
  // Actions
  startLoading: (id: string, options?: Partial<LoadingState>) => void;
  stopLoading: (id: string) => void;
  updateProgress: (id: string, progress: number) => void;
  clearAllLoading: () => void;
  
  // Getters
  isLoading: (id: string) => boolean;
  getLoadingState: (id: string) => LoadingState | undefined;
  getLoadingStates: () => LoadingState[];
  hasGlobalLoading: () => boolean;
}

export const useLoadingStore = create<LoadingStore>()(
  subscribeWithSelector((set, get) => ({
    loadingStates: new Map(),

    startLoading: (id: string, options: Partial<LoadingState> = {}) => {
      set((state) => {
        const newMap = new Map(state.loadingStates);
        const loadingState: LoadingState = {
          id,
          label: options.label,
          progress: options.progress,
          type: options.type || LoadingType.COMPONENT,
          startTime: new Date(),
          timeout: options.timeout
        };
        
        newMap.set(id, loadingState);
        
        // Set timeout if specified
        if (options.timeout) {
          setTimeout(() => {
            get().stopLoading(id);
          }, options.timeout);
        }
        
        return { loadingStates: newMap };
      });
    },

    stopLoading: (id: string) => {
      set((state) => {
        const newMap = new Map(state.loadingStates);
        newMap.delete(id);
        return { loadingStates: newMap };
      });
    },

    updateProgress: (id: string, progress: number) => {
      set((state) => {
        const newMap = new Map(state.loadingStates);
        const existingState = newMap.get(id);
        if (existingState) {
          newMap.set(id, { ...existingState, progress });
        }
        return { loadingStates: newMap };
      });
    },

    clearAllLoading: () => {
      set({ loadingStates: new Map() });
    },

    isLoading: (id: string) => {
      return get().loadingStates.has(id);
    },

    getLoadingState: (id: string) => {
      return get().loadingStates.get(id);
    },

    getLoadingStates: () => {
      return Array.from(get().loadingStates.values());
    },

    hasGlobalLoading: () => {
      return Array.from(get().loadingStates.values())
        .some(state => state.type === LoadingType.GLOBAL);
    }
  }))
);

// Loading Manager Class
class LoadingManager {
  private store = useLoadingStore;

  /**
   * Start a loading state
   */
  start(id: string, options?: {
    label?: string;
    type?: LoadingType;
    timeout?: number;
    progress?: number;
  }) {
    this.store.getState().startLoading(id, options);
  }

  /**
   * Stop a loading state
   */
  stop(id: string) {
    this.store.getState().stopLoading(id);
  }

  /**
   * Update progress for a loading state
   */
  updateProgress(id: string, progress: number) {
    this.store.getState().updateProgress(id, Math.max(0, Math.min(100, progress)));
  }

  /**
   * Check if something is loading
   */
  isLoading(id: string): boolean {
    return this.store.getState().isLoading(id);
  }

  /**
   * Get loading state details
   */
  getState(id: string): LoadingState | undefined {
    return this.store.getState().getLoadingState(id);
  }

  /**
   * Clear all loading states
   */
  clearAll() {
    this.store.getState().clearAllLoading();
  }

  /**
   * Wrap an async operation with loading state
   */
  async withLoading<T>(
    id: string,
    operation: () => Promise<T>,
    options?: {
      label?: string;
      type?: LoadingType;
      timeout?: number;
    }
  ): Promise<T> {
    this.start(id, options);
    try {
      return await operation();
    } finally {
      this.stop(id);
    }
  }

  /**
   * Wrap an async operation with progress updates
   */
  async withProgress<T>(
    id: string,
    operation: (updateProgress: (progress: number) => void) => Promise<T>,
    options?: {
      label?: string;
      timeout?: number;
    }
  ): Promise<T> {
    this.start(id, { 
      ...options, 
      type: LoadingType.PROGRESS, 
      progress: 0 
    });
    
    try {
      return await operation((progress) => this.updateProgress(id, progress));
    } finally {
      this.stop(id);
    }
  }
}

// Export singleton instance
export const loadingManager = new LoadingManager();

// Common loading IDs (to avoid typos and ensure consistency)
export const LoadingIds = {
  // Global operations
  APP_INITIALIZATION: 'app-init',
  USER_AUTHENTICATION: 'user-auth',
  
  // Task operations
  TASK_CREATION: 'task-create',
  TASK_UPDATE: 'task-update',
  TASK_DELETION: 'task-delete',
  TASK_FETCH: 'task-fetch',
  TASK_BULK_OPERATION: 'task-bulk',
  
  // File operations
  FILE_UPLOAD: 'file-upload',
  FILE_DOWNLOAD: 'file-download',
  FILE_DELETE: 'file-delete',
  
  // Voice operations
  VOICE_RECORDING: 'voice-record',
  VOICE_PROCESSING: 'voice-process',
  
  // Analytics
  ANALYTICS_FETCH: 'analytics-fetch',
  ANALYTICS_EXPORT: 'analytics-export',
  
  // Team operations

  
  // Settings
  SETTINGS_SAVE: 'settings-save',
  PROFILE_UPDATE: 'profile-update',
  
  // Subscription
  SUBSCRIPTION_UPDATE: 'subscription-update',
  PAYMENT_PROCESS: 'payment-process'
} as const;

// React hooks for easy component integration
export const useLoading = (id: string) => {
  const isLoading = useLoadingStore(state => state.isLoading(id));
  const loadingState = useLoadingStore(state => state.getLoadingState(id));
  
  return {
    isLoading,
    loadingState,
    start: (options?: Partial<LoadingState>) => loadingManager.start(id, options),
    stop: () => loadingManager.stop(id),
    updateProgress: (progress: number) => loadingManager.updateProgress(id, progress)
  };
};

export const useGlobalLoading = () => {
  const hasGlobalLoading = useLoadingStore(state => state.hasGlobalLoading());
  const globalStates = useLoadingStore(state => 
    state.getLoadingStates().filter(s => s.type === LoadingType.GLOBAL)
  );
  
  return {
    isGlobalLoading: hasGlobalLoading,
    globalLoadingStates: globalStates
  };
};

export const useLoadingStates = () => {
  const loadingStates = useLoadingStore(state => state.getLoadingStates());
  
  return {
    loadingStates,
    hasAnyLoading: loadingStates.length > 0,
    loadingCount: loadingStates.length
  };
};

// Utility functions for common loading scenarios
export const withTaskLoading = <T>(
  operation: () => Promise<T>,
  taskType: 'create' | 'update' | 'delete' | 'fetch' = 'fetch'
): Promise<T> => {
  const loadingId = `task-${taskType}-${Date.now()}`;
  return loadingManager.withLoading(loadingId, operation, {
    label: `${taskType.charAt(0).toUpperCase() + taskType.slice(1)}ing task...`,
    type: LoadingType.COMPONENT
  });
};

export const withFileLoading = <T>(
  operation: () => Promise<T>,
  fileType: 'upload' | 'download' | 'delete' = 'upload'
): Promise<T> => {
  const loadingId = `file-${fileType}-${Date.now()}`;
  return loadingManager.withLoading(loadingId, operation, {
    label: `${fileType.charAt(0).toUpperCase() + fileType.slice(1)}ing file...`,
    type: LoadingType.COMPONENT
  });
};

export const withAnalyticsLoading = <T>(
  operation: () => Promise<T>
): Promise<T> => {
  return loadingManager.withLoading(LoadingIds.ANALYTICS_FETCH, operation, {
    label: 'Loading analytics...',
    type: LoadingType.COMPONENT
  });
};
