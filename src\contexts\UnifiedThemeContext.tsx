import { createContext, useContext, ReactNode, useState, useEffect } from 'react';

export type Theme = 'light' | 'dark' | 'system';
export type ColorScheme = 'blue' | 'green' | 'purple' | 'orange' | 'pink';
export type FontSize = 'small' | 'medium' | 'large' | 'xl';
export type Spacing = 'compact' | 'normal' | 'comfortable';

export interface AccessibilitySettings {
  reducedMotion: boolean;
  highContrast: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
  announceChanges: boolean;
}

export interface ThemeSettings {
  theme: Theme;
  colorScheme: ColorScheme;
  fontSize: FontSize;
  spacing: Spacing;
  accessibility: AccessibilitySettings;
}

interface UnifiedThemeContextType {
  settings: ThemeSettings;
  updateTheme: (theme: Theme) => void;
  updateColorScheme: (scheme: ColorScheme) => void;
  updateFontSize: (size: FontSize) => void;
  updateSpacing: (spacing: Spacing) => void;
  updateAccessibility: (accessibility: Partial<AccessibilitySettings>) => void;
  resetToDefaults: () => void;
}

const defaultSettings: ThemeSettings = {
  theme: 'system',
  colorScheme: 'blue',
  fontSize: 'medium',
  spacing: 'normal',
  accessibility: {
    reducedMotion: false,
    highContrast: false,
    screenReader: false,
    keyboardNavigation: true,
    focusIndicators: true,
    announceChanges: false
  }
};

const UnifiedThemeContext = createContext<UnifiedThemeContextType | undefined>(undefined);

export function UnifiedThemeProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<ThemeSettings>(() => {
    // Load from localStorage or use defaults
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('unified-theme-settings');
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return { ...defaultSettings, ...parsed };
        } catch (error) {
          console.error('Failed to parse theme settings:', error);
        }
      }
    }
    return defaultSettings;
  });

  // Detect system preferences
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Detect system theme preference
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        if (settings.theme === 'system') {
          applyTheme(mediaQuery.matches ? 'dark' : 'light');
        }
      };

      handleChange();
      mediaQuery.addEventListener('change', handleChange);

      // Detect reduced motion preference
      const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      if (motionQuery.matches) {
        setSettings(prev => ({
          ...prev,
          accessibility: { ...prev.accessibility, reducedMotion: true }
        }));
      }

      // Detect high contrast preference
      const contrastQuery = window.matchMedia('(prefers-contrast: high)');
      if (contrastQuery.matches) {
        setSettings(prev => ({
          ...prev,
          accessibility: { ...prev.accessibility, highContrast: true }
        }));
      }

      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, [settings.theme]);

  // Apply theme to document
  useEffect(() => {
    applyThemeToDocument(settings);
    
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('unified-theme-settings', JSON.stringify(settings));
    }
  }, [settings]);

  const applyTheme = (theme: 'light' | 'dark') => {
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
  };

  const applyThemeToDocument = (settings: ThemeSettings) => {
    const root = document.documentElement;

    // Apply theme
    if (settings.theme === 'system') {
      const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      applyTheme(systemDark ? 'dark' : 'light');
    } else {
      applyTheme(settings.theme);
    }

    // Apply color scheme
    root.setAttribute('data-color-scheme', settings.colorScheme);

    // Apply font size
    root.setAttribute('data-font-size', settings.fontSize);

    // Apply spacing
    root.setAttribute('data-spacing', settings.spacing);

    // Apply accessibility settings
    Object.entries(settings.accessibility).forEach(([key, value]) => {
      root.setAttribute(`data-a11y-${key.toLowerCase()}`, value.toString());
    });

    // Add CSS custom properties
    const colorSchemes = {
      blue: { primary: '221 83% 53%', secondary: '221 14% 93%', accent: '221 83% 53%' },
      green: { primary: '142 76% 36%', secondary: '138 76% 97%', accent: '142 76% 36%' },
      purple: { primary: '263 70% 50%', secondary: '263 69% 97%', accent: '263 70% 50%' },
      orange: { primary: '25 95% 53%', secondary: '25 100% 97%', accent: '25 95% 53%' },
      pink: { primary: '336 75% 40%', secondary: '336 100% 97%', accent: '336 75% 40%' }
    };

    const fontSizes = {
      small: { base: '14px', scale: '0.9' },
      medium: { base: '16px', scale: '1' },
      large: { base: '18px', scale: '1.1' },
      xl: { base: '20px', scale: '1.2' }
    };

    const spacings = {
      compact: { scale: '0.8' },
      normal: { scale: '1' },
      comfortable: { scale: '1.2' }
    };

    const scheme = colorSchemes[settings.colorScheme];
    const fontSize = fontSizes[settings.fontSize];
    const spacing = spacings[settings.spacing];

    root.style.setProperty('--primary', scheme.primary);
    root.style.setProperty('--secondary', scheme.secondary);
    root.style.setProperty('--accent', scheme.accent);
    root.style.setProperty('--font-size-base', fontSize.base);
    root.style.setProperty('--font-scale', fontSize.scale);
    root.style.setProperty('--spacing-scale', spacing.scale);

    // High contrast adjustments
    if (settings.accessibility.highContrast) {
      root.style.setProperty('--contrast-multiplier', '1.5');
    } else {
      root.style.setProperty('--contrast-multiplier', '1');
    }

    // Reduced motion
    if (settings.accessibility.reducedMotion) {
      root.style.setProperty('--animation-duration', '0ms');
      root.style.setProperty('--transition-duration', '0ms');
    } else {
      root.style.setProperty('--animation-duration', '150ms');
      root.style.setProperty('--transition-duration', '150ms');
    }
  };

  const updateTheme = (theme: Theme) => {
    setSettings(prev => ({ ...prev, theme }));
  };

  const updateColorScheme = (colorScheme: ColorScheme) => {
    setSettings(prev => ({ ...prev, colorScheme }));
  };

  const updateFontSize = (fontSize: FontSize) => {
    setSettings(prev => ({ ...prev, fontSize }));
  };

  const updateSpacing = (spacing: Spacing) => {
    setSettings(prev => ({ ...prev, spacing }));
  };

  const updateAccessibility = (accessibility: Partial<AccessibilitySettings>) => {
    setSettings(prev => ({
      ...prev,
      accessibility: { ...prev.accessibility, ...accessibility }
    }));
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
  };

  const value: UnifiedThemeContextType = {
    settings,
    updateTheme,
    updateColorScheme,
    updateFontSize,
    updateSpacing,
    updateAccessibility,
    resetToDefaults
  };

  return (
    <UnifiedThemeContext.Provider value={value}>
      {children}
    </UnifiedThemeContext.Provider>
  );
}

export function useUnifiedTheme() {
  const context = useContext(UnifiedThemeContext);
  if (context === undefined) {
    throw new Error('useUnifiedTheme must be used within a UnifiedThemeProvider');
  }
  return context;
}

// Accessibility utilities
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  if (typeof document === 'undefined') return;

  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.setAttribute('class', 'sr-only');
  announcement.textContent = message;

  document.body.appendChild(announcement);

  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

export const trapFocus = (element: HTMLElement) => {
  const focusableElements = element.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  const firstElement = focusableElements[0] as HTMLElement;
  const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Tab') {
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    }
  };

  element.addEventListener('keydown', handleKeyDown);
  firstElement?.focus();

  return () => {
    element.removeEventListener('keydown', handleKeyDown);
  };
};

export const getAccessibleButtonProps = (settings: AccessibilitySettings) => ({
  role: 'button',
  tabIndex: 0,
  'aria-label': undefined,
  'aria-describedby': undefined,
  onKeyDown: (e: React.KeyboardEvent) => {
    if (settings.keyboardNavigation && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      (e.target as HTMLElement).click();
    }
  }
});