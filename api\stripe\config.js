const Stripe = require('stripe');

// Initialize Stripe with secret key
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);

// Configuration
const config = {
  stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:5173',
  priceIds: {
    pro_monthly: process.env.STRIPE_PRO_MONTHLY_PRICE_ID || 'price_pro_monthly',
    pro_yearly: process.env.STRIPE_PRO_YEARLY_PRICE_ID || 'price_pro_yearly',
  },
  trialPeriodDays: 7,
};

// Helper function to get price ID by lookup key
const getPriceByLookupKey = async (lookupKey) => {
  try {
    const prices = await stripe.prices.list({
      lookup_keys: [lookupKey],
      limit: 1,
    });
    
    if (prices.data.length === 0) {
      throw new Error(`Price not found for lookup key: ${lookupKey}`);
    }
    
    return prices.data[0];
  } catch (error) {
    console.error('Error fetching price:', error);
    throw error;
  }
};

// Helper function to create or retrieve customer
const createOrRetrieveCustomer = async (userId, email, name) => {
  try {
    // Try to find existing customer by metadata
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      return existingCustomers.data[0];
    }

    // Create new customer
    const customer = await stripe.customers.create({
      email: email,
      name: name,
      metadata: {
        userId: userId,
      },
    });

    return customer;
  } catch (error) {
    console.error('Error creating/retrieving customer:', error);
    throw error;
  }
};

// Helper function to get subscription by customer ID
const getSubscriptionByCustomerId = async (customerId) => {
  try {
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: 'all',
      limit: 1,
    });

    return subscriptions.data[0] || null;
  } catch (error) {
    console.error('Error fetching subscription:', error);
    throw error;
  }
};

// Helper function to format error responses
const formatErrorResponse = (error, statusCode = 500) => {
  console.error('Stripe API Error:', error);
  
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    },
    body: JSON.stringify({
      error: {
        message: error.message || 'An unexpected error occurred',
        type: error.type || 'api_error',
      },
    }),
  };
};

// Helper function to format success responses
const formatSuccessResponse = (data, statusCode = 200) => {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    },
    body: JSON.stringify(data),
  };
};

module.exports = {
  stripe,
  config,
  getPriceByLookupKey,
  createOrRetrieveCustomer,
  getSubscriptionByCustomerId,
  formatErrorResponse,
  formatSuccessResponse,
};
