// Unified task types for web and mobile
export type TaskPriority = 'low' | 'medium' | 'high';
export type TaskStatus = 'pending' | 'completed' | 'cancelled' | 'on-hold';


export interface FileReference {
  key: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  lastModified?: number;
}

export interface VoiceNote {
  url: string;
  duration: number;
  created_at: string;
}

export interface Comment {
  id: string;
  text: string;
  author: string;
  timestamp: string;
}

export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number;
  days_of_week?: number[];
  day_of_month?: number;
  end_date?: string;
  max_occurrences?: number;
}

export interface ReminderSettings {
  enabled: boolean;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  timing: {
    minutes_before?: number;
    hours_before?: number;
    days_before?: number;
    custom_times?: string[];
  };
}

export interface Task {
  id: string;
  date: string;
  title: string;
  description: string;
  priority: TaskPriority;
  status?: TaskStatus;

  completedAt?: string;
  dueDate?: string;
  due_date?: string;
  category?: string;
  tags?: string[];
  links?: string[];
  attachments?: FileReference[];
  voiceNotes?: VoiceNote[];
  comments?: Comment[];

  recurring_pattern?: RecurringPattern;
  reminder_settings?: ReminderSettings;
  created_at: string;
  user_id: string;
  updated_at?: string;
  progress_percentage?: number;
  estimated_hours?: number;
  actual_hours?: number;
  location?: string;
}

export interface TasksByDate {
  [date: string]: Task[];
}

export interface TaskFilter {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  category?: string[];

  date_range?: {
    start: string;
    end: string;
  };
  search?: string;
  tags?: string[];

  overdue_only?: boolean;
  has_voice_note?: boolean;
}

export interface TaskSort {
  field: 'created_at' | 'updated_at' | 'due_date' | 'priority' | 'title' | 'completion_percentage';
  direction: 'asc' | 'desc';
}

// Utility types
export type CreateTaskInput = Omit<Task, 'id' | 'created_at' | 'updated_at' | 'user_id'> & {
  user_id?: string;
};

export type UpdateTaskInput = Partial<Omit<Task, 'id' | 'created_at' | 'user_id'>>;

export type TaskInsert = CreateTaskInput;
export type TaskUpdate = UpdateTaskInput;