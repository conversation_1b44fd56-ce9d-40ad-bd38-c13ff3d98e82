import React, { useState } from 'react';
import { FileReference } from '@/types/task';

import { format } from 'date-fns';
import { openFile, formatFileSize, getFileTypeInfo } from '@/lib/file-utils';
import { Plus, Edit, Edit2, Trash2, ExternalLink, Paperclip, CheckCircle, Clock, Circle, AlertCircle, Pause, MessageSquare, Send, X, User, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea';
import { Task } from '@/types/task';
import { useScreenSize } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { FileAttachmentService } from '@/services/taskService';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

interface TaskDetailsPanelProps {
  selectedDate: Date | null;
  tasks: Task[];
  onEditTask: (task: Task) => void;
  onDeleteTask: (taskId: string) => void;
  onDeleteAttachment: (taskId: string, attachmentIndex: number) => void;
  onUpdateTaskStatus: (taskId: string, status: 'pending' | 'completed' | 'cancelled' | 'on-hold') => void;
  onViewCalendar?: () => void;
  onUpdateTaskCategory: (taskId: string, category: string) => void;
  onUpdateTaskPriority: (taskId: string, priority: 'low' | 'medium' | 'high') => void;
  onAddComment: (taskId: string, comment: string) => void;
  onUpdateComment: (taskId: string, commentIndex: number, newText: string) => void;
  onDeleteComment: (taskId: string, commentIndex: number) => void;
  showHeader?: boolean;
  headerButtons?: React.ReactNode;
}

const priorityColors = {
  low: 'bg-secondary text-secondary-foreground',
  medium: 'bg-accent text-accent-foreground',
  high: 'bg-destructive text-destructive-foreground',
};

const statusColors = {
  pending: 'bg-gray-100 text-gray-700 border-gray-300 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700',

  completed: 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700',
  cancelled: 'bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700',
  'on-hold': 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700',
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-4 w-4" />;

    case 'cancelled':
      return <AlertCircle className="h-4 w-4" />;
    case 'on-hold':
      return <Pause className="h-4 w-4" />;
    default:
      return <Circle className="h-4 w-4" />;
  }
};

// TaskComments Component
interface Comment {
  id: string;
  text: string;
  timestamp: string;
  editedAt?: string;
}

interface TaskCommentsProps {
  task: Task;
  onAddComment: (taskId: string, comment: string) => void;
  onUpdateComment: (taskId: string, commentIndex: number, newText: string) => void;
  onDeleteComment: (taskId: string, commentIndex: number) => void;
  isPastTask: boolean;
}

const TaskComments: React.FC<TaskCommentsProps> = ({ task, onAddComment, onUpdateComment, onDeleteComment, isPastTask }) => {
  // Ensure comments have the correct type
  const comments = (task.comments || []) as Comment[];
  const [newComment, setNewComment] = useState('');
  const [showComments, setShowComments] = useState(false);
  const [showAddComment, setShowAddComment] = useState(false);
  const [editingCommentIndex, setEditingCommentIndex] = useState<number | null>(null);
  const [editingCommentText, setEditingCommentText] = useState('');

  const handleAddComment = () => {
    if (newComment.trim()) {
      onAddComment(task.id, newComment.trim());
      setNewComment('');
      setShowAddComment(false); // Close the input field after adding
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddComment();
    }
  };

  const handleEditComment = (index: number) => {
    setEditingCommentIndex(index);
    setEditingCommentText(task.comments?.[index]?.text || '');
  };

  const handleSaveEdit = (index: number) => {
    if (editingCommentText.trim()) {
      onUpdateComment(task.id, index, editingCommentText.trim());
      setEditingCommentIndex(null);
      setEditingCommentText('');
    }
  };

  const handleDeleteComment = (index: number) => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      onDeleteComment(task.id, index);
    }
  };

  const handleCancelEdit = () => {
    setEditingCommentIndex(null);
    setEditingCommentText('');
  };

  const handleEditKeyPress = (e: React.KeyboardEvent, index: number) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit(index);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowComments(!showComments)}
          className="flex items-center gap-1 text-xs text-muted-foreground hover:text-foreground"
        >
          <MessageSquare className="h-3 w-3" />
          Comments ({task.comments?.length || 0})
        </button>
      </div>

      {showComments && (
        <div className="space-y-2 pl-4 border-l-2 border-muted">
          {/* Existing Comments */}
          {comments.length > 0 && (
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {comments.map((comment, index) => (
                <div key={comment.id} className="text-xs bg-muted/30 p-2 rounded group">
                  {editingCommentIndex === index ? (
                    // Edit mode - Use Enter to save, Escape to cancel
                    <div className="space-y-1">
                      <Input
                        value={editingCommentText}
                        onChange={(e) => setEditingCommentText(e.target.value)}
                        onKeyDown={(e) => handleEditKeyPress(e, index)}
                        className="h-6 text-xs"
                        autoFocus
                        placeholder="Press Enter to save, Escape to cancel"
                      />
                      <p className="text-[10px] text-muted-foreground">
                        Press <kbd className="px-1 py-0.5 bg-muted rounded text-[9px]">Enter</kbd> to save, <kbd className="px-1 py-0.5 bg-muted rounded text-[9px]">Esc</kbd> to cancel
                      </p>
                    </div>
                  ) : (
                    // View mode
                    <div>
                      <div className="flex justify-between items-start gap-2">
                        <p className="text-foreground flex-1">{comment.text}</p>
                        <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            onClick={() => handleEditComment(index)}
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 hover:bg-blue-100 dark:hover:bg-blue-900"
                            title="Edit comment"
                          >
                            <Edit2 className="h-2.5 w-2.5" />
                          </Button>
                          <Button
                            onClick={() => handleDeleteComment(index)}
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 hover:bg-red-100 dark:hover:bg-red-900 text-red-600"
                            title="Delete comment"
                          >
                            <Trash2 className="h-2.5 w-2.5" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-muted-foreground text-[10px] mt-1">
                        {format(new Date(comment.timestamp), 'MMM d, h:mm a')}
                        {comment.editedAt && (
                          <span className="ml-2 italic">(edited)</span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Add New Comment Section */}
          {showAddComment ? (
            <div className="flex gap-1">
              <Input
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add a comment..."
                className="h-7 text-xs"
                autoFocus
              />
              <Button
                onClick={handleAddComment}
                disabled={!newComment.trim()}
                size="sm"
                className="h-7 w-7 p-0"
              >
                <Send className="h-3 w-3" />
              </Button>
              <Button
                onClick={() => {
                  setShowAddComment(false);
                  setNewComment('');
                }}
                variant="outline"
                size="sm"
                className="h-7 w-7 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ) : (
            <Button
              onClick={() => setShowAddComment(true)}
              variant="outline"
              size="sm"
              className="h-7 w-auto px-2 text-xs text-blue-600 border-blue-200 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-700 dark:hover:bg-blue-950"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Comment
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export const TaskDetailsPanel: React.FC<TaskDetailsPanelProps> = ({
  selectedDate,
  tasks,
  onEditTask,
  onDeleteTask,
  onDeleteAttachment,
  onUpdateTaskStatus,
  onUpdateTaskCategory,
  onUpdateTaskPriority,
  onAddComment,
  onUpdateComment,
  onDeleteComment,
  onViewCalendar,
  showHeader = true,
  headerButtons,
}) => {
  const { isMobile, isTablet } = useScreenSize();
  const { session } = useAuth();
  
  const getToken = async () => {
    return session?.access_token || null;
  };
  // Use the regular supabase client

  // Function to open Supabase attachment
  const openSupabaseAttachment = async (taskId: string, fileName: string) => {
    try {
      // Get the current user ID from auth
      const token = await getToken();
      if (!token) {
        alert('Please log in to view attachments');
        return;
      }

      // Parse the JWT to get user ID
      const payload = JSON.parse(atob(token.split('.')[1]));
      const userId = payload.sub;

      // Fetch attachment details from the database
      const attachments = await FileAttachmentService.getTaskAttachments(taskId, userId, supabase);
      const attachment = attachments.find(att => att.filename === fileName);
      
      if (!attachment) {
        alert('Attachment not found in database');
        return;
      }

      // Try to get the public URL for the file
      const { data } = supabase.storage
        .from('task-attachments')
        .getPublicUrl(attachment.storage_path);
     
    if (!data?.publicUrl) {
      alert('Could not generate public URL for file');
      return;
    }
    
    // Open the file in a new tab
    window.open(data.publicUrl, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Error opening attachment:', error);
      alert(`Could not open attachment: ${error.message}`);
    }
  };

  // Filter tasks for the selected date
  const selectedDateTasks = selectedDate
    ? tasks.filter(task => task.date === format(selectedDate, 'yyyy-MM-dd'))
    : [];

  if (!selectedDate) {
    return (
      <Card
        data-task-details-panel
        className={cn(
          "w-full mx-auto bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 border-2 border-blue-100 dark:border-slate-700 shadow-xl",
          isMobile ? "max-w-full" : "max-w-4xl"
        )}
      >
        <CardContent className="p-4 text-center space-y-2">
            <div className="text-4xl">📅</div>
            <div>
              <h3 className="text-base font-semibold mb-1">Ready to get organized?</h3>
              <p className="text-muted-foreground text-sm mb-2">
                Select a date to view tasks
              </p>
              <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
                <p className="font-medium mb-1">Quick Tips:</p>
                <div className="text-[11px] space-y-0.5">
                  <p>📅 Click date tiles to view tasks</p>
                  <p>➕ Use + button to add tasks</p>
                  <p>⌨️ Ctrl+N for quick access</p>
                </div>
              </div>
            </div>
          </CardContent>
      </Card>
    );
  }

  return (
    <Card
      data-task-details-panel
      className={cn(
        "w-full mx-auto bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 border-2 border-blue-100 dark:border-slate-700 shadow-xl",
        isMobile ? "max-w-full" : "max-w-4xl"
      )}
    >
      {showHeader && (
        <CardHeader className={cn(
          "flex items-center justify-between py-3 px-4",
          isMobile ? "flex-col gap-2" : "flex-row"
        )}>
          <div className={isMobile ? "text-center" : ""}>
            <CardTitle className={cn(
              isMobile ? "text-base" : "text-lg"
            )}>
              Tasks List
            </CardTitle>
          <CardDescription className="text-xs text-muted-foreground">
            {format(selectedDate, 'MMM d, yyyy')} • {selectedDateTasks.length} task{selectedDateTasks.length !== 1 ? 's' : ''}
          </CardDescription>
          </div>

          {/* Header Buttons */}
          {headerButtons && (
            <div className="flex items-center gap-2">
              {headerButtons}
            </div>
          )}

        </CardHeader>
      )}
      
      <CardContent className="space-y-2 px-3 py-2">
        {selectedDateTasks.length === 0 ? (
          (() => {
            // Check if selected date is in the past
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const isPastDate = selectedDate < today;

            return (
              <div className="text-center py-8 text-muted-foreground">
                <p>No tasks scheduled for this date</p>
                {isPastDate ? (
                  <p className="text-sm mt-1 text-amber-600 dark:text-amber-400">
                    📅 This is a past date - you can only view existing tasks
                  </p>
                ) : (
                  <p className="text-sm mt-1">Click this date tile again or use the + button to add a task</p>
                )}
              </div>
            );
          })()
        ) : (
          selectedDateTasks.map((task, index) => {
            // Check if task is in the past
            const taskDate = new Date(task.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const isPastTask = taskDate < today;

            return (
            <Card
              key={task.id}
              data-task-id={task.id}
              className={cn(
                "border-l-4 transition-all duration-300",
                isPastTask ? "border-l-amber-400 bg-amber-50/30 dark:bg-amber-900/10" : "border-l-primary"
              )}>
              <CardContent className="p-2">
                <div className="space-y-1">
                  {/* Header Row: Number + Title + Category + Status */}
                  <div className="flex items-center justify-between gap-2">
                    <div className="flex items-center gap-2 flex-1">
                      <span className="flex items-center justify-center w-6 h-6 bg-primary/10 text-primary text-xs font-medium rounded-full">
                        {index + 1}
                      </span>
                      <h4 className="font-semibold text-sm flex-1">{task.title}</h4>
                      {/* Category Dropdown */}
                      <Select
                        value={task.category || 'General'}
                        onValueChange={(value) => onUpdateTaskCategory(task.id, value)}
                      >
                        <SelectTrigger className="w-24 h-6 text-xs bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="General">General</SelectItem>
                          <SelectItem value="Work">Work</SelectItem>
                          <SelectItem value="Personal">Personal</SelectItem>
                          <SelectItem value="Health">Health</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                          <SelectItem value="Education">Education</SelectItem>
                          <SelectItem value="Shopping">Shopping</SelectItem>
                          <SelectItem value="Travel">Travel</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Status Dropdown for all tasks (past, current, and future) */}
                      <Select
                        value={task.status || 'pending'}
                        onValueChange={(value) => onUpdateTaskStatus(task.id, value as any)}
                      >
                        <SelectTrigger className="w-28 h-7 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending</SelectItem>

                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="on-hold">On Hold</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>

                      {/* Priority Dropdown - Now next to Status */}
                      <Select
                        value={task.priority || 'medium'}
                        onValueChange={(value) => onUpdateTaskPriority(task.id, value as 'low' | 'medium' | 'high')}
                      >
                        <SelectTrigger className={cn("w-20 h-7 text-xs", priorityColors[task.priority])}>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">
                            <div className="flex items-center gap-1">
                              <Circle className="h-3 w-3 text-green-500" />
                              Low
                            </div>
                          </SelectItem>
                          <SelectItem value="medium">
                            <div className="flex items-center gap-1">
                              <AlertCircle className="h-3 w-3 text-yellow-500" />
                              Medium
                            </div>
                          </SelectItem>
                          <SelectItem value="high">
                            <div className="flex items-center gap-1">
                              <AlertCircle className="h-3 w-3 text-red-500" />
                              High
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Description and Tags Row */}
                  <div className="space-y-1">
                    {task.description && (
                      <p className="text-xs text-muted-foreground leading-relaxed">{task.description}</p>
                    )}
                    {task.tags && task.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {task.tags.map((tag, tagIndex) => (
                          <span key={tagIndex} className="text-xs px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Links Section */}
                  {task.links && task.links.length > 0 && (
                    <div className="flex flex-wrap gap-2 text-xs mb-1">
                      <span className="text-muted-foreground">Links:</span>
                      {task.links.map((link, index) => {
                        try {
                          const url = new URL(link.startsWith('http') ? link : `https://${link}`);
                          const domain = url.hostname.replace('www.', '');
                          return (
                            <a
                              key={`link-${index}`}
                              href={link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-1 text-blue-600 hover:underline"
                              title={link}
                              onClick={(e) => {
                                e.preventDefault();
                                try {
                                  const fixedLink = link.startsWith('http') ? link : `https://${link}`;
                                  window.open(fixedLink, '_blank', 'noopener,noreferrer');
                                } catch (err) {
                                  console.error('Error opening link:', err);
                                }
                              }}
                            >
                              <ExternalLink className="h-3 w-3" />
                              {domain}
                            </a>
                          );
                        } catch {
                          return null; // Skip invalid links
                        }
                      })}
                    </div>
                  )}

                  {/* Attachments Section */}
                  {task.attachments && task.attachments.length > 0 && (
                    <div className="flex flex-wrap gap-2 text-xs">
                      <span className="text-muted-foreground">Attachments:</span>
                      {task.attachments.map((attachment, index) => {
                        // For Supabase attachments, attachment is just the filename
                        const fileName = typeof attachment === 'string' ? attachment : `Attachment ${index + 1}`;

                        return (
                          <div key={`attachment-${index}`} className="flex items-center gap-1">
                            <button
                              className="inline-flex items-center gap-1 text-green-600 hover:underline"
                              title={fileName}
                              onClick={async (e) => {
                                e.preventDefault();
                                await openSupabaseAttachment(task.id, fileName);
                              }}
                            >
                              <Paperclip className="h-3 w-3" />
                              {fileName}
                            </button>
                            <button
                              onClick={() => onDeleteAttachment(task.id, index)}
                              className="text-red-500 hover:text-red-700"
                              title="Delete attachment"
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  )}

                  {/* User Comments Section */}
                  <TaskComments
                    task={task}
                    onAddComment={onAddComment}
                    onUpdateComment={onUpdateComment}
                    onDeleteComment={onDeleteComment}
                    isPastTask={isPastTask}
                  />

                  {/* Compact Action Buttons */}
                  <div className="flex items-center justify-end gap-1 pt-2 border-t border-muted/30">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditTask(task)}
                      className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                      title="Edit task"
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        if (window.confirm('Are you sure you want to delete this task?')) {
                          onDeleteTask(task.id);
                        }
                      }}
                      className="h-6 w-6 p-0 text-muted-foreground hover:text-red-600"
                      title="Delete task"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            );
          })
        )}
      </CardContent>
    </Card>
  );
};