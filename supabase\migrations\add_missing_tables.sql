-- Add missing tables to sync with TypeScript types
-- This migration adds tables that are referenced in the types but missing from the main schema

-- Create subscribers table for subscription management
CREATE TABLE IF NOT EXISTS subscribers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  user_id TEXT REFERENCES profiles(id) ON DELETE SET NULL,
  subscribed BOOLEAN DEFAULT true,
  stripe_customer_id TEXT,
  subscription_tier TEXT,
  subscription_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unified_notifications table for enhanced notification system
CREATE TABLE IF NOT EXISTS unified_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  channels TEXT[] DEFAULT ARRAY['in_app'],
  delivered_channels TEXT[] DEFAULT ARRAY[]::TEXT[],
  data JSONB,
  read BOOLEAN DEFAULT false,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_relationships table for user connections
CREATE TABLE IF NOT EXISTS user_relationships (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  requester_id TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  addressee_id TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  relationship_type TEXT NOT NULL CHECK (relationship_type IN ('friend', 'colleague', 'family', 'team_member')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'blocked')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(requester_id, addressee_id)
);

-- Create user_groups table for group management
CREATE TABLE IF NOT EXISTS user_groups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  created_by TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create group_memberships table for group membership management
CREATE TABLE IF NOT EXISTS group_memberships (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  group_id UUID NOT NULL REFERENCES user_groups(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
  is_active BOOLEAN DEFAULT true,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(group_id, user_id)
);

-- Create task_analytics view for analytics data
CREATE OR REPLACE VIEW task_analytics AS
SELECT 
  user_id,
  COUNT(*) as total_tasks,
  COUNT(*) FILTER (WHERE status = 'completed') as completed_tasks,
  COUNT(*) FILTER (WHERE status = 'pending') as pending_tasks,
  COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_tasks,
  COUNT(*) FILTER (WHERE status = 'on-hold') as on_hold_tasks,
  COUNT(*) FILTER (WHERE status = 'pending' AND due_date < CURRENT_DATE) as overdue_tasks,
  COUNT(*) FILTER (WHERE priority = 'high') as high_priority_tasks,
  COUNT(*) FILTER (WHERE status = 'pending') as in_progress_tasks,
  AVG(completion_percentage) as avg_completion_percentage,
  SUM(estimated_hours) as total_estimated_hours,
  SUM(actual_hours) as total_actual_hours
FROM tasks
GROUP BY user_id;

-- Add RLS policies for new tables
ALTER TABLE subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE unified_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_memberships ENABLE ROW LEVEL SECURITY;

-- Subscribers policies
CREATE POLICY "Users can view own subscription" ON subscribers
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can update own subscription" ON subscribers
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Unified notifications policies
CREATE POLICY "Users can view own unified notifications" ON unified_notifications
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can update own unified notifications" ON unified_notifications
  FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own unified notifications" ON unified_notifications
  FOR DELETE USING (auth.uid()::text = user_id);

-- User relationships policies
CREATE POLICY "Users can view their relationships" ON user_relationships
  FOR SELECT USING (auth.uid()::text = requester_id OR auth.uid()::text = addressee_id);

CREATE POLICY "Users can create relationship requests" ON user_relationships
  FOR INSERT WITH CHECK (auth.uid()::text = requester_id);

CREATE POLICY "Users can update their relationships" ON user_relationships
  FOR UPDATE USING (auth.uid()::text = requester_id OR auth.uid()::text = addressee_id);

-- User groups policies
CREATE POLICY "Users can view groups they created or are members of" ON user_groups
  FOR SELECT USING (
    auth.uid()::text = created_by OR 
    EXISTS (SELECT 1 FROM group_memberships gm WHERE gm.group_id = id AND gm.user_id = auth.uid()::text AND gm.is_active = true)
  );

CREATE POLICY "Users can create groups" ON user_groups
  FOR INSERT WITH CHECK (auth.uid()::text = created_by);

CREATE POLICY "Users can update groups they created" ON user_groups
  FOR UPDATE USING (auth.uid()::text = created_by);

CREATE POLICY "Users can delete groups they created" ON user_groups
  FOR DELETE USING (auth.uid()::text = created_by);

-- Group memberships policies
CREATE POLICY "Users can view memberships for their groups or their own memberships" ON group_memberships
  FOR SELECT USING (
    auth.uid()::text = user_id OR 
    EXISTS (SELECT 1 FROM user_groups ug WHERE ug.id = group_id AND ug.created_by = auth.uid()::text)
  );

CREATE POLICY "Group creators can manage memberships" ON group_memberships
  FOR ALL USING (
    EXISTS (SELECT 1 FROM user_groups ug WHERE ug.id = group_id AND ug.created_by = auth.uid()::text)
  );

-- Add functions for relationship management
CREATE OR REPLACE FUNCTION send_relationship_request(
  requester_id TEXT,
  addressee_id TEXT,
  relationship_type TEXT
)
RETURNS TEXT AS $$
DECLARE
  request_id UUID;
BEGIN
  -- Check if relationship already exists
  IF EXISTS (
    SELECT 1 FROM user_relationships 
    WHERE (requester_id = $1 AND addressee_id = $2) 
       OR (requester_id = $2 AND addressee_id = $1)
  ) THEN
    RETURN 'Relationship already exists';
  END IF;
  
  -- Create new relationship request
  INSERT INTO user_relationships (requester_id, addressee_id, relationship_type)
  VALUES ($1, $2, $3)
  RETURNING id INTO request_id;
  
  RETURN request_id::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION respond_to_relationship_request(
  relationship_id TEXT,
  response TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Update relationship status
  UPDATE user_relationships 
  SET status = response, updated_at = NOW()
  WHERE id = relationship_id::UUID
    AND addressee_id = auth.uid()::text
    AND status = 'pending';
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add triggers for updated_at columns
CREATE TRIGGER update_subscribers_updated_at 
  BEFORE UPDATE ON subscribers 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_unified_notifications_updated_at 
  BEFORE UPDATE ON unified_notifications 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_relationships_updated_at 
  BEFORE UPDATE ON user_relationships 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_groups_updated_at 
  BEFORE UPDATE ON user_groups 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscribers_user_id ON subscribers(user_id);
CREATE INDEX IF NOT EXISTS idx_subscribers_email ON subscribers(email);
CREATE INDEX IF NOT EXISTS idx_unified_notifications_user_id ON unified_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_unified_notifications_read ON unified_notifications(read);
CREATE INDEX IF NOT EXISTS idx_user_relationships_requester ON user_relationships(requester_id);
CREATE INDEX IF NOT EXISTS idx_user_relationships_addressee ON user_relationships(addressee_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_created_by ON user_groups(created_by);
CREATE INDEX IF NOT EXISTS idx_group_memberships_group_id ON group_memberships(group_id);
CREATE INDEX IF NOT EXISTS idx_group_memberships_user_id ON group_memberships(user_id);