import { useEffect, useState, useCallback } from 'react'

interface VirtualKeyboardState {
  isVisible: boolean
  height: number
  viewportHeight: number
  adjustedHeight: number
}

export function useVirtualKeyboard() {
  const [keyboardState, setKeyboardState] = useState<VirtualKeyboardState>({
    isVisible: false,
    height: 0,
    viewportHeight: window.innerHeight,
    adjustedHeight: window.innerHeight
  })

  const updateViewport = useCallback(() => {
    const currentHeight = window.innerHeight
    const visualViewport = window.visualViewport
    
    if (visualViewport) {
      const keyboardHeight = window.innerHeight - visualViewport.height
      const isKeyboardVisible = keyboardHeight > 0
      
      setKeyboardState({
        isVisible: isKeyboardVisible,
        height: keyboardHeight,
        viewportHeight: currentHeight,
        adjustedHeight: visualViewport.height
      })
    } else {
      // Fallback for browsers without Visual Viewport API
      const initialHeight = window.screen.availHeight
      const currentHeight = window.innerHeight
      const keyboardHeight = initialHeight - currentHeight
      const isKeyboardVisible = keyboardHeight > 150 // Threshold for keyboard detection
      
      setKeyboardState({
        isVisible: isKeyboardVisible,
        height: Math.max(0, keyboardHeight),
        viewportHeight: currentHeight,
        adjustedHeight: currentHeight
      })
    }
  }, [])

  useEffect(() => {
    // Check if Visual Viewport API is supported
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateViewport)
      window.visualViewport.addEventListener('scroll', updateViewport)
      
      return () => {
        window.visualViewport?.removeEventListener('resize', updateViewport)
        window.visualViewport?.removeEventListener('scroll', updateViewport)
      }
    } else {
      // Fallback for older browsers
      window.addEventListener('resize', updateViewport)
      return () => window.removeEventListener('resize', updateViewport)
    }
  }, [updateViewport])

  // Scroll element into view when keyboard appears
  const scrollIntoView = useCallback((element: HTMLElement, options?: ScrollIntoViewOptions) => {
    if (!element) return

    const defaultOptions: ScrollIntoViewOptions = {
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
      ...options
    }

    // Add delay to ensure keyboard is fully visible
    setTimeout(() => {
      element.scrollIntoView(defaultOptions)
    }, 300)
  }, [])

  return {
    ...keyboardState,
    scrollIntoView
  }
}

// Hook for handling input focus with keyboard adjustments
export function useInputFocus() {
  const { isVisible, scrollIntoView } = useVirtualKeyboard()
  
  const handleInputFocus = useCallback((event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const target = event.target as HTMLElement
    
    // Scroll the input into view when keyboard appears
    if (isVisible || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      scrollIntoView(target)
    }
  }, [isVisible, scrollIntoView])

  const handleInputBlur = useCallback(() => {
    // Optional: Handle any cleanup when input loses focus
  }, [])

  return {
    handleInputFocus,
    handleInputBlur,
    isKeyboardVisible: isVisible
  }
}

// Hook for managing keyboard-aware layout
export function useKeyboardAwareLayout() {
  const keyboardState = useVirtualKeyboard()
  
  const getLayoutStyles = useCallback(() => {
    if (!keyboardState.isVisible) {
      return {}
    }

    return {
      height: `${keyboardState.adjustedHeight}px`,
      minHeight: `${keyboardState.adjustedHeight}px`,
      maxHeight: `${keyboardState.adjustedHeight}px`,
      overflow: 'hidden'
    }
  }, [keyboardState])

  const getContentStyles = useCallback(() => {
    if (!keyboardState.isVisible) {
      return {}
    }

    return {
      paddingBottom: `${Math.max(0, keyboardState.height - 100)}px` // Adjust padding to account for keyboard
    }
  }, [keyboardState])

  return {
    keyboardState,
    getLayoutStyles,
    getContentStyles,
    isKeyboardVisible: keyboardState.isVisible
  }
}
