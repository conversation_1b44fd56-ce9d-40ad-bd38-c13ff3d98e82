import { BaseApiClient } from './base.client';
import { Task, CreateTaskInput, UpdateTaskInput, TaskFilter, TaskSort, ApiResponse, PaginatedResponse } from '../types';

export class TaskApiClient extends BaseApiClient {
  async createTask(task: CreateTaskInput): Promise<ApiResponse<Task>> {
    return this.post<Task>('/tasks', task);
  }

  async updateTask(id: string, updates: UpdateTaskInput): Promise<ApiResponse<Task>> {
    return this.patch<Task>(`/tasks/${id}`, updates);
  }

  async deleteTask(id: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/tasks/${id}`);
  }

  async getTask(id: string): Promise<ApiResponse<Task>> {
    return this.get<Task>(`/tasks/${id}`);
  }

  async getTasks(filter?: TaskFilter, sort?: TaskSort): Promise<PaginatedResponse<Task>> {
    const params = {
      filters: filter,
      sort
    };
    return this.get<Task[]>('/tasks', params) as Promise<PaginatedResponse<Task>>;
  }

  async getTasksByDate(startDate: string, endDate: string): Promise<ApiResponse<{ [date: string]: Task[] }>> {
    return this.get<{ [date: string]: Task[] }>('/tasks/by-date', {
      filters: {
        start_date: startDate,
        end_date: endDate
      }
    });
  }

  async duplicateTask(id: string): Promise<ApiResponse<Task>> {
    return this.post<Task>(`/tasks/${id}/duplicate`);
  }

  async bulkUpdateTasks(ids: string[], updates: UpdateTaskInput): Promise<ApiResponse<Task[]>> {
    return this.patch<Task[]>('/tasks/bulk', { ids, updates });
  }

  async bulkDeleteTasks(ids: string[]): Promise<ApiResponse<void>> {
    return this.request<void>('/tasks/bulk', {
      method: 'DELETE',
      body: JSON.stringify({ ids })
    });
  }

  async getTaskStats(userId: string): Promise<ApiResponse<{
    total: number;
    completed: number;
    pending: number;
    overdue: number;
    by_priority: { [key: string]: number };
    by_status: { [key: string]: number };
  }>> {
    return this.get(`/tasks/stats/${userId}`);
  }
}