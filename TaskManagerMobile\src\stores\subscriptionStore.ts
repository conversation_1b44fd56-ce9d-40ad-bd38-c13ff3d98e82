// Mobile subscription store integration
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// React Native specific imports
let AsyncStorage: any = null;
try {
  AsyncStorage = require('@react-native-async-storage/async-storage').default;
} catch (error) {
  console.warn('AsyncStorage not available, using memory storage');
}

interface MobileSubscriptionState {
  // Subscription data
  isSubscribed: boolean;
  subscriptionTier: 'free' | 'pro';
  subscriptionStatus: 'trial' | 'active' | 'past_due' | 'canceled' | 'unpaid';
  trialDaysRemaining: number;
  
  // Storage and limits
  storageUsed: number;
  storageLimit: number;
  storagePercentage: number;
  filesCount: number;
  
  // Feature flags
  hasVoiceFeatures: boolean;
  hasAdvancedAnalytics: boolean;
  hasUnlimitedStorage: boolean;
  hasCollaboration: boolean;
  
  // Stripe integration
  stripeCustomerId: string | null;
  stripeSubscriptionId: string | null;
  
  // Loading states
  isCheckingSubscription: boolean;
  isManagingBilling: boolean;
  subscriptionError: string | null;
  
  // Last update tracking
  lastChecked: number;
  autoRefreshEnabled: boolean;
}

interface MobileSubscriptionActions {
  // Subscription management
  updateSubscription: (data: Partial<MobileSubscriptionState>) => void;
  checkSubscriptionStatus: () => Promise<void>;
  
  // Storage management
  updateStorageUsage: (used: number, count: number) => void;
  canUploadFile: (fileSize: number) => boolean;
  getRemainingStorage: () => number;
  
  // Feature checks
  canUseFeature: (feature: 'voice' | 'analytics' | 'collaboration') => boolean;
  getFeatureLimits: () => {
    maxFileSize: number;
    maxTasksPerDay: number;
    maxVoiceNotesPerTask: number;
  };
  
  // Billing actions
  startBillingFlow: (planId: string) => Promise<void>;
  openBillingPortal: () => Promise<void>;
  
  // Trial management
  isTrialActive: () => boolean;
  getTrialDaysLeft: () => number;
  
  // Auto-refresh
  enableAutoRefresh: () => void;
  disableAutoRefresh: () => void;
  
  // Cache and sync
  clearSubscriptionCache: () => void;
  syncWithUnifiedStore: () => void;
  
  // Error handling
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Reset
  reset: () => void;
}

type MobileSubscriptionStore = MobileSubscriptionState & MobileSubscriptionActions;

// React Native storage adapter
const createReactNativeStorage = () => {
  if (!AsyncStorage) {
    // Fallback memory storage for testing
    const memoryStore = new Map<string, string>();
    return {
      getItem: async (name: string) => memoryStore.get(name) || null,
      setItem: async (name: string, value: string) => memoryStore.set(name, value),
      removeItem: async (name: string) => memoryStore.delete(name),
    };
  }

  return {
    getItem: AsyncStorage.getItem,
    setItem: AsyncStorage.setItem,
    removeItem: AsyncStorage.removeItem,
  };
};

const initialState: MobileSubscriptionState = {
  isSubscribed: false,
  subscriptionTier: 'free',
  subscriptionStatus: 'trial',
  trialDaysRemaining: 14,
  storageUsed: 0,
  storageLimit: 10 * 1024 * 1024, // 10MB for free tier
  storagePercentage: 0,
  filesCount: 0,
  hasVoiceFeatures: false,
  hasAdvancedAnalytics: false,
  hasUnlimitedStorage: false,
  hasCollaboration: false,
  stripeCustomerId: null,
  stripeSubscriptionId: null,
  isCheckingSubscription: false,
  isManagingBilling: false,
  subscriptionError: null,
  lastChecked: 0,
  autoRefreshEnabled: true,
};

export const useMobileSubscriptionStore = create<MobileSubscriptionStore>()(
  persist(
    immer((set, get) => ({
      ...initialState,

      // Subscription management
      updateSubscription: (data) => set((state) => {
        Object.assign(state, data);
        
        // Update derived values
        if (data.storageUsed !== undefined || data.storageLimit !== undefined) {
          state.storagePercentage = (state.storageUsed / state.storageLimit) * 100;
        }
        
        // Update feature flags based on tier
        if (data.subscriptionTier) {
          state.hasVoiceFeatures = data.subscriptionTier === 'pro';
          state.hasAdvancedAnalytics = data.subscriptionTier === 'pro';
          state.hasUnlimitedStorage = data.subscriptionTier === 'pro';
          state.hasCollaboration = data.subscriptionTier === 'pro';
          
          // Update storage limit based on tier
          state.storageLimit = data.subscriptionTier === 'pro' 
            ? 1024 * 1024 * 1024 // 1GB for pro
            : 10 * 1024 * 1024; // 10MB for free
        }
        
        state.lastChecked = Date.now();
      }),

      checkSubscriptionStatus: async () => {
        set((state) => {
          state.isCheckingSubscription = true;
          state.subscriptionError = null;
        });

        try {
          // In a real implementation, this would call the Supabase edge function
          const response = await fetch('/supabase/functions/check-subscription', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              // Add auth headers here
            },
          });

          if (!response.ok) {
            throw new Error('Failed to check subscription status');
          }

          const subscriptionData = await response.json();
          
          get().updateSubscription({
            isSubscribed: subscriptionData.subscribed,
            subscriptionTier: subscriptionData.subscription_tier || 'free',
            subscriptionStatus: subscriptionData.status || 'trial',
            stripeCustomerId: subscriptionData.stripe_customer_id,
            stripeSubscriptionId: subscriptionData.stripe_subscription_id,
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          set((state) => {
            state.subscriptionError = errorMessage;
          });
        } finally {
          set((state) => {
            state.isCheckingSubscription = false;
          });
        }
      },

      // Storage management
      updateStorageUsage: (used, count) => set((state) => {
        state.storageUsed = used;
        state.filesCount = count;
        state.storagePercentage = (used / state.storageLimit) * 100;
      }),

      canUploadFile: (fileSize) => {
        const state = get();
        const remainingStorage = state.storageLimit - state.storageUsed;
        
        // Check file size limits based on tier
        const maxFileSize = state.subscriptionTier === 'pro' 
          ? 100 * 1024 * 1024 // 100MB for pro
          : 5 * 1024 * 1024; // 5MB for free
        
        return fileSize <= maxFileSize && fileSize <= remainingStorage;
      },

      getRemainingStorage: () => {
        const state = get();
        return Math.max(0, state.storageLimit - state.storageUsed);
      },

      // Feature checks
      canUseFeature: (feature) => {
        const state = get();
        switch (feature) {
          case 'voice':
            return state.hasVoiceFeatures;
          case 'analytics':
            return state.hasAdvancedAnalytics;
          case 'collaboration':
            return state.hasCollaboration;
          default:
            return false;
        }
      },

      getFeatureLimits: () => {
        const state = get();
        if (state.subscriptionTier === 'pro') {
          return {
            maxFileSize: 100 * 1024 * 1024, // 100MB
            maxTasksPerDay: -1, // Unlimited
            maxVoiceNotesPerTask: -1, // Unlimited
          };
        } else {
          return {
            maxFileSize: 5 * 1024 * 1024, // 5MB
            maxTasksPerDay: 50,
            maxVoiceNotesPerTask: 3,
          };
        }
      },

      // Billing actions
      startBillingFlow: async (planId) => {
        set((state) => {
          state.isManagingBilling = true;
          state.subscriptionError = null;
        });

        try {
          // In React Native, this would integrate with platform-specific billing
          // For now, we'll simulate the flow
          const response = await fetch('/supabase/functions/create-checkout', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ planId }),
          });

          if (!response.ok) {
            throw new Error('Failed to start billing flow');
          }

          const { url } = await response.json();
          
          // In React Native, you would open the billing URL
          // using Linking.openURL(url) or an in-app browser
          console.log('Open billing URL:', url);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Billing flow failed';
          set((state) => {
            state.subscriptionError = errorMessage;
          });
        } finally {
          set((state) => {
            state.isManagingBilling = false;
          });
        }
      },

      openBillingPortal: async () => {
        set((state) => {
          state.isManagingBilling = true;
          state.subscriptionError = null;
        });

        try {
          const response = await fetch('/supabase/functions/customer-portal', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            throw new Error('Failed to open billing portal');
          }

          const { url } = await response.json();
          
          // In React Native, open the portal URL
          console.log('Open portal URL:', url);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Portal access failed';
          set((state) => {
            state.subscriptionError = errorMessage;
          });
        } finally {
          set((state) => {
            state.isManagingBilling = false;
          });
        }
      },

      // Trial management
      isTrialActive: () => {
        const state = get();
        return state.subscriptionStatus === 'trial' && state.trialDaysRemaining > 0;
      },

      getTrialDaysLeft: () => {
        return get().trialDaysRemaining;
      },

      // Auto-refresh management
      enableAutoRefresh: () => set((state) => {
        state.autoRefreshEnabled = true;
      }),

      disableAutoRefresh: () => set((state) => {
        state.autoRefreshEnabled = false;
      }),

      // Cache and sync
      clearSubscriptionCache: () => {
        // Clear any cached subscription data
        console.log('Clearing subscription cache');
      },

      syncWithUnifiedStore: () => {
        // Sync with the unified web store if available
        const state = get();
        
        // This would integrate with the unified store
        console.log('Syncing with unified store:', {
          tier: state.subscriptionTier,
          status: state.subscriptionStatus,
          features: {
            voice: state.hasVoiceFeatures,
            analytics: state.hasAdvancedAnalytics,
            collaboration: state.hasCollaboration,
          },
        });
      },

      // Error handling
      setError: (error) => set((state) => {
        state.subscriptionError = error;
      }),

      clearError: () => set((state) => {
        state.subscriptionError = null;
      }),

      // Reset
      reset: () => set((state) => {
        Object.assign(state, initialState);
      }),
    })),
    {
      name: 'mobile-subscription-store',
      storage: createReactNativeStorage(),
      partialize: (state) => ({
        isSubscribed: state.isSubscribed,
        subscriptionTier: state.subscriptionTier,
        subscriptionStatus: state.subscriptionStatus,
        trialDaysRemaining: state.trialDaysRemaining,
        storageUsed: state.storageUsed,
        storageLimit: state.storageLimit,
        filesCount: state.filesCount,
        stripeCustomerId: state.stripeCustomerId,
        stripeSubscriptionId: state.stripeSubscriptionId,
        lastChecked: state.lastChecked,
        autoRefreshEnabled: state.autoRefreshEnabled,
      }),
    }
  )
);

// Auto-refresh subscription status periodically
let refreshInterval: NodeJS.Timeout | null = null;

export const startSubscriptionAutoRefresh = () => {
  if (refreshInterval) return; // Already running

  refreshInterval = setInterval(() => {
    const store = useMobileSubscriptionStore.getState();
    
    if (store.autoRefreshEnabled && !store.isCheckingSubscription) {
      // Only refresh if it's been more than 5 minutes since last check
      const timeSinceLastCheck = Date.now() - store.lastChecked;
      if (timeSinceLastCheck > 5 * 60 * 1000) {
        store.checkSubscriptionStatus();
      }
    }
  }, 60 * 1000); // Check every minute
};

export const stopSubscriptionAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
};