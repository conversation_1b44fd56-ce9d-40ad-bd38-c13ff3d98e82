import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { format } from 'date-fns';
import {
  Home,
  Calendar,
  Bell,
  Search,
  Plus,
  Calendar as CalendarIcon
} from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ThemeToggle } from '@/components/calendar/ThemeToggle';
import { UserButton } from '@/components/ui/user-button';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-mobile';

interface NavigationProps {
  onQuickAdd?: () => void;
  onSearch?: (query: string) => void;
  onCalendarToggle?: () => void;
  isCalendarCollapsed?: boolean;
  onDateSelect?: (date: Date) => void;
  onYearChange?: (year: number) => void;
  selectedDate?: Date | null;
  isDatePickerOpen?: boolean;
  onDatePickerOpenChange?: (open: boolean) => void;
}

const Navigation: React.FC<NavigationProps> = ({
  onQuickAdd,
  onSearch,
  onCalendarToggle,
  isCalendarCollapsed = false,
  onDateSelect,
  onYearChange,
  selectedDate,
  isDatePickerOpen = false,
  onDatePickerOpenChange
}) => {
  const { isMobile } = useScreenSize();
  const location = useLocation();
  
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch?.(query);
  };

  const handleTodayClick = () => {
    const today = new Date();
    onYearChange?.(today.getFullYear());
    onDateSelect?.(today);
  };

  const handleDatePickerSelect = (date: Date | undefined) => {
    if (date) {
      onYearChange?.(date.getFullYear());
      onDateSelect?.(date);
      onDatePickerOpenChange?.(false);
    }
  };

  const navItems = [
    { path: '/', label: 'Home', icon: Home },
    { path: '/calendar', label: 'Calendar', icon: Calendar },
  ];

  if (isMobile) {
    return null; // Use MobileNavigation for mobile
  }

  return (
    <TooltipProvider>
      <header className="glass-card fixed top-0 left-0 right-0 z-50 animate-slide-down border-b border-border/50">
          <div className="flex items-center h-16 w-full">
              {/* Extreme Left: TaskManager Pro Logo */}
              <div className="flex items-center flex-shrink-0 pl-4">
                <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
                  <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center floating-element">
                    <Calendar className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <span className="text-responsive-xl font-bold text-premium">
                      TaskManager Pro
                    </span>
                    <div className="text-xs text-muted-foreground -mt-1">
                      Premium Task Management
                    </div>
                  </div>
                </Link>
              </div>

              {/* Right-aligned container with consistent spacing */}
              <div className="flex items-center gap-3 ml-auto pr-4">
                {navItems.map((item) => {
                  const IconComponent = item.icon;
                  const isActive = location.pathname === item.path;
                  
                  if (item.path === '/calendar') {
                    return (
                      <Button
                        key="calendar-toggle"
                        onClick={onCalendarToggle}
                        className={cn(
                          "flex items-center h-9 px-3 rounded-md text-sm font-medium transition-all duration-200 focus-premium",
                          isCalendarCollapsed
                            ? "bg-green-500 text-white hover:bg-green-600"
                            : "bg-primary/10 text-primary hover:bg-blue-600 hover:text-white border border-primary/20"
                        )}
                        variant="ghost"
                      >
                        {isCalendarCollapsed ? "Show Calendar" : "Hide Calendar"}
                      </Button>
                    );
                  } else {
                    return (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={cn(
                          "flex items-center h-9 px-3 rounded-md text-sm font-medium transition-all duration-200 focus-premium",
                          "bg-primary/10 text-primary hover:bg-blue-600 hover:text-white border border-primary/20",
                          isActive && "shadow-sm"
                        )}
                      >
                        <IconComponent className="w-4 h-4" />
                        <span>{item.label}</span>
                      </Link>
                    );
                  }
                })}
                
                {/* Search Bar */}
                <div className="relative hidden lg:block">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  <Input
                    type="text"
                    placeholder="Search tasks..."
                    value={searchQuery}
                    onChange={handleSearch}
                    className="input-premium pl-10 pr-10 w-64 text-sm border border-green-500 focus:border-green-600 hover:bg-green-50/10 bg-background/50 backdrop-blur-sm transition-all duration-200"
                  />
                  {searchQuery && (
                    <button
                      onClick={() => {
                        setSearchQuery('');
                        onSearch?.('');
                      }}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground hover:text-foreground transition-colors"
                      aria-label="Clear search"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Today Button */}
                <Button
                  onClick={handleTodayClick}
                  className={cn(
                    "flex items-center h-9 px-3 rounded-md text-sm font-medium transition-all duration-200 focus-premium",
                    "bg-primary/10 text-primary hover:bg-blue-600 hover:text-white border border-primary/20"
                  )}
                  variant="ghost"
                >
                  Today
                </Button>
                
                {/* Date Picker Button */}
                <Popover open={isDatePickerOpen} onOpenChange={onDatePickerOpenChange}>
                  <PopoverTrigger asChild>
                    <Button
                      className={cn(
                        "flex items-center gap-2 h-9 px-3 rounded-md text-sm font-medium transition-all duration-200 focus-premium",
                        "bg-primary/10 text-primary hover:bg-blue-600 hover:text-white border border-primary/20"
                      )}
                      variant="ghost"
                    >
                      <CalendarIcon className="h-4 w-4" />
                      {selectedDate && !isNaN(selectedDate.getTime()) ? format(selectedDate, 'MMM d, yyyy') : 'Select Date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={selectedDate || undefined}
                      onSelect={handleDatePickerSelect}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                
                {/* New Task Button */}
                {onQuickAdd && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={onQuickAdd}
                        className="bg-blue-600 text-white focus-premium h-9 px-3 rounded-md text-sm font-medium transition-all duration-200 border border-blue-600"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        New Task
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="z-[60]">
                      Add New Task
                    </TooltipContent>
                  </Tooltip>
                )}

                {/* Notification Bell */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className={cn(
                        "flex items-center justify-center h-9 w-9 rounded-md text-sm font-medium transition-all duration-200 focus-premium",
                        "bg-primary/10 text-primary hover:bg-primary/20 border border-primary/20"
                      )}
                    >
                      <Bell className="w-4 h-4" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="z-[60]">
                    Notifications
                  </TooltipContent>
                </Tooltip>
                
                {/* Dark Theme Toggle */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className={cn(
                        "flex items-center justify-center h-9 w-9 rounded-md text-sm font-medium transition-all duration-200 focus-premium",
                        "bg-primary/10 text-primary hover:bg-primary/20 border border-primary/20"
                      )}
                    >
                      <ThemeToggle className="focus-premium" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="z-[60]">
                    Toggle Theme
                  </TooltipContent>
                </Tooltip>
                
                {/* User Profile */}
                <UserButton />
              </div>
          </div>
      </header>
    </TooltipProvider>
  );
};

export default Navigation;