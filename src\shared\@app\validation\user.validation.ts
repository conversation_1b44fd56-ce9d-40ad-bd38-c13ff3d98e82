import { z } from 'zod';

export const UserProfileSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  full_name: z.string().optional(),
  avatar_url: z.string().url().optional(),

  notification_preferences: z.object({
    email: z.boolean(),
    push: z.boolean(),

    reminder: z.boolean()
  }),
  timezone: z.string(),
  work_hours: z.object({
    start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    days: z.array(z.number().min(0).max(6))
  }),
  theme_preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']),
    color_scheme: z.string(),
    font_size: z.enum(['small', 'medium', 'large']),
    reduced_motion: z.boolean(),
    high_contrast: z.boolean()
  }).optional(),
  accessibility_preferences: z.object({
    screen_reader: z.boolean(),
    keyboard_navigation: z.boolean(),
    focus_indicators: z.boolean()
  }).optional(),
  created_at: z.string(),
  updated_at: z.string()
});

export const UpdateUserProfileSchema = UserProfileSchema.partial().omit({
  id: true,
  created_at: true
});

export const ProfileDataSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  full_name: z.string().optional(),
  avatar_url: z.string().url().optional()
});

export function validateUserProfile(data: unknown) {
  return UserProfileSchema.safeParse(data);
}

export function validateUpdateUserProfile(data: unknown) {
  return UpdateUserProfileSchema.safeParse(data);
}

export function validateProfileData(data: unknown) {
  return ProfileDataSchema.safeParse(data);
}