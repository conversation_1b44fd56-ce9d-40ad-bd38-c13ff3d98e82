import { Task } from '@/types/unified-task';

export interface AnalyticsMetrics {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  onHoldTasks: number;
  cancelledTasks: number;
  overdueTasks: number;

  highPriorityTasks: number;
  mediumPriorityTasks: number;
  lowPriorityTasks: number;
  completionRate: number;
  averageCompletionTime: number | null;
  productivity: {
    tasksPerDay: number;
    completionStreak: number;
  };
}

export interface CategoryBreakdown {
  [category: string]: {
    total: number;
    completed: number;
    percentage: number;
  };
}

export interface TimeRangeAnalytics {
  timeRange: 'week' | 'month' | 'quarter' | 'year' | 'all';
  startDate: Date;
  endDate: Date;
  metrics: AnalyticsMetrics;
  categoryBreakdown: CategoryBreakdown;
  trends: {
    taskCreationTrend: number; // percentage change from previous period
    completionTrend: number;
    productivityTrend: number;
  };
}

export interface DetailedAnalytics {
  overview: AnalyticsMetrics;
  timeComparison: {
    current: TimeRangeAnalytics;
    previous: TimeRangeAnalytics;
  };
  categoryBreakdown: CategoryBreakdown;

  performanceMetrics: {
    onTimeCompletion: number;
    avgTaskDuration: number | null;
    mostProductiveHours: number[];
    mostProductiveDays: string[];
  };
}

export class UnifiedAnalyticsService {
  /**
   * Calculate comprehensive analytics for a given time range
   */
  static calculateAnalytics(
    tasks: Task[],
    timeRange: 'week' | 'month' | 'quarter' | 'year' | 'all' = 'all',
    userId?: string
  ): TimeRangeAnalytics {
    const { startDate, endDate } = this.getTimeRangeDates(timeRange);
    const filteredTasks = this.filterTasksByDateRange(tasks, startDate, endDate, userId);
    
    const metrics = this.calculateMetrics(filteredTasks);
    const categoryBreakdown = this.calculateCategoryBreakdown(filteredTasks);
    
    // Calculate trends by comparing with previous period
    const previousPeriod = this.getPreviousPeriodTasks(tasks, timeRange, startDate, endDate, userId);
    const previousMetrics = this.calculateMetrics(previousPeriod);
    const trends = this.calculateTrends(metrics, previousMetrics);

    return {
      timeRange,
      startDate,
      endDate,
      metrics,
      categoryBreakdown,
      trends,
    };
  }

  /**
   * Calculate detailed analytics with comparisons and deep insights
   */
  static calculateDetailedAnalytics(
    tasks: Task[],
    timeRange: 'week' | 'month' | 'quarter' | 'year' | 'all' = 'month',
    userId?: string
  ): DetailedAnalytics {
    const current = this.calculateAnalytics(tasks, timeRange, userId);
    const previous = this.calculatePreviousPeriodAnalytics(tasks, timeRange, userId);
    
    const overview = current.metrics;
    const categoryBreakdown = current.categoryBreakdown;
    

    const performanceMetrics = this.calculatePerformanceMetrics(tasks, userId);

    return {
      overview,
      timeComparison: { current, previous },
      categoryBreakdown,

      performanceMetrics,
    };
  }

  /**
   * Calculate core metrics from tasks
   */
  private static calculateMetrics(tasks: Task[]): AnalyticsMetrics {
    const total = tasks.length;
    const completed = tasks.filter(t => t.status === 'completed').length;

    const pending = tasks.filter(t => t.status === 'pending').length;
    const onHold = tasks.filter(t => t.status === 'on-hold').length;
    const cancelled = tasks.filter(t => t.status === 'cancelled').length;
    
    const overdue = tasks.filter(t => 
      t.due_date && 
      new Date(t.due_date) < new Date() && 
      t.status !== 'completed'
    ).length;
    

    
    const highPriority = tasks.filter(t => t.priority === 'high').length;
    const mediumPriority = tasks.filter(t => t.priority === 'medium').length;
    const lowPriority = tasks.filter(t => t.priority === 'low').length;
    
    const completionRate = total > 0 ? (completed / total) * 100 : 0;
    
    // Calculate average completion time
    const completedTasksWithDates = tasks.filter(t => 
      t.status === 'completed' && t.created_at && t.completed_at
    );
    
    const averageCompletionTime = completedTasksWithDates.length > 0 
      ? completedTasksWithDates.reduce((acc, task) => {
          const created = new Date(task.created_at);
          const completed = new Date(task.completed_at!);
          return acc + (completed.getTime() - created.getTime());
        }, 0) / completedTasksWithDates.length / (1000 * 60 * 60 * 24) // Convert to days
      : null;

    // Calculate productivity metrics
    const productivity = this.calculateProductivityMetrics(tasks);

    return {
      totalTasks: total,
      completedTasks: completed,
      pendingTasks: pending,
      onHoldTasks: onHold,
      cancelledTasks: cancelled,
      overdueTasks: overdue,

      highPriorityTasks: highPriority,
      mediumPriorityTasks: mediumPriority,
      lowPriorityTasks: lowPriority,
      completionRate,
      averageCompletionTime,
      productivity,
    };
  }

  /**
   * Calculate category breakdown with completion rates
   */
  private static calculateCategoryBreakdown(tasks: Task[]): CategoryBreakdown {
    const breakdown: CategoryBreakdown = {};
    
    tasks.forEach(task => {
      const category = task.category || 'General';
      if (!breakdown[category]) {
        breakdown[category] = { total: 0, completed: 0, percentage: 0 };
      }
      breakdown[category].total++;
      if (task.status === 'completed') {
        breakdown[category].completed++;
      }
    });
    
    // Calculate completion percentages
    Object.keys(breakdown).forEach(category => {
      const { total, completed } = breakdown[category];
      breakdown[category].percentage = total > 0 ? (completed / total) * 100 : 0;
    });
    
    return breakdown;
  }

  /**
   * Calculate productivity metrics
   */
  private static calculateProductivityMetrics(tasks: Task[]): AnalyticsMetrics['productivity'] {
    if (tasks.length === 0) {
      return { tasksPerDay: 0, completionStreak: 0 };
    }

    // Calculate tasks per day (average)
    const oldestTask = tasks.reduce((oldest, task) => 
      new Date(task.created_at) < new Date(oldest.created_at) ? task : oldest
    );
    const daysSinceOldest = Math.max(1, 
      (Date.now() - new Date(oldestTask.created_at).getTime()) / (1000 * 60 * 60 * 24)
    );
    const tasksPerDay = tasks.length / daysSinceOldest;

    // Calculate completion streak (consecutive days with completed tasks)
    const completedTasks = tasks
      .filter(t => t.status === 'completed' && t.completed_at)
      .sort((a, b) => new Date(b.completed_at!).getTime() - new Date(a.completed_at!).getTime());
    
    let completionStreak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    
    for (let i = 0; i < 30; i++) { // Check last 30 days
      const dayTasks = completedTasks.filter(task => {
        const completedDate = new Date(task.completed_at!);
        completedDate.setHours(0, 0, 0, 0);
        return completedDate.getTime() === currentDate.getTime();
      });
      
      if (dayTasks.length > 0) {
        completionStreak++;
      } else {
        break;
      }
      
      currentDate.setDate(currentDate.getDate() - 1);
    }

    return { tasksPerDay, completionStreak };
  }



  /**
   * Calculate performance metrics
   */
  private static calculatePerformanceMetrics(tasks: Task[], userId?: string): DetailedAnalytics['performanceMetrics'] {
    const completedTasks = tasks.filter(t => t.status === 'completed');
    
    // On-time completion rate
    const tasksWithDueDate = completedTasks.filter(t => t.due_date && t.completed_at);
    const onTimeCompletions = tasksWithDueDate.filter(t => 
      new Date(t.completed_at!) <= new Date(t.due_date!)
    ).length;
    const onTimeCompletion = tasksWithDueDate.length > 0 
      ? (onTimeCompletions / tasksWithDueDate.length) * 100 
      : 0;

    // Average task duration
    const tasksWithDuration = completedTasks.filter(t => t.created_at && t.completed_at);
    const avgTaskDuration = tasksWithDuration.length > 0
      ? tasksWithDuration.reduce((acc, task) => {
          const duration = new Date(task.completed_at!).getTime() - new Date(task.created_at).getTime();
          return acc + duration;
        }, 0) / tasksWithDuration.length / (1000 * 60 * 60 * 24) // Convert to days
      : null;

    // Most productive hours and days (placeholder - would need more detailed completion data)
    const mostProductiveHours: number[] = [];
    const mostProductiveDays: string[] = [];

    return {
      onTimeCompletion,
      avgTaskDuration,
      mostProductiveHours,
      mostProductiveDays,
    };
  }

  /**
   * Calculate trends by comparing current and previous metrics
   */
  private static calculateTrends(current: AnalyticsMetrics, previous: AnalyticsMetrics) {
    const calculateChange = (curr: number, prev: number) => 
      prev > 0 ? ((curr - prev) / prev) * 100 : 0;

    return {
      taskCreationTrend: calculateChange(current.totalTasks, previous.totalTasks),
      completionTrend: calculateChange(current.completionRate, previous.completionRate),
      productivityTrend: calculateChange(current.productivity.tasksPerDay, previous.productivity.tasksPerDay),
    };
  }

  /**
   * Get date range for time period
   */
  private static getTimeRangeDates(timeRange: string): { startDate: Date; endDate: Date } {
    const now = new Date();
    const endDate = new Date(now);
    let startDate = new Date(now);

    switch (timeRange) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default: // 'all'
        startDate = new Date(0); // Beginning of time
    }

    return { startDate, endDate };
  }

  /**
   * Filter tasks by date range and user
   */
  private static filterTasksByDateRange(
    tasks: Task[], 
    startDate: Date, 
    endDate: Date, 
    userId?: string
  ): Task[] {
    return tasks.filter(task => {
      const taskDate = new Date(task.created_at);
      const inDateRange = taskDate >= startDate && taskDate <= endDate;
      const isUserTask = !userId || task.user_id === userId;
      return inDateRange && isUserTask;
    });
  }

  /**
   * Get tasks from previous period for comparison
   */
  private static getPreviousPeriodTasks(
    tasks: Task[], 
    timeRange: string, 
    currentStart: Date, 
    currentEnd: Date, 
    userId?: string
  ): Task[] {
    const periodLength = currentEnd.getTime() - currentStart.getTime();
    const previousStart = new Date(currentStart.getTime() - periodLength);
    const previousEnd = new Date(currentStart);

    return this.filterTasksByDateRange(tasks, previousStart, previousEnd, userId);
  }

  /**
   * Calculate analytics for previous period
   */
  private static calculatePreviousPeriodAnalytics(
    tasks: Task[], 
    timeRange: 'week' | 'month' | 'quarter' | 'year' | 'all', 
    userId?: string
  ): TimeRangeAnalytics {
    const { startDate, endDate } = this.getTimeRangeDates(timeRange);
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousStart = new Date(startDate.getTime() - periodLength);
    const previousEnd = new Date(startDate);

    const previousTasks = this.filterTasksByDateRange(tasks, previousStart, previousEnd, userId);
    const metrics = this.calculateMetrics(previousTasks);
    const categoryBreakdown = this.calculateCategoryBreakdown(previousTasks);

    return {
      timeRange,
      startDate: previousStart,
      endDate: previousEnd,
      metrics,
      categoryBreakdown,
      trends: { taskCreationTrend: 0, completionTrend: 0, productivityTrend: 0 },
    };
  }

  /**
   * Generate analytics summary for export
   */
  static generateAnalyticsSummary(analytics: DetailedAnalytics): string {
    const { overview, timeComparison, categoryBreakdown } = analytics;
    
    return JSON.stringify({
      generatedAt: new Date().toISOString(),
      overview: {
        totalTasks: overview.totalTasks,
        completionRate: `${overview.completionRate.toFixed(1)}%`,
        productivity: overview.productivity,
        averageCompletionTime: overview.averageCompletionTime 
          ? `${overview.averageCompletionTime.toFixed(1)} days`
          : null,
      },
      comparison: {
        taskCreationTrend: `${timeComparison.current.trends.taskCreationTrend.toFixed(1)}%`,
        completionTrend: `${timeComparison.current.trends.completionTrend.toFixed(1)}%`,
        productivityTrend: `${timeComparison.current.trends.productivityTrend.toFixed(1)}%`,
      },
      categoryBreakdown: Object.entries(categoryBreakdown).map(([category, data]) => ({
        category,
        total: data.total,
        completed: data.completed,
        completionRate: `${data.percentage.toFixed(1)}%`,
      })),
      statusDistribution: {
        completed: overview.completedTasks,
        pending: overview.pendingTasks,
        onHold: overview.onHoldTasks,
        cancelled: overview.cancelledTasks,
        overdue: overview.overdueTasks,
      },
      priorityDistribution: {
        high: overview.highPriorityTasks,
        medium: overview.mediumPriorityTasks,
        low: overview.lowPriorityTasks,
      },
    }, null, 2);
  }
}