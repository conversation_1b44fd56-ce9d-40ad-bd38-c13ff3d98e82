import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { 
  Palette, 
  Type, 
  Eye, 
  Volume2, 
  Keyboard, 
  MousePointer,
  RotateCcw,
  Check
} from 'lucide-react';
import { useUnifiedTheme, Theme, ColorScheme, FontSize, Spacing as SpacingType } from '@/contexts/UnifiedThemeContext';
import { cn } from '@/lib/utils';

export const ThemeCustomization: React.FC = () => {
  const {
    settings,
    updateTheme,
    updateColorScheme,
    updateFontSize,
    updateSpacing,
    updateAccessibility,
    resetToDefaults
  } = useUnifiedTheme();

  const themeOptions: { value: Theme; label: string; description: string }[] = [
    { value: 'light', label: 'Light', description: 'Bright and clean interface' },
    { value: 'dark', label: 'Dark', description: 'Easy on the eyes' },
    { value: 'system', label: 'System', description: 'Match your device settings' }
  ];

  const colorSchemes: { value: ColorScheme; label: string; color: string }[] = [
    { value: 'blue', label: 'Blue', color: 'hsl(221, 83%, 53%)' },
    { value: 'green', label: 'Green', color: 'hsl(142, 76%, 36%)' },
    { value: 'purple', label: 'Purple', color: 'hsl(263, 70%, 50%)' },
    { value: 'orange', label: 'Orange', color: 'hsl(25, 95%, 53%)' },
    { value: 'pink', label: 'Pink', color: 'hsl(336, 75%, 40%)' }
  ];

  const fontSizes: { value: FontSize; label: string; example: string }[] = [
    { value: 'small', label: 'Small', example: '14px' },
    { value: 'medium', label: 'Medium', example: '16px' },
    { value: 'large', label: 'Large', example: '18px' },
    { value: 'xl', label: 'Extra Large', example: '20px' }
  ];

  const spacingOptions: { value: SpacingType; label: string; description: string }[] = [
    { value: 'compact', label: 'Compact', description: 'More content, less space' },
    { value: 'normal', label: 'Normal', description: 'Balanced spacing' },
    { value: 'comfortable', label: 'Comfortable', description: 'More breathing room' }
  ];

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Theme Customization</h2>
          <p className="text-muted-foreground">Personalize your experience with themes and accessibility options</p>
        </div>
        <Button
          variant="outline"
          onClick={resetToDefaults}
          className="flex items-center gap-2"
        >
          <RotateCcw className="h-4 w-4" />
          Reset to Defaults
        </Button>
      </div>

      {/* Theme Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Theme
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {themeOptions.map((option) => (
              <Button
                key={option.value}
                variant={settings.theme === option.value ? 'default' : 'outline'}
                onClick={() => updateTheme(option.value)}
                className={cn(
                  "h-auto p-4 flex flex-col items-start text-left",
                  settings.theme === option.value && "ring-2 ring-primary"
                )}
              >
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-medium">{option.label}</span>
                  {settings.theme === option.value && <Check className="h-4 w-4" />}
                </div>
                <span className="text-sm text-muted-foreground">
                  {option.description}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Color Scheme */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Color Scheme
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {colorSchemes.map((scheme) => (
              <Button
                key={scheme.value}
                variant={settings.colorScheme === scheme.value ? 'default' : 'outline'}
                onClick={() => updateColorScheme(scheme.value)}
                className={cn(
                  "h-auto p-4 flex flex-col items-center gap-2",
                  settings.colorScheme === scheme.value && "ring-2 ring-primary"
                )}
              >
                <div
                  className="w-8 h-8 rounded-full border-2 border-background shadow-sm"
                  style={{ backgroundColor: scheme.color }}
                />
                <span className="text-sm font-medium">{scheme.label}</span>
                {settings.colorScheme === scheme.value && (
                  <Check className="h-4 w-4" />
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Typography */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            Typography
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Font Size</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {fontSizes.map((size) => (
                  <Button
                    key={size.value}
                    variant={settings.fontSize === size.value ? 'default' : 'outline'}
                    onClick={() => updateFontSize(size.value)}
                    className={cn(
                      "h-auto p-3 flex flex-col",
                      settings.fontSize === size.value && "ring-2 ring-primary"
                    )}
                  >
                    <span className="font-medium text-sm">{size.label}</span>
                    <span className="text-xs text-muted-foreground">{size.example}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Spacing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Layout Spacing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {spacingOptions.map((option) => (
              <Button
                key={option.value}
                variant={settings.spacing === option.value ? 'default' : 'outline'}
                onClick={() => updateSpacing(option.value)}
                className={cn(
                  "h-auto p-4 flex flex-col items-start text-left",
                  settings.spacing === option.value && "ring-2 ring-primary"
                )}
              >
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-medium">{option.label}</span>
                  {settings.spacing === option.value && <Check className="h-4 w-4" />}
                </div>
                <span className="text-sm text-muted-foreground">
                  {option.description}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Accessibility */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Accessibility
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <label className="text-sm font-medium">Reduced Motion</label>
                <p className="text-xs text-muted-foreground">
                  Minimize animations and transitions
                </p>
              </div>
              <Switch
                checked={settings.accessibility.reducedMotion}
                onCheckedChange={(checked) => 
                  updateAccessibility({ reducedMotion: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <label className="text-sm font-medium">High Contrast</label>
                <p className="text-xs text-muted-foreground">
                  Increase contrast for better visibility
                </p>
              </div>
              <Switch
                checked={settings.accessibility.highContrast}
                onCheckedChange={(checked) => 
                  updateAccessibility({ highContrast: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <label className="text-sm font-medium">Screen Reader Support</label>
                <p className="text-xs text-muted-foreground">
                  Enhanced descriptions for screen readers
                </p>
              </div>
              <Switch
                checked={settings.accessibility.screenReader}
                onCheckedChange={(checked) => 
                  updateAccessibility({ screenReader: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <label className="text-sm font-medium">Keyboard Navigation</label>
                <p className="text-xs text-muted-foreground">
                  Navigate with keyboard shortcuts
                </p>
              </div>
              <Switch
                checked={settings.accessibility.keyboardNavigation}
                onCheckedChange={(checked) => 
                  updateAccessibility({ keyboardNavigation: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <label className="text-sm font-medium">Focus Indicators</label>
                <p className="text-xs text-muted-foreground">
                  Visible focus outlines for navigation
                </p>
              </div>
              <Switch
                checked={settings.accessibility.focusIndicators}
                onCheckedChange={(checked) => 
                  updateAccessibility({ focusIndicators: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <label className="text-sm font-medium">Announce Changes</label>
                <p className="text-xs text-muted-foreground">
                  Notify about page and content changes
                </p>
              </div>
              <Switch
                checked={settings.accessibility.announceChanges}
                onCheckedChange={(checked) => 
                  updateAccessibility({ announceChanges: checked })
                }
              />
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="text-xs">
                <Keyboard className="h-3 w-3 mr-1" />
                Tab Navigation
              </Badge>
              <Badge variant="outline" className="text-xs">
                <MousePointer className="h-3 w-3 mr-1" />
                Click Targets 44px+
              </Badge>
              <Badge variant="outline" className="text-xs">
                <Volume2 className="h-3 w-3 mr-1" />
                ARIA Labels
              </Badge>
              <Badge variant="outline" className="text-xs">
                <Eye className="h-3 w-3 mr-1" />
                WCAG 2.1 AA
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              This app follows web accessibility standards for inclusive design.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};