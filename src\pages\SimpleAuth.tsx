import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

const SimpleAuth = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Removed debug logging to improve performance

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', { email, password });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">
              Simple Test Login
            </CardTitle>
            <CardDescription>
              Testing input functionality
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    console.log('Email input changed:', e.target.value);
                    setEmail(e.target.value);
                  }}
                  placeholder="<EMAIL>"
                  onFocus={() => console.log('Email input focused')}
                  onBlur={() => console.log('Email input blurred')}
                  onClick={() => console.log('Email input clicked')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => {
                    console.log('Password input changed:', e.target.value);
                    setPassword(e.target.value);
                  }}
                  placeholder="password"
                  onFocus={() => console.log('Password input focused')}
                  onBlur={() => console.log('Password input blurred')}
                  onClick={() => console.log('Password input clicked')}
                />
              </div>

              <Button type="submit" className="w-full">
                Test Submit
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SimpleAuth;