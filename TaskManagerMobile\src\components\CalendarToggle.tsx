import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface CalendarToggleProps {
  viewMode: 'yearly' | 'monthly';
  onViewModeChange: (mode: 'yearly' | 'monthly') => void;
}

export function CalendarToggle({ viewMode, onViewModeChange }: CalendarToggleProps) {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          viewMode === 'yearly' && styles.activeButton,
        ]}
        onPress={() => onViewModeChange('yearly')}
      >
        <Text style={[
          styles.buttonText,
          viewMode === 'yearly' && styles.activeButtonText,
        ]}>
          Yearly
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.button,
          viewMode === 'monthly' && styles.activeButton,
        ]}
        onPress={() => onViewModeChange('monthly')}
      >
        <Text style={[
          styles.buttonText,
          viewMode === 'monthly' && styles.activeButtonText,
        ]}>
          Monthly
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 2,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  button: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeButton: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeButtonText: {
    color: '#1F2937',
    fontWeight: '600',
  },
});