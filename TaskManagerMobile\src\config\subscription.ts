// Mobile subscription configuration - mirrors web config
export const SUBSCRIPTION_PLANS = {
  free: {
    id: 'free',
    name: 'Free',
    limits: {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      maxStorageTotal: 50 * 1024 * 1024, // 50MB
      maxCommentsPerTask: 1,
      hasVoiceFeatures: false,
      hasAdvancedAnalytics: false,
      fileRetentionDays: 30,
      notificationTypes: ['email'],
    },
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    limits: {
      maxFileSize: 25 * 1024 * 1024, // 25MB
      maxStorageTotal: 500 * 1024 * 1024, // 500MB
      maxCommentsPerTask: -1, // Unlimited
      hasVoiceFeatures: true,
      hasAdvancedAnalytics: true,
      fileRetentionDays: 365,
      notificationTypes: ['email', 'sms'],
    },
  },
};

export const getSubscriptionPlan = (tier: string) => {
  return SUBSCRIPTION_PLANS[tier as keyof typeof SUBSCRIPTION_PLANS] || SUBSCRIPTION_PLANS.free;
};

export const getFileSizeLimit = (tier: string): number => {
  const plan = getSubscriptionPlan(tier);
  return plan.limits.maxFileSize;
};

export const getStorageLimit = (tier: string): number => {
  const plan = getSubscriptionPlan(tier);
  return plan.limits.maxStorageTotal;
};