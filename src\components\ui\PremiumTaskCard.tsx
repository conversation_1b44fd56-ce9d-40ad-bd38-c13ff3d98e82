import React from 'react';
import { 
  CheckCircle2, 
  Clock, 
  AlertCircle, 
 
  MapPin, 
  Calendar,
  MoreHorizontal,
  Edit3,
  Trash2,
  PlayCircle,
  PauseCircle
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { cn } from '@/lib/utils';
import { Task } from '@/types/task';
import { format, isToday, isTomorrow, isThisWeek } from 'date-fns';

interface PremiumTaskCardProps {
  task: Task;
  onEdit?: (task: Task) => void;
  onDelete?: (taskId: string) => void;
  onStatusChange?: (taskId: string, status: Task['status']) => void;
  onView?: (task: Task) => void;
  className?: string;
  compact?: boolean;
}

export const PremiumTaskCard: React.FC<PremiumTaskCardProps> = ({
  task,
  onEdit,
  onDelete,
  onStatusChange,
  onView,
  className,
  compact = false
}) => {
  const getPriorityConfig = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return {
          color: 'status-error',
          icon: AlertCircle,
          label: 'High Priority'
        };
      case 'medium':
        return {
          color: 'status-warning',
          icon: Clock,
          label: 'Medium Priority'
        };
      case 'low':
        return {
          color: 'status-success',
          icon: CheckCircle2,
          label: 'Low Priority'
        };
    }
  };

  const getStatusConfig = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return {
          color: 'status-success',
          icon: CheckCircle2,
          label: 'Completed'
        };

        return {
          color: 'status-warning',
          icon: PlayCircle,

        };
      case 'on-hold':
        return {
          color: 'status-warning',
          icon: PauseCircle,
          label: 'On Hold'
        };
      case 'cancelled':
        return {
          color: 'status-error',
          icon: AlertCircle,
          label: 'Cancelled'
        };
      default:
        return {
          color: 'text-muted-foreground',
          icon: Clock,
          label: 'Pending'
        };
    }
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    if (isThisWeek(date)) return format(date, 'EEEE');
    return format(date, 'MMM d');
  };

  const priorityConfig = getPriorityConfig(task.priority);
  const statusConfig = getStatusConfig(task.status);
  const PriorityIcon = priorityConfig.icon;
  const StatusIcon = statusConfig.icon;

  const isCompleted = task.status === 'completed';
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && !isCompleted;

  if (compact) {
    return (
      <Card 
        className={cn(
          "task-card cursor-pointer group border-l-4",
          isCompleted && "opacity-75",
          isOverdue && "border-l-destructive",
          task.priority === 'high' && !isOverdue && "border-l-destructive",
          task.priority === 'medium' && !isOverdue && "border-l-warning",
          task.priority === 'low' && !isOverdue && "border-l-success",
          className
        )}
        onClick={() => onView?.(task)}
      >
        <CardContent className="p-3">
          <div className="flex items-start justify-between space-x-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <StatusIcon className={cn("w-4 h-4", statusConfig.color)} />
                <h3 className={cn(
                  "font-medium text-sm text-premium truncate",
                  isCompleted && "line-through text-muted-foreground"
                )}>
                  {task.title}
                </h3>
              </div>
              
              <div className="flex items-center space-x-3 text-xs text-muted-foreground">
                <span className="flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(task.date)}
                </span>
                
                {task.location && (
                  <span className="flex items-center truncate">
                    <MapPin className="w-3 h-3 mr-1" />
                    {task.location}
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-1">
              <div className={cn("status-indicator", priorityConfig.color)}>
                <PriorityIcon className="w-3 h-3" />
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                  >
                    <MoreHorizontal className="w-3 h-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="dropdown-premium">
                  <DropdownMenuItem onClick={() => onEdit?.(task)}>
                    <Edit3 className="w-4 h-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete?.(task.id)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className={cn(
        "task-card group cursor-pointer",
        isCompleted && "opacity-80",
        isOverdue && "ring-1 ring-destructive/50",
        className
      )}
      onClick={() => onView?.(task)}
    >
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <div className="flex-shrink-0 mt-0.5">
              <StatusIcon className={cn("w-5 h-5", statusConfig.color)} />
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className={cn(
                "font-semibold text-lg text-premium mb-1",
                isCompleted && "line-through text-muted-foreground"
              )}>
                {task.title}
              </h3>
              
              {task.description && (
                <p className="text-muted-foreground text-sm line-clamp-2 mb-2">
                  {task.description}
                </p>
              )}
              
              {/* Meta Info */}
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <span className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {formatDate(task.date)}
                </span>
                
                {task.dueDate && (
                  <span className={cn(
                    "flex items-center",
                    isOverdue && "text-destructive font-medium"
                  )}>
                    <Clock className="w-4 h-4 mr-1" />
                    Due {formatDate(task.dueDate)}
                  </span>
                )}
                
                {task.location && (
                  <span className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {task.location.split(',')[0]}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(task);
                }}
                className="focus-premium"
              >
                <Edit3 className="w-4 h-4" />
              </Button>
            )}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="focus-premium"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="dropdown-premium">
                {onStatusChange && (
                  <>
                    <DropdownMenuItem onClick={() => onStatusChange(task.id, 'pending')}>
                      <Clock className="w-4 h-4 mr-2" />
                      Mark Pending
                    </DropdownMenuItem>
    
                      
                    <DropdownMenuItem onClick={() => onStatusChange(task.id, 'completed')}>
                      <CheckCircle2 className="w-4 h-4 mr-2" />
                      Mark Completed
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                {onDelete && (
                  <DropdownMenuItem 
                    onClick={() => onDelete(task.id)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Priority Badge */}
            <div className={cn("status-indicator", priorityConfig.color)}>
              <PriorityIcon className="w-4 h-4" />
              {priorityConfig.label}
            </div>
            
            {/* Category */}
            {task.category && (
              <Badge variant="secondary" className="text-xs">
                {task.category}
              </Badge>
            )}
            
            {/* Tags */}
            {task.tags?.slice(0, 2).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            
            {task.tags && task.tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{task.tags.length - 2} more
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};