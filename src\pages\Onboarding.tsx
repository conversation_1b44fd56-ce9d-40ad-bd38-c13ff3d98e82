import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { WelcomePage } from '@/components/onboarding/WelcomePage';
import { OnboardingTour } from '@/components/onboarding/OnboardingTour';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { TaskService } from '@/services/taskService';
import { useSupabaseTasks } from '@/hooks/useSupabaseTasks';
import { Task } from '@/types/task';
import { OnboardingService } from '@/services/onboardingService';
import { cn } from '@/lib/utils';

type OnboardingStep = 'signup' | 'welcome' | 'tour' | 'complete';

export const Onboarding: React.FC = () => {
  const { user } = useAuth();
  const isSignedIn = !!user;
  const userId = user?.id;
  const navigate = useNavigate();
  const { toast } = useToast();
  const { createTask } = useSupabaseTasks();
  
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('signup');
  const [isLoadingSamples, setIsLoadingSamples] = useState(false);
  const [showTour, setShowTour] = useState(false);

  // Check if user has completed onboarding
  useEffect(() => {
    if (isSignedIn && userId) {
      const hasCompletedOnboarding = OnboardingService.hasCompletedOnboarding(userId);
      
      if (hasCompletedOnboarding) {
        navigate('/');
        return;
      }
      
      // If signed in but no onboarding completed, show welcome
      setCurrentStep('welcome');
      OnboardingService.updateProgress(userId, 'welcome');
    } else if (!isSignedIn) {
      setCurrentStep('signup');
    }
  }, [isSignedIn, userId, navigate]);

  const handleSignUpComplete = () => {
    setCurrentStep('welcome');
  };

  const handleLoadSampleTasks = async () => {
    if (!userId) return;
    
    setIsLoadingSamples(true);
    try {
      const sampleTasks = OnboardingService.generateSampleTasks();

      for (const task of sampleTasks) {
        await createTask(task);
      }

      toast({
        title: 'Sample tasks loaded! 🎉',
        description: 'You now have some example tasks to explore',
      });

      // Mark samples as loaded and onboarding as completed
      OnboardingService.markSamplesLoaded(userId);
      OnboardingService.markOnboardingComplete(userId);
      setCurrentStep('complete');
      
      // Navigate to main app after a short delay
      setTimeout(() => {
        navigate('/');
      }, 1500);
      
    } catch (error) {
      console.error('Error loading sample tasks:', error);
      toast({
        title: 'Error loading samples',
        description: 'Failed to load sample tasks. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingSamples(false);
    }
  };

  const handleStartTour = () => {
    if (userId) {
      OnboardingService.updateProgress(userId, 'tour');
    }
    setShowTour(true);
    setCurrentStep('tour');
  };

  const handleTourComplete = () => {
    if (!userId) return;
    
    setShowTour(false);
    OnboardingService.markTourCompleted(userId);
    OnboardingService.markOnboardingComplete(userId);
    setCurrentStep('complete');
    
    toast({
      title: 'Welcome aboard! 🚀',
      description: 'You\'re all set to start managing your tasks',
    });
    
    // Navigate to main app after a short delay
    setTimeout(() => {
      navigate('/');
    }, 1500);
  };

  const handleSkipOnboarding = () => {
    if (!userId) return;
    
    OnboardingService.markOnboardingComplete(userId);
    
    // Show success message and redirect
    toast({
      title: 'Welcome! 🎉',
      description: 'Click the + button to create your first task',
    });
    
    navigate('/');
  };

  if (currentStep === 'signup' && !isSignedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <OnboardingSignUp onComplete={handleSignUpComplete} />
        </div>
      </div>
    );
  }

  if (currentStep === 'welcome' && isSignedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
        <WelcomePage
          onLoadSamples={handleLoadSampleTasks}
          onStartTour={handleStartTour}
          onSkip={handleSkipOnboarding}
          isLoadingSamples={isLoadingSamples}
          userName={user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'there'}
        />
      </div>
    );
  }

  if (currentStep === 'tour' && showTour) {
    return (
      <div className="min-h-screen">
        <OnboardingTour
          onComplete={handleTourComplete}
          onSkip={handleSkipOnboarding}
        />
      </div>
    );
  }

  if (currentStep === 'complete') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-8">
            <div className="mb-6">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎉</span>
              </div>
              <h2 className="text-2xl font-bold text-primary mb-2">
                You're all set!
              </h2>
              <p className="text-muted-foreground">
                Redirecting you to your task calendar...
              </p>
            </div>
            <div className="animate-spin w-6 h-6 border-2 border-green-500 border-t-transparent rounded-full mx-auto"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
};

// Signup component with simplified form
const OnboardingSignUp: React.FC<{ onComplete: () => void }> = ({ onComplete }) => {
  const navigate = useNavigate();

  return (
    <Card className="w-full shadow-lg border-0 bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
      <CardContent className="p-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">📋</span>
          </div>
          <h1 className="text-2xl font-bold mb-2">
            Get Started in 10 Seconds
          </h1>
          <p className="text-muted-foreground text-sm">
            Create your account and start organizing your tasks
          </p>
        </div>

        <div className="space-y-4">
          <Button 
            onClick={() => navigate('/auth')}
            className="w-full"
            size="lg"
          >
            Sign Up / Sign In
          </Button>
        </div>

        <div className="mt-4 text-center">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="text-xs text-muted-foreground hover:text-primary"
          >
            Continue without account
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default Onboarding;