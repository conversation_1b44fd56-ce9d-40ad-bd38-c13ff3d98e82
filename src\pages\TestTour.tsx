import React from 'react';
import { useNavigate } from 'react-router-dom';
import { OnboardingTour } from '@/components/onboarding/OnboardingTour';

const TestTour: React.FC = () => {
  const navigate = useNavigate();

  const handleComplete = () => {
    console.log('Tour completed');
    navigate('/');
  };

  const handleSkip = () => {
    console.log('Tour skipped');
    navigate('/');
  };

  return (
    <div className="min-h-screen">
      <OnboardingTour
        onComplete={handleComplete}
        onSkip={handleSkip}
      />
    </div>
  );
};

export default TestTour;
