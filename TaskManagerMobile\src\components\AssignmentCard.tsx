import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface AssignmentCardProps {
  assignment: any;
  onPress?: () => void;
}

export default function AssignmentCard({ assignment, onPress }: AssignmentCardProps) {
  return (
    <View style={styles.container}>
      <Text>Assignment Card</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    marginVertical: 4,
  },
});