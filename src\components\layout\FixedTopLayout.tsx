import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface FixedTopLayoutProps {
  children: ReactNode;
  className?: string;
  leftOffset?: string;
  centered?: boolean;
  height?: string;
  isCalendarCollapsed?: boolean;
}

export const FixedTopLayout = ({ children, className = '', leftOffset = 'left-0', centered = false, height = 'h-[42vh]', isCalendarCollapsed = false }: FixedTopLayoutProps) => (
  <div className={cn(
    `fixed top-16 ${isCalendarCollapsed ? 'h-0' : height} z-10 overflow-hidden transition-all duration-300 ease-in-out`,
    centered ? 'left-1/2 transform -translate-x-1/2 w-full max-w-6xl' : `right-0 ${leftOffset}`,
    className
  )}>
    {children}
  </div>
);