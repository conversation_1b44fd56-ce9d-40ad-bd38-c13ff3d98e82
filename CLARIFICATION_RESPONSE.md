# Technical Clarifications - Task Assignment Workflow

## 1. Friend Connection Channels & Validation

### Email Channel
- **Sender**: System email (<EMAIL>) - NOT user's personal email
- **Validation**: Database validation via unique invitation tokens
- **Process**:
  1. User enters friend's email in FriendsAndGroupsManager
  2. <PERSON> generates unique invitation token
  3. Email sent with secure invitation link
  4. Friend clicks link → creates account → friendship confirmed
  5. Token stored in `friend_invitations` table with expiration

### SMS Channel
- **Sender**: System SMS via Twilio/similar service
- **Validation**: Phone number verification via OTP
- **Process**: Same as email but via SMS link

### In-App Notification
- **Sender**: System notification
- **Validation**: Username search + existing user confirmation
- **Process**: Direct friend request within app

### Database Validation Schema
```sql
CREATE TABLE friend_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id UUID REFERENCES auth.users(id),
  recipient_email VARCHAR(255),
  recipient_phone VARCHAR(20),
  invitation_token VARCHAR(255) UNIQUE,
  status ENUM('pending', 'accepted', 'expired') DEFAULT 'pending',
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '7 days',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 2. Notification Preferences Integration

### Location: Existing NotificationSettings Page
**File**: `src/pages/NotificationSettings.tsx` (enhanced)

### New Preference Categories
```typescript
interface TaskAssignmentPreferences {
  emailNotifications: boolean;
  smsNotifications: boolean;
  inAppNotifications: boolean;
  autoAcceptAssignments: boolean;
  assignmentEmailFrequency: 'immediate' | 'daily' | 'weekly';
}
```

### UI Integration
- Add new section "Task Assignment Preferences"
- Toggle switches for each preference
- Maintains existing notification settings structure

## 3. Existing Assignment Enhancement Strategy

### Current State Analysis
The existing TaskModal has basic email assignment at lines 1095-1100. **Enhancement approach**:

### Enhancement Strategy
**Replace with enhanced friend picker** while maintaining backward compatibility:

1. **Primary**: Friend selection via FriendsAndGroupsManager picker mode
2. **Fallback**: Email entry for non-connected users
3. **Toggle**: Simple switch between "Friends" and "Email" modes

### Implementation
```typescript
// Enhanced TaskModal assignment section
const [assignmentMode, setAssignmentMode] = useState<'friends' | 'email'>('friends');
const [selectedFriends, setSelectedFriends] = useState<string[]>([]);
const [emailAssignments, setEmailAssignments] = useState<string[]>([]);

// UI: Radio buttons for mode selection
// Friends mode: Uses existing FriendsAndGroupsManager
// Email mode: Uses existing email input (current functionality)
```

## Updated Technical Specification

### Friend Connection Flow
```mermaid
sequenceDiagram
    participant User
    participant System
    participant Database
    participant Friend
    
    User->>System: Enter friend email/phone/username
    System->>Database: Check existing relationship
    Database-->>System: No existing relationship
    System->>System: Generate invitation token
    System->>Database: Store invitation
    System->>Friend: Send invitation (email/SMS/in-app)
    Friend->>System: Accept invitation
    System->>Database: Create friendship
    System->>User: Notification of new friend
```

### Notification Preference Storage
```sql
-- Add to existing user preferences
ALTER TABLE user_preferences 
ADD COLUMN task_assignment_email BOOLEAN DEFAULT true,
ADD COLUMN task_assignment_sms BOOLEAN DEFAULT false,
ADD COLUMN task_assignment_in_app BOOLEAN DEFAULT true,
ADD COLUMN auto_accept_assignments BOOLEAN DEFAULT false;
```

### Enhanced Assignment UI
The existing assignment section in TaskModal will be enhanced with:
1. **Mode toggle** (Friends/Email)
2. **Friend picker** (when Friends mode selected)
3. **Email input** (when Email mode selected, existing functionality)
4. **Preference link** to NotificationSettings

This approach maintains full backward compatibility while adding the requested functionality.