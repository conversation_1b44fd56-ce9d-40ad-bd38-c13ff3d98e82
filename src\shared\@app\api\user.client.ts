import { BaseApiClient } from './base.client';
import { UserProfile, ProfileData, ApiResponse } from '../types';

export class UserApiClient extends BaseApiClient {
  async getUserProfile(userId: string): Promise<ApiResponse<UserProfile>> {
    return this.get<UserProfile>(`/users/${userId}`);
  }

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    return this.patch<UserProfile>(`/users/${userId}`, updates);
  }

  async deleteUserProfile(userId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/users/${userId}`);
  }

  async syncProfile(userData: any): Promise<ApiResponse<ProfileData>> {
    return this.post<ProfileData>('/users/sync', userData);
  }

  async searchUsers(query: string): Promise<ApiResponse<UserProfile[]>> {
    return this.get<UserProfile[]>('/users/search', { query });
  }

  async getUsersByEmails(emails: string[]): Promise<ApiResponse<UserProfile[]>> {
    return this.post<UserProfile[]>('/users/by-emails', { emails });
  }
}