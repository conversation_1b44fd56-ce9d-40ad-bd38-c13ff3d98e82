import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Plus } from 'lucide-react';
import { ErrorBoundary } from '@/components/ui/error-boundary';

import { ContributionCalendar } from '@/components/calendar/ContributionCalendar';
import { TaskPanel } from '@/components/calendar/TaskPanel';
import { AnalyticsPanel } from '@/components/calendar/AnalyticsPanel';
import { LeftExplorerPanel } from '@/components/layout/LeftExplorerPanel';
import { FixedTopLayout } from '@/components/layout/FixedTopLayout';
import { DynamicBottomLayout } from '@/components/layout/DynamicBottomLayout';
import { AnalyticsModal } from '@/components/calendar/AnalyticsModal';
import { TaskModal } from '@/components/calendar/TaskModal';
import { TaskCreationChoice } from '@/components/calendar/TaskCreationChoice';
import { VoiceRecording } from '@/components/calendar/VoiceRecording';
import { ExportModal } from '@/components/export/ExportModal';



import { InstallPrompt, OfflineIndicator } from '@/components/ui/install-prompt';

import { useSupabaseTasks } from '@/hooks/useSupabaseTasks';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { StandardizedRecurringService } from '@/services/standardizedRecurringService';
import { supabase } from '@/lib/supabase';



import { useScreenSize } from '@/hooks/use-screen-size';
import { Button } from '@/components/ui/button';
import { MobileNavigation } from '@/components/layout/MobileNavigation';
import Navigation from '@/components/layout/Navigation';
import { Toaster } from '@/components/ui/toaster';
import { Task } from '@/types/task';
import { cn } from '@/lib/utils';

const IndexPage = () => {
  const { isMobile, isDesktop } = useScreenSize();
  const { user, signOut, loading } = useAuth();
  const navigate = useNavigate();
  const [isCalendarCollapsed, setIsCalendarCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  
  // Load state from localStorage on initial render
  const loadAppState = () => {
    try {
      const savedState = localStorage.getItem('app-ui-state');
      if (savedState) {
        const parsed = JSON.parse(savedState);
        return {
          selectedDate: parsed.selectedDate ? new Date(parsed.selectedDate) : null,
          currentYear: parsed.currentYear || new Date().getFullYear(),
          isDatePickerOpen: false,
          isAnalyticsModalOpen: false,
          isTaskModalOpen: false,
          isCreationChoiceOpen: false,
          isVoiceRecordingOpen: false,
          editingTask: null,
          pendingVoiceNote: null,
          isExportModalOpen: false,
          analyticsModalData: null,
          focusedTaskId: null,
          isExplorerCollapsed: parsed.isExplorerCollapsed || false,
    
      
        };
      }
    } catch (error) {
      console.error('Error loading app state:', error);
    }
    return {
      selectedDate: null,
      currentYear: new Date().getFullYear(),
      isDatePickerOpen: false,
      isAnalyticsModalOpen: false,
      isTaskModalOpen: false,
      isCreationChoiceOpen: false,
      isVoiceRecordingOpen: false,
      editingTask: null,
      pendingVoiceNote: null,
      isExportModalOpen: false,
      analyticsModalData: null,
      focusedTaskId: null,
      isExplorerCollapsed: false
  
    };
  };

  const [appState, setAppState] = useState(loadAppState);
  
  // Destructure state for easier access
  const {
    selectedDate,
    currentYear,
    isAnalyticsModalOpen,
    analyticsModalData,
    isTaskModalOpen,
    isDatePickerOpen,
    isExportModalOpen,
    isCreationChoiceOpen,
    isVoiceRecordingOpen,
    editingTask,
    pendingVoiceNote,
    focusedTaskId,
    isExplorerCollapsed,
    

  } = appState;

  // Save state to localStorage whenever it changes
  useEffect(() => {
    try {
      const stateToSave = {
        selectedDate: selectedDate ? selectedDate.toISOString() : null,
        currentYear,
        isExplorerCollapsed
      };
      localStorage.setItem('app-ui-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Error saving app state:', error);
    }
  }, [selectedDate, currentYear, isExplorerCollapsed]);

  // Update state setter
  const updateAppState = (updates: Partial<typeof appState>) => {
    setAppState(prev => ({ ...prev, ...updates }));
  };

  const {
    tasks,
    loading: tasksLoading,
    error,
    tasksByDate,
    createTask,
    updateTask,
    deleteTask,
    updateTaskStatus,
    updateTaskPriority,
    addComment,
    uploadFile,
    refetch
  } = useSupabaseTasks();

  const { toast } = useToast();



  const handleCalendarToggle = () => {
    setIsCalendarCollapsed(!isCalendarCollapsed);
  };

  const handleDateSelect = (date: Date) => updateAppState({ selectedDate: date });
  
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  
  const handleQuickAdd = () => {
    updateAppState({ isCreationChoiceOpen: true });
  };







  const handleTextTaskChoice = () => {
    // Check if selected date is in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate && selectedDate < today) {
      toast({
        title: 'Cannot Create Task',
        description: 'You cannot create new tasks for past dates. Please select today or a future date.',
        variant: 'destructive',
      });
      updateAppState({
        isCreationChoiceOpen: false
      });
      return;
    }
    
    updateAppState({
      isCreationChoiceOpen: false,
      editingTask: null,
      isTaskModalOpen: true
    });
  };

  const handleVoiceTaskChoice = () => {
    // Check if selected date is in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate && selectedDate < today) {
      toast({
        title: 'Cannot Create Task',
        description: 'You cannot create new tasks for past dates. Please select today or a future date.',
        variant: 'destructive',
      });
      updateAppState({
        isCreationChoiceOpen: false
      });
      return;
    }
    
    updateAppState({
      isCreationChoiceOpen: false,
      isVoiceRecordingOpen: true
    });
  };

  const handleVoiceNoteSaved = (voiceNote: any) => {
    updateAppState({
      isVoiceRecordingOpen: false,
      pendingVoiceNote: voiceNote,
      isTaskModalOpen: true
    });
  };

  const handleSaveTask = async (taskData: any) => {
    console.log('=== TASK SAVE DEBUG START ===');
    console.log('User object:', user);
    console.log('User ID:', user?.id);
    console.log('Task data:', taskData);
    
    try {
      if (pendingVoiceNote) {
        taskData.voiceNotes = [pendingVoiceNote];
      }
      
      if (taskData.id) {
        console.log('Updating existing task with ID:', taskData.id);
        // Updating existing task
        const { attachments, status, ...taskUpdateData } = taskData;
        
        // Always use updateTaskStatus for status changes to properly handle completedAt
        if (status !== undefined) {
          await updateTaskStatus(taskData.id, status);
          // Update other fields if any (excluding status since it's already updated)
          if (Object.keys(taskUpdateData).length > 0) {
            await updateTask(taskData.id, taskUpdateData);
          }
        } else {
          // Update the task with all data (no status change)
          await updateTask(taskData.id, taskUpdateData);
        }
        
        // Handle file uploads for existing task
        const fileAttachments = attachments?.filter((att: any) => att.file) || [];
        if (fileAttachments.length > 0) {
          for (const fileAtt of fileAttachments) {
            try {
              await uploadFile(fileAtt.file, taskData.id);
            } catch (error) {
              console.error('Error uploading file:', error);
            }
          }
          // Refresh tasks to show new attachments
          await refetch();
        }
        
        toast({
          title: 'Task Updated',
          description: 'Task has been updated successfully!',
        });
      } else {
        // Creating new task - handle file uploads after task creation
        console.log('Creating new task...');
        
        if (!user || !user.id) {
          console.error('No authenticated user found!');
          throw new Error('User not authenticated');
        }
        
        const fileAttachments = taskData.attachments?.filter((att: any) => att.file) || [];
        const urlAttachments = taskData.attachments?.filter((att: any) => typeof att === 'string') || [];
        
        // Create task with URL attachments only
        const newTaskData = {
          ...taskData,
          attachments: urlAttachments
        };
        
        console.log('Calling createTask with:', newTaskData);
        const createdTask = await createTask(newTaskData as Omit<Task, 'id' | 'createdAt' | 'updatedAt'>);
        console.log('Task created successfully:', createdTask);
        
        // Check if this is a recurring task and create recurring pattern
        if (taskData.recurringPattern && taskData.recurringPattern !== 'none' && createdTask) {
          try {
            // Create the recurring pattern based on the task data
            let pattern: any = {
              type: taskData.recurringPattern,
              frequency: taskData.recurringFrequency || 1,
              endDate: taskData.recurringEndDate ? new Date(taskData.recurringEndDate) : undefined
            };
            
            // Add pattern-specific data
            if (taskData.recurringPattern === 'weekly' && taskData.selectedDaysOfWeek) {
              pattern.daysOfWeek = taskData.selectedDaysOfWeek;
            } else if (taskData.recurringPattern === 'monthly' && taskData.selectedDayOfMonth) {
              pattern.dayOfMonth = taskData.selectedDayOfMonth;
            } else if (taskData.recurringPattern === 'yearly' && taskData.selectedMonths) {
              pattern.months = taskData.selectedMonths;
            }
            
            // Create the recurring task record
            await StandardizedRecurringService.createRecurringTask(
              {
                title: createdTask.title,
                description: createdTask.description || '',
                status: createdTask.status,
                priority: createdTask.priority,
                category: createdTask.category || '',
                date: createdTask.date,
                dueDate: createdTask.dueDate || createdTask.date
              },
              pattern
            );
            
            console.log('Recurring task pattern created successfully');
          } catch (recurringError) {
            console.error('Error creating recurring task pattern:', recurringError);
            // Don't fail the entire operation if recurring pattern creation fails
          }
        }
        
        // Upload files for the newly created task
        if (fileAttachments.length > 0 && createdTask) {
          const uploadedUrls = [];
          for (const fileAtt of fileAttachments) {
            try {
              const uploadedUrl = await uploadFile(fileAtt.file, createdTask.id);
              if (uploadedUrl) {
                uploadedUrls.push(uploadedUrl);
              }
            } catch (error) {
              console.error('Error uploading file:', error);
            }
          }
          
          // Update task with uploaded file URLs
          if (uploadedUrls.length > 0) {
            await updateTask(createdTask.id, {
              attachments: [...urlAttachments, ...uploadedUrls]
            });
            // Refresh tasks to show new attachments
            await refetch();
          }
        }
        
        toast({
          title: 'Task Created',
          description: taskData.recurringPattern && taskData.recurringPattern !== 'none' 
            ? 'Recurring task has been created successfully!' 
            : 'New task has been created successfully!',
        });
      }
      
      updateAppState({
        isTaskModalOpen: false,
        editingTask: null,
        pendingVoiceNote: null
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: taskData.id ? 'Failed to update task' : 'Failed to create task',
        variant: 'destructive',
      });
    }
  };

  const handleSettingsClick = () => {
    // Settings functionality is now available in the left panel cards
  };



  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        if (!isTaskModalOpen && !isAnalyticsModalOpen && !isCreationChoiceOpen && !isVoiceRecordingOpen) {
          handleQuickAdd();
        }
      }
      if (event.key === 'Escape') {
        updateAppState({
          isTaskModalOpen: false,
          isAnalyticsModalOpen: false,
          isCreationChoiceOpen: false,
          isVoiceRecordingOpen: false
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isTaskModalOpen, isAnalyticsModalOpen, isCreationChoiceOpen, isVoiceRecordingOpen]);

  // Handle today navigation from TaskPanel
  useEffect(() => {
    const handleNavigateToToday = (event: CustomEvent) => {
      const today = event.detail;
      updateAppState({
        selectedDate: today,
        currentYear: today.getFullYear()
      });
    };

    window.addEventListener('navigateToToday', handleNavigateToToday as EventListener);
    return () => window.removeEventListener('navigateToToday', handleNavigateToToday as EventListener);
  }, []);
  
  const handleAnalyticsCardClick = (cardId: string, tasks: Task[]) => {
    console.log('Analytics card clicked:', { cardId, tasks, tasksLength: tasks?.length });
    
    const title = cardId.charAt(0).toUpperCase() + cardId.slice(1).replace(/-/g, ' ') + ' (As on Today)';
    
    updateAppState({
      analyticsModalData: {
        cardType: cardId,
        data: tasks,
        title
      },
      isAnalyticsModalOpen: true
    });
  };
  
  const handleEditTask = (task: any) => {
    updateAppState({
      editingTask: task,
      isTaskModalOpen: true
    });
  };
  
  const handleAnalyticsTaskClick = (task: Task) => {
    // Close analytics modal and focus on the task
    updateAppState({
      isAnalyticsModalOpen: false,
      selectedDate: new Date(task.date), // Set the date to the task's date
      focusedTaskId: task.id // Set the focused task ID
    });
    
    // Clear the focused task after 10 seconds (increased from 3 seconds)
    // Only clear if the user hasn't interacted with other tasks
    setTimeout(() => {
      updateAppState({ focusedTaskId: null });
    }, 10000);
  };
  
  const handleDeleteTask = (taskId: string) => deleteTask(taskId);

  const handleAddComment = async (taskId: string, comment: string) => {
    console.log('Index.tsx - handleAddComment called:', { taskId, comment });
    try {
      const result = await addComment(taskId, comment);
      if (result) {
        toast({
          title: 'Comment Added',
          description: 'Your comment has been added successfully!',
        });
      }
      return result;
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to add comment. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleUpdateComment = (taskId: string, commentIndex: number, newText: string) => {
    console.log('Index.tsx - handleUpdateComment called:', { taskId, commentIndex, newText });
    try {
      const task = tasks.find(t => t.id === taskId);
      if (task && task.comments) {
        const updatedComments = [...task.comments];
        updatedComments[commentIndex] = {
          ...updatedComments[commentIndex],
          text: newText
        };
        updateTask(taskId, { comments: updatedComments });
        toast({
          title: 'Comment Updated',
          description: 'Your comment has been updated successfully!',
        });
      }
    } catch (error) {
      console.error('Error updating comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to update comment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteComment = (taskId: string, commentIndex: number) => {
    console.log('Index.tsx - handleDeleteComment called:', { taskId, commentIndex });
    try {
      const task = tasks.find(t => t.id === taskId);
      if (task && task.comments) {
        const updatedComments = task.comments.filter((_, index) => index !== commentIndex);
        updateTask(taskId, { comments: updatedComments });
        toast({
          title: 'Comment Deleted',
          description: 'Comment has been removed successfully!',
        });
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete comment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Test Supabase connection and authentication
  useEffect(() => {
    const testSupabaseConnection = async () => {
      if (user) {
        console.log('=== SUPABASE CONNECTION TEST ===');
        console.log('Authenticated user:', user);
        console.log('User ID:', user.id);
        console.log('User email:', user.email);
        
        try {
          // Test basic query
          const { data, error } = await supabase
            .from('profiles')
            .select('id, email')
            .eq('id', user.id)
            .single();
            
          console.log('Profile query result:', { data, error });
          
          if (error) {
            console.error('Supabase connection error:', error);
          } else {
            console.log('Supabase connection successful!');
          }
        } catch (err) {
          console.error('Supabase test error:', err);
        }
      }
    };
    
    testSupabaseConnection();
  }, [user]);

  // Redirect to auth if not signed in
  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  // Show loading state
  if (loading) {
    return (
      <DynamicBottomLayout>
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </DynamicBottomLayout>
    );
  }

  // Early return for non-signed-in users
  if (!user) {
    return null; // Will redirect to auth
  }

  return (
    <ErrorBoundary>
      <div className="relative h-screen bg-white dark:bg-gray-900">
        <InstallPrompt />
        <OfflineIndicator />

        {/* Desktop Navigation */}
        {!isMobile && (
          <Navigation
          onQuickAdd={handleQuickAdd}
          onCalendarToggle={handleCalendarToggle}
          isCalendarCollapsed={isCalendarCollapsed}
          onSearch={handleSearch}
          onDateSelect={handleDateSelect}
          onYearChange={(year) => updateAppState({ currentYear: year })}
          selectedDate={selectedDate}
          isDatePickerOpen={isDatePickerOpen}
          onDatePickerOpenChange={(open) => updateAppState({ isDatePickerOpen: open })}
        />
        )}

        {/* Mobile Navigation */}
        {isMobile && (
          <MobileNavigation
            onExportOpen={() => updateAppState({ isExportModalOpen: true })}
          />
        )}

        <div className={cn("flex h-full", isMobile && "pb-20", !isMobile && "pt-20")}>
          {/* Left Explorer Panel */}
          {!isMobile && (
            <div className={cn(
              "fixed left-0 top-16 bottom-0 z-20 transition-all duration-300 ease-in-out border-r border-gray-200/50 dark:border-gray-700/50 bg-white dark:bg-gray-900",
              isExplorerCollapsed ? "w-12" : "w-80"
            )}>
              <LeftExplorerPanel
                isCollapsed={isExplorerCollapsed}
                onToggleCollapse={() => updateAppState({ isExplorerCollapsed: !isExplorerCollapsed })}
                selectedDate={selectedDate}
                tasks={tasks}
                onAnalyticsCardClick={handleAnalyticsCardClick}
              />
            </div>
          )}

          {/* Main Content Area */}
          <div className="flex flex-col flex-1 h-full">
            {/* Fixed top section */}
            <FixedTopLayout
              className="bg-gradient-to-b from-white/90 to-gray-50/80 dark:from-gray-900/90 dark:to-gray-800/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50"
              leftOffset={isMobile ? "left-0" : (isExplorerCollapsed ? "left-12" : "left-80")}
              centered={!isMobile && isExplorerCollapsed}
              height={!isMobile && isExplorerCollapsed ? "h-[40vh]" : "h-[44vh]"}
              isCalendarCollapsed={isCalendarCollapsed}
            >
              <div className={cn(
                "w-full h-full overflow-hidden",
                !isMobile && isExplorerCollapsed ? "px-1" : "px-4"
              )}>
                <div className="space-y-4 pt-4 h-full overflow-hidden">
                  <ContributionCalendar
                    year={currentYear}
                    onYearChange={(year) => updateAppState({ currentYear: year })}
                    selectedDate={selectedDate}
                    onDateSelect={handleDateSelect}
                    tasksByDate={tasksByDate}
                    onSettingsClick={handleSettingsClick}
                    onSignOut={signOut}
                    isDatePickerOpen={isDatePickerOpen}
                    onDatePickerOpenChange={(open) => updateAppState({ isDatePickerOpen: open })}
                    onQuickAdd={handleQuickAdd}
                    isCompact={!isMobile && isExplorerCollapsed}
                  />
                </div>
              </div>
            </FixedTopLayout>

            {/* Dynamic bottom section */}
            <DynamicBottomLayout
              leftOffset={isMobile ? "left-0" : (isExplorerCollapsed ? "left-12" : "left-80")}
              centered={!isMobile && isExplorerCollapsed}
              topOffset={isMobile ? "top-[calc(4rem+40vh)]" : (isExplorerCollapsed ? "top-[calc(4rem+40vh)]" : "top-[calc(4rem+44vh)]")}
              isCalendarCollapsed={isCalendarCollapsed}
            >
              <div className={cn(
                "w-full h-full overflow-x-hidden scrollbar-visible",
                !isMobile && isExplorerCollapsed ? "px-1" : "px-4"
              )}>
                {/* Tasks Panel - Now takes full width */}
                <div className="h-full">
                  <TaskPanel
                    selectedDate={selectedDate}
                    tasks={tasks}
                    loading={tasksLoading}
                    onTaskUpdate={updateTask}
                    onTaskStatusUpdate={updateTaskStatus}
                    onTaskClick={handleEditTask}
                    onDeleteTask={handleDeleteTask}
                    onAddComment={handleAddComment}
                    onUpdateComment={handleUpdateComment}
                    onDeleteComment={handleDeleteComment}
                    onRefresh={refetch}
                    focusedTaskId={focusedTaskId}
                    searchQuery={searchQuery}
                    isCalendarCollapsed={isCalendarCollapsed}
                  />
                </div>
              </div>
            </DynamicBottomLayout>
          </div>
        </div>

        {/* Analytics Modal */}
        <AnalyticsModal
          isOpen={isAnalyticsModalOpen}
          onClose={() => updateAppState({ isAnalyticsModalOpen: false })}
          cardType={analyticsModalData?.cardType || ''}
          data={analyticsModalData?.data || []}
          title={analyticsModalData?.title || ''}
          onTaskClick={handleAnalyticsTaskClick}
          onUpdateTaskStatus={updateTaskStatus}
          onUpdateTaskPriority={updateTaskPriority}
        />



        {/* Task Creation Choice Modal */}
        <TaskCreationChoice
          isOpen={isCreationChoiceOpen}
          onClose={() => updateAppState({ isCreationChoiceOpen: false })}
          onTextTask={handleTextTaskChoice}
          onVoiceTask={handleVoiceTaskChoice}
        />

        {/* Voice Recording Modal */}
        <VoiceRecording
          isOpen={isVoiceRecordingOpen}
          onClose={() => updateAppState({ isVoiceRecordingOpen: false })}
          onVoiceNoteSaved={handleVoiceNoteSaved}
        />

        {/* Task Modal */}
        <TaskModal
          isOpen={isTaskModalOpen}
          onClose={() => {
            updateAppState({
              isTaskModalOpen: false,
              editingTask: null,
              pendingVoiceNote: null
            });
          }}
          onSave={handleSaveTask}
          selectedDate={selectedDate}
          editingTask={editingTask}
          tasks={tasks}
          onEditTask={handleEditTask}
          onDeleteTask={handleDeleteTask}
          onDeleteAttachment={() => {}}
          onUpdateTaskStatus={updateTaskStatus}
        />

        {/* Export Modal */}
        <ExportModal
          isOpen={isExportModalOpen}
          onClose={() => updateAppState({ isExportModalOpen: false })}
        />

        {/* Task Assignment Modal */}




        <Toaster />
      </div>
    </ErrorBoundary>
  );
};

export default IndexPage;