<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TaskFlow Pro - Professional Task Management</title>
    <meta name="description" content="Modern, privacy-first task management with advanced analytics, voice integration, and beautiful calendar interface. Boost your productivity with TaskFlow Pro." />
    <meta name="author" content="TaskFlow Pro" />
    <meta name="keywords" content="task management, productivity, calendar, analytics, voice integration, privacy-first, SaaS" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.svg" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="TaskFlow Pro - Professional Task Management" />
    <meta property="og:description" content="Modern, privacy-first task management with advanced analytics, voice integration, and beautiful calendar interface." />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:url" content="https://taskflowpro.com" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="TaskFlow Pro - Professional Task Management" />
    <meta name="twitter:description" content="Modern, privacy-first task management with advanced analytics, voice integration, and beautiful calendar interface." />
    <meta name="twitter:image" content="/og-image.png" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="msapplication-TileColor" content="#3b82f6" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
