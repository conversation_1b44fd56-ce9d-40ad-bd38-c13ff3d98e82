import React, { useState, useEffect } from 'react';
import { Calendar, Plus, Check, Clock, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Task } from '@/types/task';

interface DemoModeProps {
  onSignUp: () => void;
  onContinueDemo: () => void;
}

export const DemoMode: React.FC<DemoModeProps> = ({ onSignUp, onContinueDemo }) => {
  const [currentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [demoTasks, setDemoTasks] = useState<Task[]>([]);
  const [showSignupPrompt, setShowSignupPrompt] = useState(false);
  const [demoActions, setDemoActions] = useState(0);

  // Sample demo tasks
  const sampleTasks: Task[] = [
    {
      id: 'demo-1',
      title: 'Complete project proposal',
      description: 'Finish the Q4 project proposal for client review',
      date: new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      dueDate: new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'high',
      status: 'pending',
      category: 'work',
      tags: ['project', 'client'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'demo-2',
      title: 'Team meeting preparation',
      description: 'Prepare agenda for Monday team sync',
      date: new Date(currentDate.getTime() + 1 * 24 * 60 * 60 * 1000).toISOString(),
      dueDate: new Date(currentDate.getTime() + 1 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'medium',
      status: 'pending',
      category: 'work',
      tags: ['meeting', 'team'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'demo-3',
      title: 'Review design mockups',
      description: 'Provide feedback on new UI designs',
      date: new Date(currentDate.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      dueDate: new Date(currentDate.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'medium',
      status: 'completed',
      category: 'work',
      tags: ['design', 'review'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  useEffect(() => {
    setDemoTasks(sampleTasks);
  }, []);

  const handleDemoAction = () => {
    const newActions = demoActions + 1;
    setDemoActions(newActions);
    
    // Show signup prompt after 3 demo actions
    if (newActions >= 3) {
      setShowSignupPrompt(true);
    }
  };

  const getDaysInMonth = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    
    const days = [];
    for (let i = 0; i < firstDay; i++) {
      days.push(null);
    }
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(year, month, i));
    }
    
    return days;
  };

  const getTasksForDate = (date: Date) => {
    return demoTasks.filter(task => {
      const taskDate = new Date(task.dueDate || task.date);
      return taskDate.toDateString() === date.toDateString();
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            TaskCalendar Demo
          </h1>
          <p className="text-lg text-muted-foreground">
            Experience the power of visual task management
          </p>
          <Badge variant="outline" className="mt-2">
            Demo Mode - No signup required
          </Badge>
        </header>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Calendar View */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-7 gap-1">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                    <div key={day} className="text-center text-sm font-medium text-muted-foreground py-2">
                      {day}
                    </div>
                  ))}
                  {getDaysInMonth().map((date, index) => {
                    if (!date) return <div key={`empty-${index}`} />;
                    
                    const tasks = getTasksForDate(date);
                    const isSelected = selectedDate?.toDateString() === date.toDateString();
                    
                    return (
                      <div
                        key={date.toISOString()}
                        className={cn(
                          "aspect-square border rounded-lg p-2 cursor-pointer transition-all",
                          "hover:bg-primary/5 hover:border-primary/50",
                          isSelected && "bg-primary/10 border-primary",
                          date.toDateString() === new Date().toDateString() && "border-primary border-2"
                        )}
                        onClick={() => {
                          setSelectedDate(date);
                          handleDemoAction();
                        }}
                      >
                        <div className="text-sm font-medium">{date.getDate()}</div>
                        {tasks.length > 0 && (
                          <div className="flex gap-1 mt-1">
                            {tasks.slice(0, 3).map(task => (
                              <div
                                key={task.id}
                                className={cn(
                                  "w-2 h-2 rounded-full",
                                  getPriorityColor(task.priority)
                                )}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Task Details */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {selectedDate ? selectedDate.toLocaleDateString() : 'Select a date'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedDate ? (
                  <div className="space-y-3">
                    {getTasksForDate(selectedDate).map(task => (
                      <div
                        key={task.id}
                        className={cn(
                          "p-3 rounded-lg border",
                          task.status === 'completed' && "bg-green-50 dark:bg-green-900/20"
                        )}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{task.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">{task.description}</p>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className={cn("w-2 h-2 rounded-full", getPriorityColor(task.priority))} />
                            {task.status === 'completed' && <Check className="h-3 w-3 text-green-600" />}
                          </div>
                        </div>
                        <div className="flex gap-1 mt-2">
                          {task.tags.map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                    {getTasksForDate(selectedDate).length === 0 && (
                      <p className="text-sm text-muted-foreground text-center py-8">
                        No tasks for this date
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-8">
                    Click on a calendar date to see tasks
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Demo Actions */}
            <Card className="mt-4">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <Button
                    className="w-full"
                    onClick={() => {
                      const newTask: Task = {
                        id: `demo-${Date.now()}`,
                        title: 'New demo task',
                        description: 'This is a demo task you just created',
                        date: selectedDate?.toISOString() || new Date().toISOString(),
                        dueDate: selectedDate?.toISOString() || new Date().toISOString(),
                        priority: 'medium',
                        status: 'pending',
                        category: 'demo',
                        tags: ['demo', 'new'],
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString(),
                      };
                      setDemoTasks([...demoTasks, newTask]);
                      handleDemoAction();
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Demo Task
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      const taskToComplete = demoTasks.find(t => t.status === 'pending');
                      if (taskToComplete) {
                        setDemoTasks(demoTasks.map(t => 
                          t.id === taskToComplete.id ? { ...t, status: 'completed' } : t
                        ));
                        handleDemoAction();
                      }
                    }}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Complete Random Task
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Signup Prompt */}
        {showSignupPrompt && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="max-w-md mx-4">
              <CardHeader>
                <CardTitle className="text-center">
                  Ready to organize your real tasks?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-center text-muted-foreground mb-4">
                  You've seen how easy it is to manage tasks. Create your account to get started with your own tasks.
                </p>
                <div className="space-y-3">
                  <Button className="w-full" onClick={onSignUp}>
                    Create Free Account
                  </Button>
                  <Button variant="outline" className="w-full" onClick={onContinueDemo}>
                    Continue Exploring Demo
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Footer */}
        <div className="text-center mt-8">
          <Button variant="outline" onClick={onSignUp}>
            Ready to start? Create your account
          </Button>
        </div>
      </div>
    </div>
  );
};