const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Task Manager Mobile App...\n');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  .env file not found. Creating template...');
  const envTemplate = `# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_URL_HERE
EXPO_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY_HERE
`;
  fs.writeFileSync(envPath, envTemplate);
  console.log('✅ Created .env template file');
  console.log('📝 Please edit .env file with your Supabase credentials\n');
}

// Check if node_modules exists
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 Installing dependencies...');
  const install = spawn('npm', ['install'], { stdio: 'inherit', shell: true });
  
  install.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Dependencies installed successfully');
      startExpo();
    } else {
      console.log('❌ Failed to install dependencies');
    }
  });
} else {
  startExpo();
}

function startExpo() {
  console.log('🎯 Starting Expo development server...\n');
  const expo = spawn('npx', ['expo', 'start'], { stdio: 'inherit', shell: true });
  
  expo.on('close', (code) => {
    console.log(`Expo process exited with code ${code}`);
  });
}