import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Settings as SettingsIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { TasksEmptyState } from '@/components/ui/illustrated-empty-states';
import { ProgressiveOnboarding } from '@/components/ui/progressive-onboarding';
import { LazyWrapper, LazyAnalyticsDashboard, useIntersectionObserver } from '@/components/ui/performance-optimizations';
import { useAdvancedOnboarding, useContextualTips } from '@/hooks/useAdvancedOnboarding';
import { ContributionCalendar } from '@/components/calendar/ContributionCalendar';
import { ThemeToggle } from '@/components/calendar/ThemeToggle';
import { MobileNavigation } from '@/components/layout/MobileNavigation';
import { useScreenSize } from '@/hooks/use-mobile';
import { useSupabaseTasks } from '@/hooks/useSupabaseTasks';

interface EnhancedIndexPageProps {
  children: React.ReactNode;
}

export const EnhancedIndexPage: React.FC<EnhancedIndexPageProps> = ({ children }) => {
  const { isMobile } = useScreenSize();
  const { tasks } = useSupabaseTasks();
  const { shouldShowWelcome, markStepCompleted } = useAdvancedOnboarding();
  const { trackUserAction, showNextTip, currentTip } = useContextualTips();
  
  // State management
  const [showOnboarding, setShowOnboarding] = useState(shouldShowWelcome);
  const [showSettings, setShowSettings] = useState(false);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  
  // Intersection observer for lazy loading analytics
  const { elementRef: analyticsRef, isIntersecting: shouldLoadAnalytics } = useIntersectionObserver({
    threshold: 0.1,
    triggerOnce: true
  });

  // Check if we should show empty state
  const showTasksEmptyState = tasks.length === 0;

  // Handle onboarding completion
  const handleOnboardingComplete = () => {
    markStepCompleted('welcome');
    setShowOnboarding(false);
  };

  // Handle task creation with tracking
  const handleCreateTask = () => {
    trackUserAction();
    // This would trigger task creation modal
    if (tasks.length === 0) {
      markStepCompleted('first-task');
    }
  };

  // Show contextual tip after actions
  useEffect(() => {
    if (tasks.length > 0 && currentTip === null) {
      setTimeout(() => showNextTip(), 2000);
    }
  }, [tasks.length, currentTip, showNextTip]);

  if (showSettings) {
    return children; // Return the settings component
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-background"
    >
      {/* Progressive Onboarding */}
      <ProgressiveOnboarding
        isOpen={showOnboarding}
        onClose={() => setShowOnboarding(false)}
        onComplete={handleOnboardingComplete}
      />

      {/* Mobile Navigation */}
      {isMobile && (
        <MobileNavigation 
          onExportOpen={() => {}}
        />
      )}

      {/* Desktop Header */}
      {!isMobile && (
        <motion.header 
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="glass-card sticky top-0 z-40 border-b border-border/40 safe-area-padding-x"
        >
          <div className="container-premium flex items-center justify-between h-16">
            <motion.div 
              className="flex items-center space-x-4"
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center space-x-3">
                <motion.div 
                  className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center floating-element"
                  whileHover={{ rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <Calendar className="w-5 h-5 text-white" />
                </motion.div>
                <div>
                  <span className="text-responsive-xl font-bold text-premium">
                    TaskManager Pro
                  </span>
                  <div className="text-xs text-muted-foreground -mt-1">
                    Premium Task Management
                  </div>
                </div>
              </div>
            </motion.div>

            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowSettings(true)}
                className="focus-premium"
              >
                <SettingsIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </motion.header>
      )}

      {/* Main Content with Animation */}
      <motion.main 
        className="container-premium py-8"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <AnimatePresence mode="wait">
          {showTasksEmptyState ? (
            <motion.div 
              key="empty-state"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.4 }}
              className="flex items-center justify-center min-h-[60vh]"
            >
              <TasksEmptyState onCreateTask={handleCreateTask} />
            </motion.div>
          ) : (
            <motion.div
              key="main-content" 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4 }}
              className="grid grid-cols-1 lg:grid-cols-3 gap-8"
            >
              {/* Calendar Section */}
              <div className="lg:col-span-2 space-y-6">
                <motion.div 
                  className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <div>
                    <h1 className="text-responsive-2xl font-bold text-premium mb-2">
                      Task Calendar
                    </h1>
                    <p className="text-muted-foreground">
                      Track your productivity and manage your tasks
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.4 }}
                >
                  {children} {/* This would be the calendar component */}
                </motion.div>
              </div>

              {/* Analytics Section with Lazy Loading */}
              <motion.div 
                className="space-y-6"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <div ref={analyticsRef as any}>
                  {shouldLoadAnalytics ? (
                    <LazyWrapper>
                      <LazyAnalyticsDashboard
                        tasks={tasks}
                        onCardClick={() => {}}
                      />
                    </LazyWrapper>
                  ) : (
                    <motion.div 
                      className="h-96 glass-card rounded-xl flex items-center justify-center"
                      animate={{ 
                        background: [
                          "linear-gradient(45deg, hsl(var(--muted)) 0%, hsl(var(--muted)) 50%, transparent 50%)",
                          "linear-gradient(45deg, transparent 0%, hsl(var(--muted)) 50%, hsl(var(--muted)) 100%)"
                        ]
                      }}
                      transition={{ 
                        duration: 2, 
                        repeat: Infinity, 
                        ease: "easeInOut" 
                      }}
                    >
                      <div className="text-center">
                        <motion.div 
                          className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-3"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        >
                          <Calendar className="w-6 h-6 text-muted-foreground" />
                        </motion.div>
                        <p className="text-muted-foreground">Analytics loading...</p>
                      </div>
                    </motion.div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.main>
    </motion.div>
  );
};