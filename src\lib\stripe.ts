import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe
export const stripePromise = loadStripe(
  import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || ''
);

export const STRIPE_CONFIG = {
  publicKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
  webhookSecret: import.meta.env.STRIPE_WEBHOOK_SECRET || '',
  successUrl: `${window.location.origin}/subscription/success`,
  cancelUrl: `${window.location.origin}/subscription/cancel`,
  customerPortalUrl: `${window.location.origin}/subscription/portal`,
};

export const createCheckoutSessionUrl = (priceId: string, customerId?: string) => {
  const params = new URLSearchParams({
    price_id: priceId,
    success_url: STRIPE_CONFIG.successUrl,
    cancel_url: STRIPE_CONFIG.cancelUrl,
  });
  
  if (customerId) {
    params.append('customer_id', customerId);
  }
  
  return `/api/stripe/create-checkout-session?${params.toString()}`;
};

export const createCustomerPortalUrl = (customerId: string) => {
  return `/api/stripe/create-customer-portal?customer_id=${customerId}`;
};

// Stripe appearance theme
export const stripeAppearance = {
  theme: 'stripe' as const,
  variables: {
    colorPrimary: '#0F172A',
    colorBackground: '#ffffff',
    colorText: '#1f2937',
    colorDanger: '#dc2626',
    fontFamily: 'Inter, system-ui, sans-serif',
    spacingUnit: '4px',
    borderRadius: '8px',
  },
};

// Common Stripe options
export const stripeOptions = {
  appearance: stripeAppearance,
  loader: 'auto' as const,
};
