// Unified Input component for web and mobile platforms
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

const isReactNative = typeof window === 'undefined' || !!global.navigator?.product?.match(/ReactNative/);

const inputVariants = cva(
  'flex w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 caret-primary transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'border-input hover:border-primary/50 focus:border-primary/70',
        error: 'border-destructive focus-visible:ring-destructive',
        success: 'border-success focus-visible:ring-success',
      },
      size: {
        sm: 'h-8 px-2 text-sm',
        default: 'h-10 px-3',
        lg: 'h-12 px-4 text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface InputProps extends VariantProps<typeof inputVariants> {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  className?: string;
  style?: React.CSSProperties;
  testId?: string;
  autoComplete?: string;
  autoFocus?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  name?: string;
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-invalid'?: boolean;
}

// Web implementation
const WebInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    type = 'text',
    placeholder,
    value,
    defaultValue,
    onChange,
    onBlur,
    onFocus,
    disabled,
    readOnly,
    required,
    className,
    style,
    testId,
    variant,
    size,
    autoComplete,
    autoFocus,
    maxLength,
    minLength,
    pattern,
    name,
    id,
    ...props
  }, ref) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(e.target.value);
    };

    return (
      <input
        ref={ref}
        type={type}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        onChange={handleChange}
        onBlur={onBlur}
        onFocus={onFocus}
        disabled={disabled}
        readOnly={readOnly}
        required={required}
        className={inputVariants({ variant, size, className })}
        style={style}
        data-testid={testId}
        autoComplete={autoComplete}
        autoFocus={autoFocus}
        maxLength={maxLength}
        minLength={minLength}
        pattern={pattern}
        name={name}
        id={id}
        {...props}
      />
    );
  }
);

WebInput.displayName = 'WebInput';

// React Native implementation (placeholder)
const NativeInput = React.forwardRef<any, InputProps>(
  ({
    placeholder,
    value,
    defaultValue,
    onChange,
    onBlur,
    onFocus,
    disabled,
    readOnly,
    style,
    maxLength,
    ...props
  }, ref) => {
    return (
      <input
        ref={ref}
        placeholder={placeholder}
        value={value || defaultValue}
        onChange={(e: any) => onChange?.(e.target.value)}
        onBlur={onBlur}
        onFocus={onFocus}
        disabled={disabled || readOnly}
        maxLength={maxLength}
        style={{
          width: '100%',
          padding: 12,
          borderRadius: 8,
          border: '1px solid #ccc',
          backgroundColor: disabled ? '#f5f5f5' : 'white',
          fontSize: 16,
          color: '#333',
          ...style,
        }}
      />
    );
  }
);

NativeInput.displayName = 'NativeInput';

// Unified Input export
export const Input = React.forwardRef<HTMLInputElement, InputProps>((props, ref) => {
  if (isReactNative) {
    return <NativeInput {...props} ref={ref} />;
  }
  return <WebInput {...props} ref={ref} />;
});

Input.displayName = 'Input';

export { inputVariants };