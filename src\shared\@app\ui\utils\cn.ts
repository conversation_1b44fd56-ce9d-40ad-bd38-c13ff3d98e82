// Class name utility for combining Tailwind classes
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Platform-specific class name utilities
export function platformClass(webClass: string, nativeClass?: string) {
  const isReactNative = typeof window === 'undefined' || !!global.navigator?.product?.match(/ReactNative/);
  return isReactNative ? (nativeClass || '') : webClass;
}

// Responsive class name utilities
export function responsiveClass(classes: {
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
  '2xl'?: string;
  default?: string;
}) {
  const classNames: string[] = [];
  
  if (classes.default) classNames.push(classes.default);
  if (classes.sm) classNames.push(`sm:${classes.sm}`);
  if (classes.md) classNames.push(`md:${classes.md}`);
  if (classes.lg) classNames.push(`lg:${classes.lg}`);
  if (classes.xl) classNames.push(`xl:${classes.xl}`);
  if (classes['2xl']) classNames.push(`2xl:${classes['2xl']}`);
  
  return classNames.join(' ');
}

// Theme-aware class utilities
export function themeClass(lightClass: string, darkClass: string) {
  return `${lightClass} dark:${darkClass}`;
}

// State-based class utilities
export function stateClass(
  baseClass: string,
  states: {
    hover?: string;
    focus?: string;
    active?: string;
    disabled?: string;
    loading?: string;
  }
) {
  const classNames = [baseClass];
  
  if (states.hover) classNames.push(`hover:${states.hover}`);
  if (states.focus) classNames.push(`focus:${states.focus}`);
  if (states.active) classNames.push(`active:${states.active}`);
  if (states.disabled) classNames.push(`disabled:${states.disabled}`);
  if (states.loading) classNames.push(`data-[loading=true]:${states.loading}`);
  
  return classNames.join(' ');
}