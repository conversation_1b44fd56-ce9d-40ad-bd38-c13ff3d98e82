import { useState, useEffect } from 'react';
import { 
  <PERSON>evronR<PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Plus, 
  Calendar, 
  CheckCircle,
  Mic,
  Settings,
  ArrowRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface InteractiveDemoProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

interface DemoStep {
  id: string;
  title: string;
  description: string;
  target: string;
  position: 'top' | 'bottom' | 'left' | 'right';
  action?: string;
}

const demoSteps: DemoStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Task Manager!',
    description: 'Let\'s take a quick tour to help you get started with managing your tasks efficiently.',
    target: 'calendar-container',
    position: 'bottom',
  },
  {
    id: 'calendar',
    title: 'Interactive Calendar',
    description: 'Click on any date to view and manage tasks for that day. The heatmap shows your activity patterns.',
    target: 'calendar-heatmap',
    position: 'bottom',
    action: 'Click any date',
  },
  {
    id: 'add-task',
    title: 'Create Your First Task',
    description: 'Use the floating button or keyboard shortcut (Ctrl+N) to add a new task quickly.',
    target: 'fab-button',
    position: 'left',
    action: 'Click the + button',
  },
  {
    id: 'task-details',
    title: 'Task Details Panel',
    description: 'View all tasks for the selected date. Click on any task to edit or update its status.',
    target: 'task-details-panel',
    position: 'top',
  },
  {
    id: 'analytics',
    title: 'Analytics Dashboard',
    description: 'Switch to the Analytics tab to see your productivity insights and task completion rates.',
    target: 'analytics-tab',
    position: 'bottom',
  },
  {
    id: 'voice-notes',
    title: 'Voice Notes',
    description: 'Record voice notes for your tasks. Perfect for quick thoughts and detailed descriptions.',
    target: 'voice-recording',
    position: 'left',
  },
  {
    id: 'complete',
    title: 'You\'re All Set!',
    description: 'You now know the basics. Start adding tasks and track your productivity!',
    target: 'calendar-container',
    position: 'bottom',
  },
];

export const InteractiveDemo: React.FC<InteractiveDemoProps> = ({
  isOpen,
  onClose,
  onComplete,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [highlightedElement, setHighlightedElement] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && currentStep < demoSteps.length) {
      setHighlightedElement(demoSteps[currentStep].target);
    }
  }, [isOpen, currentStep]);

  const handleNext = () => {
    if (currentStep < demoSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    onClose();
    setCurrentStep(0);
  };

  const handleComplete = () => {
    onComplete();
    setCurrentStep(0);
  };

  const currentDemoStep = demoSteps[currentStep];

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-40" onClick={handleSkip} />

      {/* Demo tooltip */}
      <div
        className={cn(
          "fixed z-50 bg-white dark:bg-gray-800 rounded-lg shadow-2xl p-4 max-w-sm",
          "border border-gray-200 dark:border-gray-700",
          "top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        )}
      >
        <div className="flex items-start justify-between mb-3">
          <div>
            <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
              {currentDemoStep.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {currentDemoStep.description}
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSkip}
            className="h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        {currentDemoStep.action && (
          <div className="flex items-center gap-2 mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md">
            <MousePointer className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {currentDemoStep.action}
            </span>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="flex gap-1">
            {demoSteps.map((_, index) => (
              <div
                key={index}
                className={cn(
                  "h-1 w-8 rounded-full transition-colors",
                  index === currentStep
                    ? "bg-blue-600"
                    : index < currentStep
                    ? "bg-blue-300"
                    : "bg-gray-300 dark:bg-gray-600"
                )}
              />
            ))}
          </div>

          <div className="flex gap-2">
            {currentStep > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                className="h-7 text-xs"
              >
                Previous
              </Button>
            )}
            <Button
              size="sm"
              onClick={handleNext}
              className="h-7 text-xs"
            >
              {currentStep === demoSteps.length - 1 ? 'Finish' : 'Next'}
              <ChevronRight className="h-3 w-3 ml-1" />
            </Button>
          </div>
        </div>
      </div>

      {/* Interactive hints */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 border">
          <div className="flex items-center gap-2 text-sm">
            <Settings className="h-4 w-4" />
            <span>Demo Mode Active</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSkip}
              className="h-6 text-xs"
            >
              Skip Tour
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};