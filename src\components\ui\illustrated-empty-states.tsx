import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, CheckCircle, Users, Settings, Plus, Sparkles, Target, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface IllustratedEmptyStateProps {
  type: 'tasks' | 'calendar' | 'analytics' | 'search' | 'settings';
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'premium';
  };
  className?: string;
}

const illustrations = {
  tasks: {
    icon: CheckCircle,
    gradient: "from-blue-500 to-cyan-500",
    bgPattern: "✓",
    image: "photo-1581090464777-f3220bbe1b8b" // Person with light bulb - represents ideas/productivity
  },
  calendar: {
    icon: Calendar,
    gradient: "from-purple-500 to-pink-500",
    bgPattern: "📅",
    image: "photo-1470813740244-df37b8c1edcb" // Starry night - represents planning/future
  },

  analytics: {
    icon: Target,
    gradient: "from-orange-500 to-red-500",
    bgPattern: "📊",
    image: "photo-1500375592092-40eb2168fd21" // Ocean wave - represents flow/data
  },
  search: {
    icon: Zap,
    gradient: "from-yellow-500 to-orange-500",
    bgPattern: "🔍",
    image: "photo-1535268647677-300dbf3078d1" // Kitten - represents searching/discovery
  },
  settings: {
    icon: Settings,
    gradient: "from-gray-500 to-slate-500",
    bgPattern: "⚙️",
    image: "photo-1485827404703-89b55fcc595e" // Robot - represents configuration
  }
};

export const IllustratedEmptyState: React.FC<IllustratedEmptyStateProps> = ({
  type,
  title,
  description,
  action,
  className
}) => {
  const config = illustrations[type];
  const IconComponent = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={cn("relative", className)}
    >
      <Card className="glass-card border-0 shadow-xl overflow-hidden">
        <CardContent className="relative p-8 md:p-12 text-center">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5 text-6xl flex items-center justify-center">
            <div className="text-grid">
              {Array.from({ length: 20 }, (_, i) => (
                <span key={i} className="animate-pulse" style={{ animationDelay: `${i * 100}ms` }}>
                  {config.bgPattern}
                </span>
              ))}
            </div>
          </div>

          {/* Illustration */}
          <div className="relative mb-8">
            {/* Main illustration container */}
            <motion.div
              initial={{ scale: 0, rotate: -10 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ 
                type: "spring", 
                stiffness: 200, 
                damping: 20, 
                delay: 0.2 
              }}
              className="relative mx-auto w-32 h-32 md:w-40 md:h-40"
            >
              {/* Gradient background */}
              <div className={cn(
                "absolute inset-0 rounded-full bg-gradient-to-br opacity-20",
                config.gradient
              )} />
              
              {/* Floating elements */}
              <motion.div
                animate={{
                  y: [0, -8, 0],
                  rotate: [0, 5, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg"
              >
                <Sparkles className="w-4 h-4 text-yellow-800" />
              </motion.div>

              <motion.div
                animate={{
                  y: [0, 8, 0],
                  rotate: [0, -5, 0]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
                className="absolute -bottom-2 -left-4 w-6 h-6 bg-pink-400 rounded-full shadow-lg opacity-80"
              />

              {/* Main icon */}
              <div className={cn(
                "absolute inset-4 rounded-full bg-gradient-to-br flex items-center justify-center text-white shadow-2xl",
                config.gradient
              )}>
                <IconComponent className="w-12 h-12 md:w-16 md:h-16" />
              </div>
            </motion.div>

            {/* Secondary floating icons */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="absolute top-1/2 left-0 transform -translate-y-1/2"
            >
              <motion.div
                animate={{ x: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-4 h-4 bg-blue-400 rounded-full opacity-60"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="absolute top-1/4 right-8 transform"
            >
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                className="w-3 h-3 border-2 border-purple-400 rounded-full opacity-40"
              />
            </motion.div>
          </div>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="relative z-10"
          >
            <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
              {title}
            </h3>
            <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto leading-relaxed">
              {description}
            </p>

            {action && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6 }}
              >
                <Button 
                  onClick={action.onClick}
                  variant={action.variant || "premium"}
                  size="lg"
                  className="shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-200"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  {action.label}
                </Button>
              </motion.div>
            )}
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Pre-configured empty states for different sections
export const TasksEmptyState: React.FC<{ onCreateTask: () => void }> = ({ onCreateTask }) => (
  <IllustratedEmptyState
    type="tasks"
    title="No tasks yet"
    description="Start your productivity journey by creating your first task. Organize, prioritize, and accomplish your goals."
    action={{
      label: "Create Your First Task",
      onClick: () => {
        console.log('TasksEmptyState button clicked');
        onCreateTask();
      },
      variant: "premium"
    }}
  />
);

export const CalendarEmptyState: React.FC<{ onAddEvent: () => void }> = ({ onAddEvent }) => (
  <IllustratedEmptyState
    type="calendar"
    title="Your calendar is clear"
    description="Plan your days effectively by scheduling tasks and events. A well-organized calendar leads to better productivity."
    action={{
      label: "Add Event",
      onClick: onAddEvent
    }}
  />
);



export const SearchEmptyState: React.FC = () => (
  <IllustratedEmptyState
    type="search"
    title="No results found"
    description="Try adjusting your search terms or filters. We'll help you find what you're looking for."
  />
);

export const AnalyticsEmptyState: React.FC = () => (
  <IllustratedEmptyState
    type="analytics"
    title="Not enough data yet"
    description="Complete a few tasks to see your productivity insights. Your analytics will appear here as you work."
  />
);