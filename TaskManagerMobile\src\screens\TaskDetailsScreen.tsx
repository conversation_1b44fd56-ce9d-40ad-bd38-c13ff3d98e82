import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { format } from 'date-fns';
import { supabase } from '../lib/supabase';
import { Task } from '../types/task';
import { RootStackParamList } from '../navigation/AppNavigator';

type TaskDetailsScreenRouteProp = RouteProp<RootStackParamList, 'TaskDetails'>;

const TaskDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<TaskDetailsScreenRouteProp>();
  const { taskId } = route.params;
  
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchTaskDetails = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (error) throw error;

      setTask(data);
    } catch (error) {
      console.error('Error fetching task details:', error);
      Alert.alert('Error', 'Failed to load task details');
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  useEffect(() => {
    fetchTaskDetails();
  }, [fetchTaskDetails]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'in-progress': return '#F59E0B';
      case 'pending': return '#6B7280';
      case 'cancelled': return '#EF4444';
      case 'on-hold': return '#8B5CF6';
      default: return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return 'checkmark-circle';
      case 'in-progress': return 'play-circle';
      case 'pending': return 'time';
      case 'cancelled': return 'close-circle';
      case 'on-hold': return 'pause-circle';
      default: return 'help-circle';
    }
  };

  const updateTaskStatus = async (newStatus: string) => {
    if (!task) return;

    const completedAt = newStatus === 'completed' ? new Date().toISOString() : null;

    try {
      const { error } = await supabase
        .from('tasks')
        .update({ 
          status: newStatus,
          completed_at: completedAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      if (error) throw error;

      setTask(prev => prev ? { ...prev, status: newStatus as any, completedAt } : prev);
    } catch (error) {
      console.error('Error updating task status:', error);
      Alert.alert('Error', 'Failed to update task status');
    }
  };

  const deleteTask = async () => {
    if (!task) return;

    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('tasks')
                .delete()
                .eq('id', task.id);

              if (error) throw error;

              navigation.goBack();
            } catch (error) {
              console.error('Error deleting task:', error);
              Alert.alert('Error', 'Failed to delete task');
            }
          },
        },
      ]
    );
  };

  const addComment = async () => {
    if (!task) return;

    Alert.prompt(
      'Add Comment',
      'Enter your comment:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Add',
          onPress: async (commentText) => {
            if (!commentText) return;

            const newComment = {
              id: Date.now().toString(),
              text: commentText,
              timestamp: new Date().toISOString(),
            };

            const updatedComments = [...(task.comments || []), newComment];

            try {
              const { error } = await supabase
                .from('tasks')
                .update({ 
                  comments: updatedComments,
                  updated_at: new Date().toISOString()
                })
                .eq('id', task.id);

              if (error) throw error;

              setTask(prev => prev ? { ...prev, comments: updatedComments } : prev);
            } catch (error) {
              console.error('Error adding comment:', error);
              Alert.alert('Error', 'Failed to add comment');
            }
          },
        },
      ],
      'plain-text'
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading task details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!task) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Task not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>{task.title}</Text>
              <View style={styles.metaInfo}>
                <Text style={styles.dateText}>
                  {format(new Date(task.date), 'MMM dd, yyyy')}
                </Text>
                {task.category && (
                  <View style={styles.categoryBadge}>
                    <Ionicons name="folder-outline" size={12} color="#6B7280" />
                    <Text style={styles.categoryText}>{task.category}</Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Description */}
          {task.description && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text style={styles.description}>{task.description}</Text>
            </View>
          )}

          {/* Status and Priority */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Status & Priority</Text>
            <View style={styles.statusPriorityRow}>
              <View style={styles.statusContainer}>
                <TouchableOpacity
                  style={[
                    styles.statusButton,
                    { backgroundColor: getStatusColor(task.status || 'pending') + '20' }
                  ]}
                  onPress={() => {
                    const statusOptions = [
                      { text: 'Cancel', style: 'cancel' as const },
                      { text: 'Pending', onPress: () => updateTaskStatus('pending') },
                      { text: 'In Progress', onPress: () => updateTaskStatus('in-progress') },
                      { text: 'Completed', onPress: () => updateTaskStatus('completed') },
                      { text: 'On Hold', onPress: () => updateTaskStatus('on-hold') },
                      { text: 'Cancelled', onPress: () => updateTaskStatus('cancelled') },
                    ];
                    Alert.alert('Change Status', 'Select new status:', statusOptions);
                  }}
                >
                  <Ionicons
                    name={getStatusIcon(task.status || 'pending')}
                    size={20}
                    color={getStatusColor(task.status || 'pending')}
                  />
                  <Text style={[styles.statusText, { color: getStatusColor(task.status || 'pending') }]}>
                    {(task.status || 'pending').replace('-', ' ').toUpperCase()}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.priorityContainer}>
                <View style={[
                  styles.priorityBadge,
                  { backgroundColor: getPriorityColor(task.priority) + '20' }
                ]}>
                  <View style={[
                    styles.priorityDot,
                    { backgroundColor: getPriorityColor(task.priority) }
                  ]} />
                  <Text style={[styles.priorityText, { color: getPriorityColor(task.priority) }]}>
                    {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Tags */}
          {task.tags && task.tags.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Tags</Text>
              <View style={styles.tagsContainer}>
                {task.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Links */}
          {task.links && task.links.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Links</Text>
              {task.links.map((link, index) => (
                <View key={index} style={styles.linkItem}>
                  <Ionicons name="link-outline" size={16} color="#3B82F6" />
                  <Text style={styles.linkText}>{link}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Attachments */}
          {task.attachments && task.attachments.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Attachments</Text>
              {task.attachments.map((attachment, index) => (
                <TouchableOpacity key={index} style={styles.attachmentItem}>
                  <Ionicons name="document-outline" size={20} color="#6B7280" />
                  <Text style={styles.attachmentText}>{attachment.toString()}</Text>
                  <Ionicons name="download-outline" size={16} color="#3B82F6" />
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Voice Notes */}
          {task.voiceNotes && task.voiceNotes.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Voice Notes</Text>
              {task.voiceNotes.map((note, index) => (
                <TouchableOpacity key={index} style={styles.voiceNoteItem}>
                  <Ionicons name="mic-outline" size={20} color="#8B5CF6" />
                  <Text style={styles.voiceNoteText}>Voice Note {index + 1}</Text>
                  <Ionicons name="play-outline" size={16} color="#8B5CF6" />
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Comments */}
          <View style={styles.section}>
            <View style={styles.commentsHeader}>
              <Text style={styles.sectionTitle}>Comments</Text>
              <TouchableOpacity onPress={addComment} style={styles.addCommentButton}>
                <Ionicons name="add" size={16} color="#3B82F6" />
                <Text style={styles.addCommentText}>Add</Text>
              </TouchableOpacity>
            </View>
            
            {task.comments && task.comments.length > 0 ? (
              task.comments.map((comment, index) => (
                <View key={index} style={styles.commentItem}>
                  <View style={styles.commentHeader}>
                    <Text style={styles.commentAuthor}>You</Text>
                    <Text style={styles.commentTime}>
                      {format(new Date(comment.timestamp), 'MMM dd, HH:mm')}
                    </Text>
                  </View>
                  <Text style={styles.commentText}>{comment.text}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.noCommentsText}>No comments yet</Text>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity onPress={deleteTask} style={styles.deleteButton}>
              <Ionicons name="trash-outline" size={20} color="white" />
              <Text style={styles.deleteButtonText}>Delete Task</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
  },
  header: {
    marginBottom: 24,
  },
  titleContainer: {
    gap: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    lineHeight: 34,
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  dateText: {
    fontSize: 14,
    color: '#6B7280',
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#EFF6FF',
    borderRadius: 8,
    gap: 4,
  },
  categoryText: {
    fontSize: 12,
    color: '#1D4ED8',
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
  },
  statusPriorityRow: {
    flexDirection: 'row',
    gap: 12,
  },
  statusContainer: {
    flex: 1,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  priorityContainer: {
    flex: 1,
  },
  priorityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },
  linkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  linkText: {
    fontSize: 14,
    color: '#3B82F6',
    textDecorationLine: 'underline',
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  attachmentText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
  },
  voiceNoteItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  voiceNoteText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
  },
  commentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addCommentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  addCommentText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  commentItem: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  commentAuthor: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1F2937',
  },
  commentTime: {
    fontSize: 12,
    color: '#6B7280',
  },
  commentText: {
    fontSize: 14,
    color: '#4B5563',
  },
  noCommentsText: {
    fontSize: 14,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
  actionButtons: {
    marginTop: 24,
    marginBottom: 32,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EF4444',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TaskDetailsScreen;
