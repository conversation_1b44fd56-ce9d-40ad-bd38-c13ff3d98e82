import React, { useState } from 'react';
import { Download, X, Smartphone, Monitor } from 'lucide-react';
import { Button } from './button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { usePWA, useServiceWorker } from '@/hooks/use-pwa';
import { useScreenSize } from '@/hooks/use-mobile';

export function InstallPrompt() {
  const { isInstallable, isInstalled, installApp } = usePWA();
  const { isMobile } = useScreenSize();
  const [isDismissed, setIsDismissed] = useState(false);

  if (!isInstallable || isInstalled || isDismissed) {
    return null;
  }

  const handleInstall = async () => {
    const success = await installApp();
    if (!success) {
      setIsDismissed(true);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
  };

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 mx-auto max-w-md shadow-lg border-2 border-primary/20 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isMobile ? (
              <Smartphone className="h-5 w-5 text-primary" />
            ) : (
              <Monitor className="h-5 w-5 text-primary" />
            )}
            <CardTitle className="text-sm">Install App</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="h-6 w-6 p-0"
            aria-label="Dismiss install prompt"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription className="text-xs">
          Install Task Manager for quick access and offline use
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex gap-2">
          <Button
            onClick={handleInstall}
            size="sm"
            className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0"
          >
            <Download className="h-4 w-4 mr-2" />
            Install
          </Button>
          <Button
            onClick={handleDismiss}
            variant="outline"
            size="sm"
            className="px-3"
          >
            Later
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Offline indicator component
export function OfflineIndicator() {
  const { isOnline } = usePWA();

  if (isOnline) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-yellow-900 text-center py-2 px-4 text-sm font-medium z-50">
      <div className="flex items-center justify-center gap-2">
        <div className="w-2 h-2 bg-yellow-900 rounded-full animate-pulse"></div>
        You're offline. Changes will sync when you're back online.
      </div>
    </div>
  );
}

// Update available notification
export function UpdateNotification() {
  const [isVisible, setIsVisible] = useState(true);
  
  // Connect to service worker update detection
  const { updateAvailable, updateServiceWorker } = useServiceWorker();

  if (!updateAvailable || !isVisible) {
    return null;
  }

  const handleUpdate = () => {
    // Trigger service worker update
    updateServiceWorker();
    setIsVisible(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  return (
    <Card className="fixed top-4 left-4 right-4 z-50 mx-auto max-w-md shadow-lg border-2 border-green-500/20 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-green-700 dark:text-green-300">
            Update Available
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="h-6 w-6 p-0"
            aria-label="Dismiss update notification"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription className="text-xs text-green-600 dark:text-green-400">
          A new version of the app is available
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex gap-2">
          <Button
            onClick={handleUpdate}
            size="sm"
            className="flex-1 bg-green-600 hover:bg-green-700 text-white"
          >
            Update Now
          </Button>
          <Button
            onClick={handleDismiss}
            variant="outline"
            size="sm"
            className="px-3"
          >
            Later
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
