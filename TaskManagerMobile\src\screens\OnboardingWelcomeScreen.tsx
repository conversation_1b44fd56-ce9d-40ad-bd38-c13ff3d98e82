import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface OnboardingWelcomeScreenProps {
  onLoadSamples: () => void;
  onStartTour: () => void;
  onSkip: () => void;
  isLoadingSamples: boolean;
  userName: string;
}

export const OnboardingWelcomeScreen: React.FC<OnboardingWelcomeScreenProps> = ({
  onLoadSamples,
  onStartTour,
  onSkip,
  isLoadingSamples,
  userName,
}) => {
  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name="calendar-outline" size={48} color="#fff" />
          </View>
          <Text style={styles.title}>
            Welcome to TaskCalendar, {userName}! 🎉
          </Text>
          <Text style={styles.subtitle}>
            Your personal task management system that helps you stay organized and productive
          </Text>
        </View>

        {/* Features */}
        <View style={styles.featuresContainer}>
          <View style={styles.featureCard}>
            <Ionicons name="calendar-outline" size={24} color="#3b82f6" />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Tap dates to view tasks</Text>
              <Text style={styles.featureDescription}>
                Navigate through your calendar and tap on any date to see your tasks
              </Text>
            </View>
          </View>

          <View style={styles.featureCard}>
            <Ionicons name="add-circle-outline" size={24} color="#3b82f6" />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Quick add tasks</Text>
              <Text style={styles.featureDescription}>
                Use the + button to quickly add new tasks anywhere
              </Text>
            </View>
          </View>

          <View style={styles.featureCard}>
            <Ionicons name="time-outline" size={24} color="#3b82f6" />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Set priorities</Text>
              <Text style={styles.featureDescription}>
                Organize tasks with priority levels and never miss deadlines
              </Text>
            </View>
          </View>

          <View style={styles.featureCard}>
            <Ionicons name="checkmark-circle-outline" size={24} color="#3b82f6" />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Track progress</Text>
              <Text style={styles.featureDescription}>
                Mark tasks complete and watch your productivity grow
              </Text>
            </View>
          </View>
        </View>

        {/* Features Preview */}
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Key Features</Text>
          <View style={styles.featuresGrid}>
            <View style={styles.miniFeature}>
              <Ionicons name="phone-portrait-outline" size={20} color="#fff" />
              <Text style={styles.miniFeatureText}>Mobile Friendly</Text>
            </View>
            <View style={styles.miniFeature}>
              <Ionicons name="sync-outline" size={20} color="#fff" />
              <Text style={styles.miniFeatureText}>Real-time Sync</Text>
            </View>
            <View style={styles.miniFeature}>
              <Ionicons name="bulb-outline" size={20} color="#fff" />
              <Text style={styles.miniFeatureText}>Stay Organized</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.primaryButton, isLoadingSamples && styles.disabledButton]}
            onPress={onLoadSamples}
            disabled={isLoadingSamples}
          >
            {isLoadingSamples ? (
              <>
                <Ionicons name="sync" size={20} color="#fff" />
                <Text style={styles.buttonText}>Loading Samples...</Text>
              </>
            ) : (
              <>
                <Ionicons name="add-circle-outline" size={20} color="#fff" />
                <Text style={styles.buttonText}>Explore Sample Tasks</Text>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={onStartTour}
          >
            <Ionicons name="play-circle-outline" size={20} color="#3b82f6" />
            <Text style={styles.secondaryButtonText}>Take a Tour</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.skipButton}
            onPress={onSkip}
          >
            <Text style={styles.skipButtonText}>Skip and start with empty calendar</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#3b82f6',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 24,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  featuresContainer: {
    marginBottom: 32,
  },
  featureCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featureText: {
    flex: 1,
    marginLeft: 12,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  previewContainer: {
    marginBottom: 32,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 16,
    textAlign: 'center',
  },
  featuresGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  miniFeature: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 12,
    minWidth: 100,
  },
  miniFeatureText: {
    fontSize: 12,
    color: '#fff',
    marginTop: 4,
    textAlign: 'center',
  },
  buttonContainer: {
    gap: 12,
  },
  primaryButton: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  skipButton: {
    paddingVertical: 12,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3b82f6',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  skipButtonText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  disabledButton: {
    opacity: 0.7,
  },
});

export default OnboardingWelcomeScreen;