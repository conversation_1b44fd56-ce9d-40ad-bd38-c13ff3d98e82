import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { Loader2 } from 'lucide-react'

interface AuthWrapperProps {
  children: React.ReactNode
}

export const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const { user, session, loading } = useAuth()
  const isSignedIn = !!user
  const isLoaded = !loading
  const userId = user?.id
  const [profileSyncing, setProfileSyncing] = useState(false)

  // Auto-sync profile when user signs in (optional - only if profiles table exists)
  useEffect(() => {
    const syncProfile = async () => {
      if (!isLoaded || !isSignedIn || !user) return

      try {
        setProfileSyncing(true)

        // Check if profile already exists
        const { data: existingProfile, error: selectError } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .single()

        // If the profiles table doesn't exist or R<PERSON> is blocking, skip profile sync
        if (selectError && (selectError.code === 'PGRST116' || selectError.code === '42P01' || selectError.message.includes('406'))) {
          console.log('Profiles table not available or access denied, skipping profile sync')
          setProfileSyncing(false)
          return
        }

        // Prepare profile data
        const profileData = {
          id: user.id,
          email: user.email || '',
          full_name: user.user_metadata?.full_name || '',
          avatar_url: user.user_metadata?.avatar_url || '',
          updated_at: new Date().toISOString(),
        }

        // Only set created_at for new profiles
        if (!existingProfile) {
          (profileData as any).created_at = new Date().toISOString()
        }

        const { error } = await supabase
          .from('profiles')
          .upsert(profileData, { 
            onConflict: 'id',
            ignoreDuplicates: false 
          })

        if (error) {
          console.error('Profile sync error:', error)
        }
      } catch (error) {
        console.error('Profile sync failed:', error)
      } finally {
        setProfileSyncing(false)
      }
    }

    syncProfile()
  }, [isLoaded, isSignedIn, user])

  // Show loading state while authentication is loading
  if (!isLoaded || profileSyncing) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">
            {profileSyncing ? 'Syncing profile...' : 'Loading...'}
          </p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
