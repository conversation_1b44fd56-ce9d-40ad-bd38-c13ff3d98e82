const {
  stripe,
  config,
  formatErrorResponse,
  formatSuccessResponse,
} = require('./config');

/**
 * Creates a Stripe Customer Portal Session
 * 
 * Expected body:
 * {
 *   customerId: string,
 *   returnUrl?: string
 * }
 */
module.exports = async (req, res) => {
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).json({ message: 'OK' });
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { customerId, returnUrl } = req.body;

    // Validate required fields
    if (!customerId) {
      return res.status(400).json({
        error: 'Missing required field: customerId'
      });
    }

    // Verify customer exists
    try {
      await stripe.customers.retrieve(customerId);
    } catch (error) {
      return res.status(404).json({
        error: 'Customer not found'
      });
    }

    // Set up return URL
    const baseUrl = config.frontendUrl;
    const finalReturnUrl = returnUrl || `${baseUrl}/subscription/manage`;

    // Create customer portal session
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: finalReturnUrl,
    });

    return res.status(200).json({
      url: session.url,
      sessionId: session.id,
    });

  } catch (error) {
    console.error('Error creating customer portal session:', error);
    
    // Handle specific Stripe errors
    if (error.type === 'StripeInvalidRequestError') {
      return res.status(400).json({
        error: 'Invalid customer ID or portal configuration'
      });
    }
    
    if (error.type === 'StripeAPIError') {
      return res.status(500).json({
        error: 'Customer portal temporarily unavailable. Please try again later.'
      });
    }
    
    return res.status(500).json({
      error: 'An unexpected error occurred. Please try again.'
    });
  }
};
