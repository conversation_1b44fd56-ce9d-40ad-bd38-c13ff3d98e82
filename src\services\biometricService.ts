import { Capacitor } from '@capacitor/core';

// Simple biometric implementation using Web Authentication API
export interface BiometricOptions {
  title?: string;
  subtitle?: string;
  description?: string;
  fallbackButtonTitle?: string;
  negativeButtonTitle?: string;
}

export class BiometricService {
  
  static async isAvailable(): Promise<boolean> {
    if (Capacitor.isNativePlatform()) {
      // For native platforms, we'd use a biometric plugin
      // For now, return true if WebAuthn is supported
      return this.isWebAuthnSupported();
    }
    
    return this.isWebAuthnSupported();
  }

  static async authenticate(options: BiometricOptions = {}): Promise<boolean> {
    try {
      if (!await this.isAvailable()) {
        throw new Error('Biometric authentication not available');
      }

      if (Capacitor.isNativePlatform()) {
        return this.nativeBiometricAuth(options);
      }
      
      return this.webBiometricAuth(options);
    } catch (error) {
      console.error('Biometric authentication error:', error);
      throw error;
    }
  }

  static async enableBiometric(userId: string): Promise<void> {
    try {
      if (!await this.isAvailable()) {
        throw new Error('Biometric authentication not available');
      }

      // Create a credential for the user
      const credential = await navigator.credentials.create({
        publicKey: {
          challenge: new TextEncoder().encode(`biometric-setup-${userId}-${Date.now()}`),
          rp: {
            name: "TaskManager Pro",
            id: window.location.hostname,
          },
          user: {
            id: new TextEncoder().encode(userId),
            name: userId,
            displayName: "TaskManager User",
          },
          pubKeyCredParams: [{
            type: "public-key",
            alg: -7, // ES256
          }],
          authenticatorSelection: {
            authenticatorAttachment: "platform",
            userVerification: "required",
          },
          timeout: 60000,
          attestation: "direct"
        },
      }) as PublicKeyCredential;

      if (credential) {
        // Store credential ID for future authentication
        localStorage.setItem(`biometric-${userId}`, credential.id);
        return;
      }
      
      throw new Error('Failed to create biometric credential');
    } catch (error) {
      console.error('Error enabling biometric:', error);
      throw error;
    }
  }

  static async disableBiometric(userId: string): Promise<void> {
    localStorage.removeItem(`biometric-${userId}`);
  }

  static isBiometricEnabled(userId: string): boolean {
    return localStorage.getItem(`biometric-${userId}`) !== null;
  }

  private static async nativeBiometricAuth(options: BiometricOptions): Promise<boolean> {
    // In a real implementation, you'd use something like:
    // import { BiometricAuth } from '@capacitor-community/biometric-auth';
    // return BiometricAuth.verify(options);
    
    // For now, fallback to web authentication
    return this.webBiometricAuth(options);
  }

  private static async webBiometricAuth(options: BiometricOptions): Promise<boolean> {
    try {
      // Use WebAuthn for biometric authentication
      const credential = await navigator.credentials.get({
        publicKey: {
          challenge: new TextEncoder().encode(`auth-${Date.now()}`),
          timeout: 60000,
          userVerification: "required",
          rpId: window.location.hostname,
        },
      }) as PublicKeyCredential;

      return !!credential;
    } catch (error) {
      console.error('Web biometric authentication failed:', error);
      return false;
    }
  }

  private static isWebAuthnSupported(): boolean {
    return !!(navigator.credentials && navigator.credentials.create && navigator.credentials.get);
  }

  static async getAvailableBiometricTypes(): Promise<string[]> {
    const types: string[] = [];
    
    if (await this.isAvailable()) {
      // For web, we can't determine exact types, so return generic
      types.push('fingerprint', 'face');
    }
    
    return types;
  }

  static async promptBiometricSetup(userId: string): Promise<boolean> {
    if (this.isBiometricEnabled(userId)) {
      return true;
    }

    const userWants = window.confirm(
      'Would you like to enable biometric authentication for faster and more secure access?'
    );

    if (userWants) {
      try {
        await this.enableBiometric(userId);
        return true;
      } catch (error) {
        console.error('Failed to setup biometric:', error);
        alert('Failed to setup biometric authentication. You can try again later in settings.');
        return false;
      }
    }

    return false;
  }
}