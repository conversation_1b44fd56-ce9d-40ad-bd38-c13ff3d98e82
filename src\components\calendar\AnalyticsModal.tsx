
import React, { useState, useMemo } from 'react';
import { format, isWithinInterval } from 'date-fns';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Task } from '@/types/task';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-mobile';
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  CalendarIcon,
  Clock,
  AlertCircle,
  CheckCircle,
  Pause,
  X,
  Eye,
  ChevronDown,
  Circle,
  ExternalLink,
  Paperclip,
  Edit,
  Trash2,
  Download,
  FileText,
  BarChart3,
  Group,
  TrendingUp
} from 'lucide-react';

interface AnalyticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  cardType: string;
  data: Task[];
  title: string;
  onUpdateTaskStatus?: (taskId: string, status: string) => void;
  onUpdateTaskPriority?: (taskId: string, priority: string) => void;
  onUpdateTaskCategory?: (taskId: string, category: string) => void;
  onTaskClick?: (task: Task) => void;
}

function AnalyticsModal(props: AnalyticsModalProps): JSX.Element {
  const {
    isOpen,
    onClose,
    cardType,
    data: initialData,
    title,
    onUpdateTaskStatus,
    onUpdateTaskPriority,
    onUpdateTaskCategory,
    onTaskClick
  } = props;
  const { isMobile, isTablet } = useScreenSize();
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [isStartDatePickerOpen, setIsStartDatePickerOpen] = useState(false);
  const [isEndDatePickerOpen, setIsEndDatePickerOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'dueDate' | 'priority' | 'status' | 'title' | 'category'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');

  const [filterCategory, setFilterCategory] = useState('all');
  const [filterTag, setFilterTag] = useState('all');
  const [filterRecurring, setFilterRecurring] = useState('all');
  const [groupBy, setGroupBy] = useState<'none' | 'status' | 'priority' | 'category' | 'month' | 'week'>('none');
  const [showSummary, setShowSummary] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);

  // Get modal configuration based on card type
  const getModalConfig = () => {
    switch (cardType) {
      case 'total':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: true,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-primary',
          headerBg: 'from-primary-50/50 to-primary-100/50 dark:from-primary-950/20 dark:to-primary-900/20'
        }


      case 'high-priority':
        return {
          showStatusFilter: true,
          showPriorityFilter: false,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'high',
          headerColor: 'text-red-600',
          headerBg: 'from-red-50/50 to-rose-50/50 dark:from-red-950/20 dark:to-rose-950/20'
        };
      case 'medium-priority':
        return {
          showStatusFilter: true,
          showPriorityFilter: false,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'medium',
          headerColor: 'text-yellow-600',
          headerBg: 'from-yellow-50/50 to-amber-50/50 dark:from-yellow-950/20 dark:to-amber-950/20'
        };
      case 'low-priority':
        return {
          showStatusFilter: true,
          showPriorityFilter: false,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'low',
          headerColor: 'text-green-600',
          headerBg: 'from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20'
        };
      case 'all-priorities':
      case 'merged-priority':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: true,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-slate-600',
          headerBg: 'from-slate-50/50 to-slate-100/50 dark:from-slate-950/20 dark:to-slate-900/20'
        };
      case 'upcoming':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-purple-600',
          headerBg: 'from-purple-50/50 to-violet-50/50 dark:from-purple-950/20 dark:to-violet-950/20'
        };
      case 'categories':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: true,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-purple-600',
          headerBg: 'from-purple-50/50 to-violet-50/50 dark:from-purple-950/20 dark:to-violet-950/20'
        };
      case 'overdue-tasks':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-red-600',
          headerBg: 'from-red-50/50 to-rose-50/50 dark:from-red-950/20 dark:to-rose-950/20'
        };
      case 'due-tasks':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-blue-600',
          headerBg: 'from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20'
        };
      case 'priority-unified':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: true,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-slate-600',
          headerBg: 'from-slate-50/50 to-slate-100/50 dark:from-slate-950/20 dark:to-slate-900/20'
        };
      case 'assigned-tasks':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-indigo-600',
          headerBg: 'from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/20 dark:to-purple-950/20'
        };
      case 'tagged-tasks':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-orange-600',
          headerBg: 'from-orange-50/50 to-amber-50/50 dark:from-orange-950/20 dark:to-amber-950/20'
        };
      case 'recurring-tasks':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-purple-600',
          headerBg: 'from-purple-50/50 to-violet-50/50 dark:from-purple-950/20 dark:to-violet-950/20'
        };
      case 'status-unified':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: true,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-indigo-600',
          headerBg: 'from-indigo-50/50 to-blue-50/50 dark:from-indigo-950/20 dark:to-blue-950/20'
        };
      case 'total-tasks':
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: true,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-blue-600',
          headerBg: 'from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20'
        };
      case 'completed-tasks':
        return {
          showStatusFilter: false,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'completed',
          defaultPriority: 'all',
          headerColor: 'text-green-600',
          headerBg: 'from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20'
        };
      case 'pending-tasks':
        return {
          showStatusFilter: false,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'pending',
          defaultPriority: 'all',
          headerColor: 'text-orange-600',
          headerBg: 'from-orange-50/50 to-yellow-50/50 dark:from-orange-950/20 dark:to-yellow-950/20'
        };
      case 'on-hold-tasks':
        return {
          showStatusFilter: false,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: false,
          defaultStatus: 'on-hold',
          defaultPriority: 'all',
          headerColor: 'text-purple-600',
          headerBg: 'from-purple-50/50 to-violet-50/50 dark:from-purple-950/20 dark:to-violet-950/20'
        };
      default:
        return {
          showStatusFilter: true,
          showPriorityFilter: true,
          showDateFilter: true,
          showFullFilters: true,
          defaultStatus: 'all',
          defaultPriority: 'all',
          headerColor: 'text-blue-600',
          headerBg: 'from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20'
        };
    }
  };

  const initialModalConfig = getModalConfig();

  // Consolidated filtering and sorting logic
  const filteredAndSortedTasks = useMemo(() => {
    if (!Array.isArray(initialData)) return [];
    
    let filtered = initialData;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Base filtering based on cardType
    switch (cardType) {
      case 'pending':
        filtered = filtered.filter(task => (task.status || 'pending') === 'pending');
        break;

      case 'completed':
        filtered = filtered.filter(task => (task.status || 'pending') === 'completed');
        break;
      case 'high-priority':
        filtered = filtered.filter(task => (task.priority || 'low') === 'high');
        break;
      case 'medium-priority':
        filtered = filtered.filter(task => (task.priority || 'low') === 'medium');
        break;
      case 'low-priority':
        filtered = filtered.filter(task => (task.priority || 'low') === 'low');
        break;
      case 'overdue':
        filtered = filtered.filter(task => {
          if (!task.dueDate) return false;
          const dueDate = new Date(task.dueDate);
          dueDate.setHours(0, 0, 0, 0);
          return dueDate < today && (task.status || 'pending') !== 'completed' && (task.status || 'pending') !== 'cancelled';
        });
        break;
      case 'upcoming':
        filtered = filtered.filter(task => {
          if (!task.dueDate) return false;
          const dueDate = new Date(task.dueDate);
          dueDate.setHours(0, 0, 0, 0);
          return dueDate >= today && (task.status || 'pending') !== 'completed' && (task.status || 'pending') !== 'cancelled';
        });
        break;
      case 'recurring-tasks':
        filtered = filtered.filter(task => {
          return task.recurringPattern !== undefined && task.recurringPattern !== null;
        });
        break;
      default:
        // 'total' or any other type shows all tasks
        break;
    }

    // Additional filtering
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(task => {
        const matchesTitle = task.title.toLowerCase().includes(searchLower);
        const matchesDescription = (task.description || '').toLowerCase().includes(searchLower);
        const matchesCategory = (task as any).category?.toLowerCase().includes(searchLower);
        const matchesTags = (task as any).tags?.some((tag: string) =>
          tag.toLowerCase().includes(searchLower)
        );
        return matchesTitle || matchesDescription || matchesCategory || matchesTags;
      });
    }

    // Status filter (only if not already filtered by cardType)
    if (filterStatus !== 'all' && !['pending', 'completed'].includes(cardType)) {
      filtered = filtered.filter(task => (task.status || 'pending') === filterStatus);
    }

    // Priority filter (only if not already filtered by cardType)
    if (filterPriority !== 'all' && !['high-priority', 'medium-priority', 'low-priority'].includes(cardType)) {
      filtered = filtered.filter(task => task.priority === filterPriority);
    }

    // Category filter
    if (filterCategory !== 'all') {
      filtered = filtered.filter(task => (task as any).category === filterCategory);
    }

    // Tag filter (only for tagged-tasks card type)
    if (cardType === 'tagged-tasks' && filterTag !== 'all') {
      filtered = filtered.filter(task => {
        const tags = (task as any).tags;
        return tags && Array.isArray(tags) && tags.includes(filterTag);
      });
    }

    // Recurring filter (only for recurring-tasks card type)
    if (cardType === 'recurring-tasks' && filterRecurring !== 'all') {
      filtered = filtered.filter(task => {
        return task.recurringPattern === filterRecurring;
      });
    }



    // Date range filtering
    if (startDate || endDate) {
      filtered = filtered.filter(task => {
        const taskDate = new Date(task.date);
        if (startDate && taskDate < startDate) return false;
        if (endDate && taskDate > endDate) return false;
        return true;
      });
    }

    // Sorting
    return filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'dueDate':
        case 'date':
          const dateA = new Date(sortBy === 'dueDate' ? a.dueDate || a.date : a.date);
          const dateB = new Date(sortBy === 'dueDate' ? b.dueDate || b.date : b.date);
          comparison = dateA.getTime() - dateB.getTime();
          break;
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          comparison = (priorityOrder[a.priority || 'low'] || 0) - (priorityOrder[b.priority || 'low'] || 0);
          break;
        case 'status':
          const statusOrder = { 'pending': 2, 'completed': 1, 'cancelled': 0 };
          comparison = (statusOrder[a.status || 'pending'] || 0) - (statusOrder[b.status || 'pending'] || 0);
          break;
        case 'title':
          comparison = (a.title || '').localeCompare(b.title || '');
          break;
        case 'category':
          comparison = ((a as any).category || '').localeCompare((b as any).category || '');
          break;
        default:
          comparison = 0;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }, [initialData, cardType, searchTerm, filterStatus, filterPriority, filterCategory, filterTag, filterRecurring, startDate, endDate, sortBy, sortOrder]);

  // Group tasks if grouping is enabled
  const groupedTasksByDate = useMemo(() => {
    if (groupBy === 'none') return { ungrouped: filteredAndSortedTasks };
    
    const groups = {};
    filteredAndSortedTasks.forEach(task => {
      const key = task[groupBy] || 'Other';
      if (!groups[key]) groups[key] = [];
      groups[key].push(task);
    });
    return groups;
  }, [filteredAndSortedTasks, groupBy]);

  // Color schemes for task badges
  const priorityColors = {
    high: 'bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700',
    medium: 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700',
    low: 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700',
  };

  const statusColors = {
    pending: 'bg-gray-100 text-gray-700 border-gray-300 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700',

    completed: 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700',
    cancelled: 'bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700',
    'on-hold': 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700',
  };

  // Status icon helper
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;

      case 'cancelled':
        return <AlertCircle className="h-4 w-4" />;
      case 'on-hold':
        return <Pause className="h-4 w-4" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  // Calculate task statistics
  const taskStats = useMemo(() => {
    const stats = {
      total: initialData.length,
      completed: initialData.filter(t => t.status === 'completed').length,

      pending: initialData.filter(t => (t.status || 'pending') === 'pending').length,
      highPriority: initialData.filter(t => t.priority === 'high').length,
    };
    return stats;
  }, [initialData]);

  // Group tasks if grouping is enabled
  const groupedTasks = useMemo(() => {
    if (groupBy === 'none') return { ungrouped: filteredAndSortedTasks };
    
    return filteredAndSortedTasks.reduce((groups, task) => {
      let key;
      switch (groupBy) {
        case 'status':
          key = task.status || 'pending';
          break;
        case 'priority':
          key = task.priority || 'low';
          break;
        case 'month':
          key = format(new Date(task.date), 'MMMM yyyy');
          break;
        case 'week':
          const weekStart = new Date(task.date);
          weekStart.setDate(weekStart.getDate() - weekStart.getDay());
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          key = `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;
          break;
        default:
          key = task[groupBy] || 'Other';
      }
      if (!groups[key]) groups[key] = [];
      groups[key].push(task);
      return groups;
    }, {});
  }, [filteredAndSortedTasks, groupBy]);



  // Get unique categories from tasks
  const uniqueCategories = useMemo(() => {
    if (!Array.isArray(initialData)) return [];
    
    const categories = new Set<string>();
    initialData.forEach(task => {
      const category = (task as any).category;
      if (category && typeof category === 'string') {
        categories.add(category);
      }
    });
    
    // Add 'General' as a default category if it doesn't exist
    categories.add('General');
    
    return Array.from(categories).sort();
  }, [initialData]);

  // Get unique tags from tasks
  const uniqueTags = useMemo(() => {
    if (!Array.isArray(initialData)) return [];
    
    const tags = new Set<string>();
    initialData.forEach(task => {
      const taskTags = (task as any).tags;
      if (taskTags && Array.isArray(taskTags)) {
        taskTags.forEach(tag => {
          if (tag && typeof tag === 'string') {
            tags.add(tag);
          }
        });
      }
    });
    
    return Array.from(tags).sort();
  }, [initialData]);

  // Clear date filters
  const clearDateFilters = () => {
    setStartDate(null);
    setEndDate(null);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setFilterStatus('all');
    setFilterPriority('all');

    setFilterCategory('all');
    setFilterTag('all');
    setFilterRecurring('all');
    setStartDate(null);
    setEndDate(null);
  };

  // Export functionality
  const exportToCSV = () => {
    const headers = ['#', 'Title', 'Description', 'Date', 'Due Date', 'Priority', 'Status', 'Category', 'Tags'];
    const csvContent = [
      headers.join(','),
      ...filteredAndSortedTasks.map((task, index) => [
        index + 1,
        `"${task.title.replace(/"/g, '""')}"`,
        `"${(task.description || '').replace(/"/g, '""')}"`,
        task.date,
        task.dueDate || '',
        task.priority,
        task.status || 'pending',
        (task as any).category || 'General',
        `"${((task as any).tags || []).join(', ')}"`,
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${title.replace(/\s+/g, '_')}_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToPDF = () => {
    const printContent = `
      <html>
        <head>
          <title>${title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f8f9fa; font-weight: bold; }
            tr:nth-child(even) { background-color: #f8f9fa; }
            .priority-high { color: #dc3545; font-weight: bold; }
            .priority-medium { color: #ffc107; font-weight: bold; }
            .priority-low { color: #28a745; font-weight: bold; }
            .status-completed { color: #28a745; }
            .status-pending { color: #6c757d; }
    
          </style>
        </head>
        <body>
          <h1>${title}</h1>
          <p>Generated on: ${format(new Date(), 'MMMM d, yyyy')}</p>
          <p>Total Tasks: ${filteredAndSortedTasks.length}</p>
          <table>
            <thead>
              <tr>
                <th>#</th>
                <th>Title</th>
                <th>Description</th>
                <th>Date</th>
                <th>Due Date</th>
                <th>Priority</th>
                <th>Status</th>
                <th>Category</th>
              </tr>
            </thead>
            <tbody>
              ${filteredAndSortedTasks.map((task, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${task.title}</td>
                  <td>${task.description || '-'}</td>
                  <td>${format(new Date(task.date), 'MMM d, yyyy')}</td>
                  <td>${task.dueDate ? format(new Date(task.dueDate), 'MMM d, yyyy') : '-'}</td>
                  <td class="priority-${task.priority}">${task.priority}</td>
                  <td class="status-${task.status || 'pending'}">${task.status || 'pending'}</td>
                  <td>${(task as any).category || 'General'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };

  // Summary statistics
  const summaryStats = useMemo(() => {
    const stats = {
      total: filteredAndSortedTasks.length,
      pending: 0,
      inProgress: 0,
      completed: 0,
      onHold: 0,
      cancelled: 0,
      overdue: 0,
      highPriority: 0,
      mediumPriority: 0,
      lowPriority: 0,
    };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    filteredAndSortedTasks.forEach(task => {
      // Status counts - align with AnalyticsDashboard logic
      const status = task.status || 'pending';
      if (status !== 'completed' && status !== 'cancelled') stats.pending;
      else if (status === 'completed') stats.completed++;
      else if (status === 'cancelled') stats.cancelled++;
      else if (status === 'on-hold') stats.onHold++;

      // Priority counts
      if (task.priority === 'high') stats.highPriority++;
      else if (task.priority === 'medium') stats.mediumPriority++;
      else if (task.priority === 'low') stats.lowPriority++;

      // Overdue count
      if (task.dueDate && status !== 'completed' && status !== 'cancelled') {
        const dueDate = new Date(task.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        if (dueDate < today) stats.overdue++;
      }
    });

    return stats;
  }, [filteredAndSortedTasks]);

  // Grouping functionality
  const groupedTasksByType = useMemo(() => {       
    if (groupBy === 'none') {
      return { 'All Tasks': filteredAndSortedTasks };
    }

    const groups: { [key: string]: Task[] } = {};

    filteredAndSortedTasks.forEach(task => {
      const taskDate = new Date(task.date);
      let groupKey: string;

      if (groupBy === 'month') {
        groupKey = format(taskDate, 'MMMM yyyy');
      } else if (groupBy === 'week') {
        const weekStart = new Date(taskDate);
        weekStart.setDate(taskDate.getDate() - taskDate.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        groupKey = `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;
      } else {
        groupKey = 'All Tasks';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(task);
    });

    // Sort groups by date
    const sortedGroups: { [key: string]: Task[] } = {};
    Object.keys(groups)
      .sort((a, b) => {
        if (a === 'All Tasks') return -1;
        if (b === 'All Tasks') return 1;

        // For month/week sorting, compare the first task date in each group
        const aFirstTask = groups[a][0];
        const bFirstTask = groups[b][0];
        return new Date(aFirstTask.date).getTime() - new Date(bFirstTask.date).getTime();
      })
      .forEach(key => {
        sortedGroups[key] = groups[key];
      });

    return sortedGroups;
  }, [filteredAndSortedTasks, groupBy]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          "max-w-5xl max-h-[96vh] overflow-y-auto overflow-x-hidden flex flex-col p-3",
          isMobile && "max-w-[98vw] max-h-[98vh] p-2",
          isTablet && "max-w-[95vw] max-h-[96vh] p-3"
        )}
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className={cn(
          "pb-2 pr-8 rounded-t-lg border-b-2",
          "bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",
          "border-gray-200 dark:border-gray-700"
        )}>
          <div className="flex items-center justify-between gap-2">
            <DialogTitle className={cn(
              "flex flex-col gap-1 text-base font-semibold flex-1 min-w-0 px-2 py-2",
              "rounded-md",
              initialModalConfig.headerBg,
              "border border-gray-200/50 dark:border-gray-700/50"
            )}>
              <div className="flex items-center gap-2">
                <Eye className={cn("h-4 w-4", initialModalConfig.headerColor)} />
                <span className={cn("font-bold", initialModalConfig.headerColor)}>
                  {cardType === 'total' && 'All Tasks'}
                  {cardType === 'pending' && 'Pending Tasks'}
                  {cardType === 'completed' && 'Completed Tasks'}
                  {cardType === 'overdue' && 'Overdue Tasks'}
                  {cardType === 'overdue-tasks' && 'Overdue Tasks'}
                  {cardType === 'due-tasks' && 'Not Yet Due'}
                  {cardType === 'upcoming' && 'Upcoming Tasks'}
                  {cardType === 'high-priority' && 'High Priority Tasks'}
                  {cardType === 'medium-priority' && 'Medium Priority Tasks'}
                  {cardType === 'low-priority' && 'Low Priority Tasks'}
                  {cardType === 'priority-unified' && 'Priority Tasks'}
                  {cardType === 'categories' && 'Tasks by Category'}
                  {cardType === 'assigned-tasks' && 'Assigned Tasks'}
                  {cardType === 'tagged-tasks' && 'Tagged Tasks'}
                  {cardType === 'recurring-tasks' && 'Recurring Tasks'}
                  {cardType === 'unassigned-tasks' && 'Unassigned Tasks'}
                  {cardType === 'team-members' && 'Team Tasks'}
                  {cardType === 'status-unified' && 'Task Status Overview'}
                  {cardType === 'total-tasks' && 'Total Tasks'}
                  {cardType === 'completed-tasks' && 'Completed Tasks'}
                  {cardType === 'pending-tasks' && 'Pending Tasks'}
                  {cardType === 'on-hold-tasks' && 'On Hold Tasks'}
                  {!['total', 'pending', 'completed', 'overdue', 'overdue-tasks', 'due-tasks', 'upcoming', 'high-priority', 'medium-priority', 'low-priority', 'priority-unified', 'categories', 'assigned-tasks', 'tagged-tasks', 'recurring-tasks', 'unassigned-tasks', 'team-members', 'status-unified', 'total-tasks', 'completed-tasks', 'pending-tasks', 'on-hold-tasks'].includes(cardType) && 'Task List'}
                </span>
                <Badge variant="outline" className={cn("ml-2 text-xs h-5", initialModalConfig.headerColor, "border-current/30")}>
                  {filteredAndSortedTasks.length}
                </Badge>
              </div>
              {/* Date Reference */}
               <div className="flex items-center gap-1 text-xs opacity-75">
                 <CalendarIcon className="h-3 w-3" />
                 <span>
                   {title.includes('(As on Today)') && 'As on Today'}
                   {title.includes('(As on') && !title.includes('(As on Today)') && title.match(/\(([^)]+)\)/)?.[1]}
                   {!title.includes('(') && 'All Time'}
                 </span>
               </div>
            </DialogTitle>
            <DialogDescription className="sr-only">
              View and analyze task details with filtering and export options
            </DialogDescription>

            {/* Export and Summary Controls - Compact */}
            <div className="flex items-center gap-0.5 flex-shrink-0">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSummary(!showSummary)}
                className="text-muted-foreground hover:text-foreground h-5 w-5 p-0"
                title="Toggle summary statistics"
              >
                <BarChart3 className="h-3 w-3" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={exportToCSV}
                className="text-muted-foreground hover:text-foreground h-5 w-5 p-0"
                title="Export as CSV"
              >
                <Download className="h-3 w-3" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={exportToPDF}
                className="text-muted-foreground hover:text-foreground h-5 w-5 p-0"
                title="Export as PDF"
              >
                <FileText className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Filters Section - Compact with 1.5pt spacing */}
        <div className={cn(
          "space-y-2 py-1.5 border-b bg-gradient-to-r rounded-md p-1.5",
          initialModalConfig.headerBg
        )}>
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="Search tasks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8 h-7 text-sm bg-white dark:bg-gray-900"
          />
        </div>

        {/* Filter Controls - Compact */}
        <div className={cn(
          "grid gap-2",
          cardType === 'tagged-tasks' || cardType === 'recurring-tasks'
            ? "grid-cols-2 md:grid-cols-7"
            : initialModalConfig.showFullFilters
              ? "grid-cols-2 md:grid-cols-6"
              : "grid-cols-2 md:grid-cols-6"
        )}>
            {/* Status Filter - Only show for relevant card types */}
            {initialModalConfig.showStatusFilter && (
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="h-7 text-sm py-1">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                   
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="on-hold">On Hold</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* Priority Filter - Only show for relevant card types */}
            {initialModalConfig.showPriorityFilter && (
              <Select value={filterPriority} onValueChange={setFilterPriority}>
                <SelectTrigger className="h-7 text-sm py-1">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="high">High Priority</SelectItem>
                  <SelectItem value="medium">Medium Priority</SelectItem>
                  <SelectItem value="low">Low Priority</SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* Category Filter - Show for all card types */}
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="h-7 text-sm py-1">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {uniqueCategories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Tag Filter - Only show for tagged-tasks card type */}
            {cardType === 'tagged-tasks' && (
              <Select value={filterTag} onValueChange={setFilterTag}>
                <SelectTrigger className="h-7 text-sm py-1">
                  <SelectValue placeholder="Tags" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tags</SelectItem>
                  {uniqueTags.map(tag => (
                    <SelectItem key={tag} value={tag}>
                      {tag}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            {/* Recurring Filter - Only show for recurring-tasks card type */}
            {cardType === 'recurring-tasks' && (
              <Select value={filterRecurring || 'all'} onValueChange={(value) => setFilterRecurring(value)}>
                <SelectTrigger className="h-7 text-sm py-1">
                  <SelectValue placeholder="Recurring" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Recurring</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* Sort Controls - Always show */}
            <Select value={sortBy} onValueChange={(value: 'date' | 'priority' | 'title') => setSortBy(value)}>
              <SelectTrigger className="h-7 text-sm py-1">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Sort by Date</SelectItem>
                <SelectItem value="priority">Sort by Priority</SelectItem>
                <SelectItem value="title">Sort by Title</SelectItem>
              </SelectContent>
            </Select>

            {/* Grouping - Show for all card types except assigned-tasks, tagged-tasks, recurring-tasks, overdue-tasks, and due-tasks */}
            {!['assigned-tasks', 'tagged-tasks', 'recurring-tasks', 'overdue-tasks', 'due-tasks'].includes(cardType) && (
              <Select value={groupBy} onValueChange={(value: 'none' | 'month' | 'week') => setGroupBy(value)}>
                <SelectTrigger className="h-7 text-sm py-1">
                  <SelectValue placeholder="Group by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Grouping</SelectItem>
                  <SelectItem value="month">Group by Month</SelectItem>
                  <SelectItem value="week">Group by Week</SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* Sort Order - Show in the same row as other filters */}
            <div className="flex gap-1">
              {/* Show sort order button for all card types except assigned-tasks, tagged-tasks, recurring-tasks, overdue-tasks, and due-tasks */}
              {!['assigned-tasks', 'tagged-tasks', 'recurring-tasks', 'overdue-tasks', 'due-tasks'].includes(cardType) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="flex items-center gap-1 flex-1 h-7 text-xs py-1"
                >
                  {sortOrder === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />}
                  {sortOrder === 'asc' ? 'Asc' : 'Desc'}
                </Button>
              )}
              

              
              {/* Clear filter button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-muted-foreground hover:text-foreground h-7 w-7 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Date Range Filters - Hidden since data is pre-filtered */}
          {/* The data is already filtered for cumulative date ranges by AnalyticsPanel */}
          {/* Date range filters would conflict with the pre-filtered boundaries */}

          {/* Categories view info */}
          {cardType === 'categories' && (
            <div className="text-xs text-muted-foreground bg-white/50 dark:bg-gray-900/50 p-1.5 rounded-md border">
              <p className="font-medium mb-0.5">
                🎯 Showing all tasks grouped by categories
              </p>
              <p className="text-xs">
                Use filters to narrow down results by status, priority, or date range.
              </p>
            </div>
          )}
        </div>

        {/* Summary Statistics */}
        {showSummary && (
          <div className="border-b bg-gradient-to-r from-gray-50/50 to-slate-50/50 dark:from-gray-950/20 dark:to-slate-950/20 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Summary Statistics</span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
              {/* Status Distribution */}
              <div className="text-center p-2 bg-white dark:bg-gray-900 rounded-lg border">
                <div className="text-lg font-bold text-gray-600">{summaryStats.pending}</div>
                <div className="text-xs text-muted-foreground">Pending</div>
              </div>

              <div className="text-center p-2 bg-white dark:bg-gray-900 rounded-lg border">
                <div className="text-lg font-bold text-blue-600">{summaryStats.inProgress}</div>
                
              </div>

              <div className="text-center p-2 bg-white dark:bg-gray-900 rounded-lg border">
                <div className="text-lg font-bold text-green-600">{summaryStats.completed}</div>
                <div className="text-xs text-muted-foreground">Completed</div>
              </div>

              <div className="text-center p-2 bg-white dark:bg-gray-900 rounded-lg border">
                <div className="text-lg font-bold text-red-600">{summaryStats.overdue}</div>
                <div className="text-xs text-muted-foreground">Overdue</div>
              </div>

              {/* Priority Distribution */}
              <div className="text-center p-2 bg-white dark:bg-gray-900 rounded-lg border">
                <div className="text-lg font-bold text-orange-600">{summaryStats.highPriority}</div>
                <div className="text-xs text-muted-foreground">High Priority</div>
              </div>

              <div className="text-center p-2 bg-white dark:bg-gray-900 rounded-lg border">
                <div className="text-lg font-bold text-emerald-600">{summaryStats.total}</div>
                <div className="text-xs text-muted-foreground">Total Tasks</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Progress</span>
                <span>{summaryStats.total > 0 ? Math.round((summaryStats.completed / summaryStats.total) * 100) : 0}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full transition-all duration-500"
                  style={{
                    width: summaryStats.total > 0 ? `${(summaryStats.completed / summaryStats.total) * 100}%` : '0%'
                  }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Task List with Grouping */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden">
          {filteredAndSortedTasks.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No tasks found</h3>
              <p className="text-muted-foreground">
                {searchTerm || filterStatus !== 'all' || filterPriority !== 'all' || startDate || endDate
                  ? 'Try adjusting your search or filters'
                  : 'No tasks available'}
              </p>
            </div>
          ) : (
            <div className="space-y-4 p-2">
              {Object.entries(groupedTasksByType).map(([groupName, groupTasks]) => (
                <div key={groupName}>
                  {/* Group Header */}
                  {groupBy !== 'none' && (
                    <div className="flex items-center gap-2 mb-3 pb-2 border-b border-muted/30">
                      <Group className="h-4 w-4 text-muted-foreground" />
                      <h3 className="font-semibold text-sm text-muted-foreground">{groupName}</h3>
                      <Badge variant="outline" className="text-xs">
                        {groupTasks.length} task{groupTasks.length !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                  )}

                  {/* Tasks in Group */}
                  <div className="space-y-3">
                    {groupTasks.map((task, taskIndex) => {
                      const globalIndex = filteredAndSortedTasks.findIndex(t => t.id === task.id);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const taskDate = new Date(task.date);
                taskDate.setHours(0, 0, 0, 0);
                const isPastTask = taskDate < today;
                const isSelected = selectedTaskId === task.id;

                return (
                  <Card
                    key={task.id}
                    className={cn(
                      "cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-[1.01] border-l-4 group",
                      isPastTask ? "border-l-amber-400 bg-amber-50/30 dark:bg-amber-900/10" : "border-l-primary",
                      task.priority === 'high' && "border-l-red-500",
                      task.priority === 'medium' && "border-l-yellow-500",
                      task.priority === 'low' && "border-l-green-500",
                      "hover:border-2 hover:border-blue-200 hover:bg-blue-50/30 dark:hover:bg-blue-900/20 dark:hover:border-blue-600",
                      isSelected && "border-2 border-blue-300 bg-blue-100/60 dark:bg-blue-800/30 dark:border-blue-500 shadow-md"
                    )}
                    onClick={() => {
                      setSelectedTaskId(task.id);
                      onTaskClick?.(task);
                    }}
                  >
                    <CardContent className="p-2">
                      <div className="space-y-0.5">
                        {/* Title Row with Date and Badges */}
                        <div className="flex items-center justify-between gap-2">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <div className="flex-shrink-0 w-6 h-6 bg-primary/10 text-primary rounded-full flex items-center justify-center text-xs font-medium">
                              {globalIndex + 1}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <h4 className="font-semibold text-sm truncate group-hover:text-primary transition-colors">
                                  {task.title}
                                </h4>
                                <span className="text-xs text-muted-foreground flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {format(new Date(task.date), 'MMM d, yyyy')}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Badges Row - Right Side */}
                          <div className="flex items-center gap-1 flex-shrink-0 overflow-hidden">
                            {/* Status Badge - Static */}
                             <Badge
                               variant="outline"
                               className={cn(
                                 "h-5 text-xs",
                                 statusColors[task.status || 'pending']
                               )}
                             >
                               <span className="capitalize truncate">{task.status || 'pending'}</span>
                             </Badge>
 
                             {/* Priority Badge - Static */}
                             <Badge
                               variant="outline"
                               className={cn(
                                 "h-5 text-xs",
                                 priorityColors[task.priority]
                               )}
                             >
                               <span className="capitalize">{task.priority}</span>
                             </Badge>
 
                             {/* Category Badge - Static */}
                             <Badge
                               variant="outline"
                               className="h-5 text-xs bg-orange-100 text-orange-700 border-orange-300 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700"
                             >
                               <span className="capitalize truncate">{(task as any).category || 'General'}</span>
                             </Badge>

                             {/* Tags Badge - Only show for tagged-tasks card type */}
                             {cardType === 'tagged-tasks' && (task as any).tags && (task as any).tags.length > 0 && (
                               <Badge
                                 variant="outline"
                                 className="h-5 text-xs bg-blue-100 text-blue-700 border-blue-300 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700"
                               >
                                 <span className="truncate">{(task as any).tags[0]}{(task as any).tags.length > 1 ? ` +${(task as any).tags.length - 1}` : ''}</span>
                               </Badge>
                             )}

                             {/* Recurring Badge - Only show for recurring-tasks card type */}
                             {cardType === 'recurring-tasks' && task.recurringPattern && (
                               <Badge
                                 variant="outline"
                                 className="h-5 text-xs bg-purple-100 text-purple-700 border-purple-300 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700"
                               >
                                 <span className="truncate">{task.recurringPattern}</span>
                               </Badge>
                             )}


                          </div>
                        </div>

                        {/* Description */}
                        {task.description && (
                          <p className="text-xs text-muted-foreground leading-tight pl-8 mt-0.5">
                            {task.description}
                          </p>
                        )}




                      </div>
                    </CardContent>
                  </Card>
                );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export { AnalyticsModal };
