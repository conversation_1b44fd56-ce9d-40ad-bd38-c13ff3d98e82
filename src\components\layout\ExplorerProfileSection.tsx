import React from 'react';
import { User, LogOut, Settings, Shield, Mail, Calendar } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface ExplorerProfileSectionProps {
  className?: string;
}

export const ExplorerProfileSection: React.FC<ExplorerProfileSectionProps> = ({
  className
}) => {
  const { user, signOut } = useAuth();
  const { toast } = useToast();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: "Signed Out",
        description: "You have been successfully signed out"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sign out. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleManageProfile = () => {
    toast({
      title: "Profile Management",
      description: "Profile management will be available soon"
    });
  };

  const handlePrivacySettings = () => {
    toast({
      title: "Privacy Settings",
      description: "Privacy settings will be available soon"
    });
  };

  const getUserInitials = (email: string) => {
    return email
      .split('@')[0]
      .split('.')
      .map(part => part.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  };

  const formatJoinDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short'
      });
    } catch {
      return 'Recently';
    }
  };

  if (!user) {
    return (
      <div className={cn("space-y-3", className)}>
        <Card className="bg-card/50 backdrop-blur-sm border-l-4 border-l-green-500">
          <CardContent className="p-0">
            <div className="bg-green-50 dark:bg-green-900/20 px-2 py-2 rounded-t-md">
              <div className="text-xs font-medium flex items-center gap-2">
                <div className="p-1 rounded-md bg-green-500/20">
                  <User className="h-3 w-3 text-green-500" />
                </div>
                Sign In Required
              </div>
            </div>
            <div className="p-3">
              <div className="text-center space-y-2">
                <User className="h-8 w-8 mx-auto text-muted-foreground" />
                <p className="text-xs text-muted-foreground">
                  Please sign in to access profile features
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const profileCards = [
    {
      id: 'user-info',
      title: 'Profile',
      icon: User,
      content: (
        <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user.user_metadata?.avatar_url} />
                <AvatarFallback className="text-xs">
                  {getUserInitials(user.email || 'U')}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium truncate">
                  {user.user_metadata?.full_name || user.email?.split('@')[0] || 'User'}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.email}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">
                Joined {formatJoinDate(user.created_at)}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {user.user_metadata?.subscription_tier || 'Free'}
              </Badge>
              {user.email_confirmed_at && (
                <Badge variant="outline" className="text-xs">
                  <Mail className="h-2 w-2 mr-1" />
                  Verified
                </Badge>
              )}
            </div>
          </div>
      )
    },
    {
      id: 'account-actions',
      title: 'Account',
      icon: Settings,
      content: (
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleManageProfile}
            className="w-full h-7 text-xs justify-start"
          >
            <User className="h-3 w-3 mr-2" />
            Manage Profile
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrivacySettings}
            className="w-full h-7 text-xs justify-start"
          >
            <Shield className="h-3 w-3 mr-2" />
            Privacy Settings
          </Button>
          
          <Button
            variant="destructive"
            size="sm"
            onClick={handleSignOut}
            className="w-full h-7 text-xs justify-start"
          >
            <LogOut className="h-3 w-3 mr-2" />
            Sign Out
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className={cn("space-y-3", className)}>
      {profileCards.map((card) => {
        const Icon = card.icon;
        return (
          <Card key={card.id} className="bg-card/50 backdrop-blur-sm border-l-4 border-l-green-500">
            <CardContent className="p-0">
              <div className="bg-green-50 dark:bg-green-900/20 px-2 py-2 rounded-t-md">
                <div className="text-xs font-medium flex items-center gap-2">
                  <div className="p-1 rounded-md bg-green-500/20">
                    <Icon className="h-3 w-3 text-green-500" />
                  </div>
                  {card.title}
                </div>
              </div>
              <div className="p-2">
                {card.content}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};