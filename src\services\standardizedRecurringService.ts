import { format, addDays, addWeeks, addMonths, addYears, isBefore, isAfter } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { Task, TaskInsert } from '@/types/task';

export interface StandardizedRecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  frequency: number; // Every X days/weeks/months/years
  daysOfWeek?: number[]; // For weekly: 0=Sunday, 1=Monday, etc.
  dayOfMonth?: number; // For monthly: 1-31
  months?: number[]; // For yearly: 0=January, 1=February, etc.
  endDate?: Date;
}

export interface RecurringTaskData {
  id: string;
  baseTask: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>;
  pattern: StandardizedRecurringPattern;
  nextOccurrence: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class StandardizedRecurringService {
  /**
   * Create a new recurring task
   */
  static async createRecurringTask(
    baseTask: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>,
    pattern: StandardizedRecurringPattern
  ): Promise<string> {
    try {
      const nextOccurrence = this.calculateNextOccurrence(new Date(baseTask.date), pattern);
      
      const { data, error } = await supabase
        .from('recurring_tasks')
        .insert({
          title: baseTask.title,
          description: baseTask.description,
          priority: baseTask.priority,
          category: baseTask.category,
          user_id: (baseTask as any).user_id,
          pattern: pattern as any,
          frequency: pattern.frequency,
          start_date: baseTask.date,
          end_date: pattern.endDate?.toISOString(),
          next_occurrence: nextOccurrence.toISOString(),
          status: 'pending'
        })
        .select()
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('Error creating recurring task:', error);
      throw error;
    }
  }

  /**
   * Calculate the next occurrence based on pattern
   */
  static calculateNextOccurrence(
    currentDate: Date,
    pattern: StandardizedRecurringPattern
  ): Date {
    let nextDate = new Date(currentDate);

    switch (pattern.type) {
      case 'daily':
        nextDate = addDays(currentDate, pattern.frequency);
        break;

      case 'weekly':
        if (pattern.daysOfWeek && pattern.daysOfWeek.length > 0) {
          const currentDay = currentDate.getDay();
          const validDays = pattern.daysOfWeek.sort();
          
          // Find next valid day
          let nextDay = validDays.find(day => day > currentDay);
          if (!nextDay) {
            // No more days this week, go to first day of next week
            nextDay = validDays[0];
            nextDate = addWeeks(currentDate, pattern.frequency);
            nextDate.setDate(nextDate.getDate() - nextDate.getDay() + nextDay);
          } else {
            nextDate.setDate(nextDate.getDate() + (nextDay - currentDay));
          }
        } else {
          nextDate = addWeeks(currentDate, pattern.frequency);
        }
        break;

      case 'monthly':
        nextDate = addMonths(currentDate, pattern.frequency);
        if (pattern.dayOfMonth) {
          nextDate.setDate(pattern.dayOfMonth);
        }
        break;

      case 'yearly':
        nextDate = addYears(currentDate, pattern.frequency);
        if (pattern.months && pattern.months.length > 0) {
          nextDate.setMonth(pattern.months[0]);
        }
        break;
    }

    // Ensure we don't exceed end date
    if (pattern.endDate && isAfter(nextDate, pattern.endDate)) {
      return pattern.endDate;
    }

    return nextDate;
  }

  /**
   * Generate task instances for all due recurring tasks
   */
  static async processDueRecurringTasks(): Promise<Task[]> {
    try {
      const { data: recurringTasks, error } = await supabase
        .from('recurring_tasks')
        .select('*')
        .eq('status', 'pending')
        .lte('next_occurrence', new Date().toISOString());

      if (error) throw error;

      const generatedTasks: Task[] = [];

      for (const recurringTask of recurringTasks) {
        const taskData = {
          title: recurringTask.title,
          description: recurringTask.description || '',
          priority: recurringTask.priority as 'low' | 'medium' | 'high',
          category: recurringTask.category,
          date: format(new Date(recurringTask.next_occurrence), 'yyyy-MM-dd'),
          status: 'pending' as const,
          user_id: recurringTask.user_id,
          recurring_task_id: recurringTask.id
        };

        // Create the task instance
        const { data: newTask, error: taskError } = await supabase
          .from('tasks')
          .insert(taskData)
          .select()
          .single();

        if (taskError) {
          console.error('Error creating task instance:', taskError);
          continue;
        }

        generatedTasks.push(newTask as any);

        // Calculate next occurrence
        const nextOccurrence = this.calculateNextOccurrence(
          new Date(recurringTask.next_occurrence),
          recurringTask.pattern as any as StandardizedRecurringPattern
        );

        // Update recurring task with next occurrence
        await supabase
          .from('recurring_tasks')
          .update({
            next_occurrence: nextOccurrence.toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', recurringTask.id);
      }

      return generatedTasks;
    } catch (error) {
      console.error('Error processing due recurring tasks:', error);
      return [];
    }
  }

  /**
   * Get recurring tasks for a user
   */
  static async getUserRecurringTasks(userId: string): Promise<RecurringTaskData[]> {
    try {
      const { data, error } = await supabase
        .from('recurring_tasks')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'pending');

      if (error) throw error;

      return data.map(task => ({
        id: task.id,
        baseTask: {
          title: task.title,
          description: task.description || '',
          priority: task.priority as 'low' | 'medium' | 'high',
          category: task.category,
          date: task.start_date,
          status: 'pending',
          user_id: task.user_id
        },
        pattern: task.pattern as any as StandardizedRecurringPattern,
        nextOccurrence: new Date(task.next_occurrence),
        isActive: task.status === 'pending',
        createdAt: new Date(task.created_at),
        updatedAt: new Date(task.updated_at)
      }));
    } catch (error) {
      console.error('Error fetching recurring tasks:', error);
      return [];
    }
  }

  /**
   * Update a recurring task
   */
  static async updateRecurringTask(
    id: string,
    updates: Partial<{
      pattern: StandardizedRecurringPattern;
      endDate: Date;
      isActive: boolean;
    }>
  ): Promise<void> {
    try {
      const updateData: any = {};

      if (updates.pattern) {
        updateData.pattern = updates.pattern;
        updateData.frequency = updates.pattern.frequency;
      }

      if (updates.endDate) {
        updateData.end_date = updates.endDate.toISOString();
      }

      if (updates.isActive !== undefined) {
        updateData.status = updates.isActive ? 'pending' : 'paused';
      }

      updateData.updated_at = new Date().toISOString();

      const { error } = await supabase
        .from('recurring_tasks')
        .update(updateData)
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating recurring task:', error);
      throw error;
    }
  }

  /**
   * Delete a recurring task
   */
  static async deleteRecurringTask(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('recurring_tasks')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting recurring task:', error);
      throw error;
    }
  }

  /**
   * Get human readable description of recurring pattern
   */
  static getPatternDescription(pattern: StandardizedRecurringPattern): string {
    const freqText = pattern.frequency === 1 ? '' : `every ${pattern.frequency} `;

    switch (pattern.type) {
      case 'daily':
        return `${freqText}day${pattern.frequency !== 1 ? 's' : ''}`;

      case 'weekly':
        if (pattern.daysOfWeek && pattern.daysOfWeek.length > 0) {
          const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
          const dayNames = pattern.daysOfWeek.map(d => days[d]).join(', ');
          return `${freqText}week${pattern.frequency !== 1 ? 's' : ''} on ${dayNames}`;
        }
        return `${freqText}week${pattern.frequency !== 1 ? 's' : ''}`;

      case 'monthly':
        if (pattern.dayOfMonth) {
          return `${freqText}month${pattern.frequency !== 1 ? 's' : ''} on day ${pattern.dayOfMonth}`;
        }
        return `${freqText}month${pattern.frequency !== 1 ? 's' : ''}`;

      case 'yearly':
        if (pattern.months && pattern.months.length > 0) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const monthList = pattern.months.map(m => monthNames[m]).join(', ');
          return `${freqText}year${pattern.frequency !== 1 ? 's' : ''} in ${monthList}`;
        }
        return `${freqText}year${pattern.frequency !== 1 ? 's' : ''}`;

      default:
        return 'Custom pattern';
    }
  }

  /**
   * Helper methods to create common patterns
   */
  static createDailyPattern(frequency: number = 1, endDate?: Date): StandardizedRecurringPattern {
    return { type: 'daily', frequency, endDate };
  }

  static createWeeklyPattern(daysOfWeek: number[], frequency: number = 1, endDate?: Date): StandardizedRecurringPattern {
    return { type: 'weekly', frequency, daysOfWeek, endDate };
  }

  static createMonthlyPattern(dayOfMonth: number, frequency: number = 1, endDate?: Date): StandardizedRecurringPattern {
    return { type: 'monthly', frequency, dayOfMonth, endDate };
  }

  static createYearlyPattern(months: number[], frequency: number = 1, endDate?: Date): StandardizedRecurringPattern {
    return { type: 'yearly', frequency, months, endDate };
  }
}