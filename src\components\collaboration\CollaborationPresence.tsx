import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface UserPresence {
  user_id: string;
  full_name?: string;
  avatar_url?: string;
  status: 'online' | 'away' | 'busy';
  last_seen: string;
  current_task?: string;
}

interface CollaborationPresenceProps {
  taskId?: string;
  className?: string;
}

export const CollaborationPresence: React.FC<CollaborationPresenceProps> = ({ 
  taskId, 
  className = "" 
}) => {
  const [presences, setPresences] = useState<Record<string, UserPresence>>({});
  const [isOnline, setIsOnline] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) return;

    const channel = supabase.channel(`collaboration:${taskId || 'global'}`, {
      config: {
        presence: {
          key: user.id,
        },
      },
    });

    // Track user presence
    const trackPresence = async () => {
      const userStatus: UserPresence = {
        user_id: user.id,
        full_name: user.user_metadata?.full_name,
        avatar_url: user.user_metadata?.avatar_url,
        status: isOnline ? 'online' : 'away',
        last_seen: new Date().toISOString(),
        current_task: taskId
      };

      await channel.track(userStatus);
    };

    // Listen to presence changes
    channel
      .on('presence', { event: 'sync' }, () => {
        const newState = channel.presenceState();
        const presenceMap: Record<string, UserPresence> = {};
        
        Object.entries(newState).forEach(([key, presences]) => {
          if (presences && presences.length > 0) {
            const presence = presences[0] as any;
            if (presence && typeof presence === 'object' && presence.user_id) {
              presenceMap[key] = presence as UserPresence;
            }
          }
        });
        
        setPresences(presenceMap);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', key, leftPresences);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          await trackPresence();
        }
      });

    // Handle visibility changes
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      setIsOnline(isVisible);
      
      if (isVisible) {
        trackPresence();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      channel.unsubscribe();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, taskId, isOnline]);

  // Filter out current user and get active collaborators
  const activeCollaborators = Object.values(presences).filter(
    p => p.user_id !== user?.id && 
    new Date().getTime() - new Date(p.last_seen).getTime() < 300000 // 5 minutes
  );

  if (activeCollaborators.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex -space-x-2">
        {activeCollaborators.slice(0, 3).map((collaborator) => (
          <div
            key={collaborator.user_id}
            className="relative"
            title={`${collaborator.full_name || 'Anonymous'} (${collaborator.status})`}
          >
            {collaborator.avatar_url ? (
              <img
                src={collaborator.avatar_url}
                alt={collaborator.full_name || 'User'}
                className="w-8 h-8 rounded-full border-2 border-background"
              />
            ) : (
              <div className="w-8 h-8 rounded-full border-2 border-background bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium">
                {(collaborator.full_name || 'A').charAt(0).toUpperCase()}
              </div>
            )}
            <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${
              collaborator.status === 'online' ? 'bg-green-500' :
              collaborator.status === 'busy' ? 'bg-red-500' : 'bg-yellow-500'
            }`} />
          </div>
        ))}
      </div>
      
      {activeCollaborators.length > 3 && (
        <div className="text-xs text-muted-foreground">
          +{activeCollaborators.length - 3} more
        </div>
      )}
      
      <div className="text-xs text-muted-foreground">
        {activeCollaborators.length === 1 ? '1 collaborator' : `${activeCollaborators.length} collaborators`} online
      </div>
    </div>
  );
};