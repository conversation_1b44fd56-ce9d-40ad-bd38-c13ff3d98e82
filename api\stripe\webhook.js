const {
  stripe,
  config,
  formatErrorResponse,
  formatSuccessResponse,
} = require('./config');

// You'll need to implement these functions based on your Supabase integration
const {
  updateUserSubscription,
  getUserByStripeCustomerId,
  sendTrialEndingEmail,
  sendSubscriptionUpdatedEmail,
} = require('./database'); // We'll create this next

/**
 * Handles Stripe webhook events
 * 
 * This endpoint processes various subscription lifecycle events from Stripe
 */
module.exports = async (req, res) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const sig = req.headers['stripe-signature'];
  let event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      config.stripeWebhookSecret
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).json({ error: 'Invalid signature' });
  }

  try {
    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return res.status(200).json({ received: true });

  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).json({ error: 'Webhook processing failed' });
  }
};

// Event handlers

async function handleCheckoutSessionCompleted(session) {
  console.log('Checkout session completed:', session.id);
  
  try {
    // Get customer and subscription details
    const customer = await stripe.customers.retrieve(session.customer);
    const subscription = await stripe.subscriptions.retrieve(session.subscription);
    
    // Extract user ID from metadata
    const userId = session.metadata.userId || subscription.metadata.userId;
    
    if (!userId) {
      console.error('No user ID found in session metadata');
      return;
    }

    // Update user subscription in database
    await updateUserSubscription(userId, {
      stripeCustomerId: customer.id,
      stripeSubscriptionId: subscription.id,
      tier: 'pro',
      status: subscription.status === 'trialing' ? 'trial' : 'active',
      trialEndsAt: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
    });

    console.log(`Subscription activated for user ${userId}`);
  } catch (error) {
    console.error('Error handling checkout session completed:', error);
  }
}

async function handleSubscriptionCreated(subscription) {
  console.log('Subscription created:', subscription.id);
  
  try {
    const userId = subscription.metadata.userId;
    
    if (!userId) {
      console.error('No user ID found in subscription metadata');
      return;
    }

    await updateUserSubscription(userId, {
      stripeSubscriptionId: subscription.id,
      tier: 'pro',
      status: subscription.status === 'trialing' ? 'trial' : 'active',
      trialEndsAt: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
    });

    console.log(`Subscription created for user ${userId}`);
  } catch (error) {
    console.error('Error handling subscription created:', error);
  }
}

async function handleSubscriptionUpdated(subscription) {
  console.log('Subscription updated:', subscription.id);
  
  try {
    const user = await getUserByStripeCustomerId(subscription.customer);
    
    if (!user) {
      console.error('No user found for customer:', subscription.customer);
      return;
    }

    // Map Stripe status to our subscription status
    let status = 'active';
    switch (subscription.status) {
      case 'trialing':
        status = 'trial';
        break;
      case 'active':
        status = 'active';
        break;
      case 'past_due':
        status = 'past_due';
        break;
      case 'canceled':
      case 'unpaid':
        status = 'canceled';
        break;
    }

    await updateUserSubscription(user.id, {
      status: status,
      trialEndsAt: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
    });

    // Send email notification for important updates
    await sendSubscriptionUpdatedEmail(user.email, status);

    console.log(`Subscription updated for user ${user.id}: ${status}`);
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionDeleted(subscription) {
  console.log('Subscription deleted:', subscription.id);
  
  try {
    const user = await getUserByStripeCustomerId(subscription.customer);
    
    if (!user) {
      console.error('No user found for customer:', subscription.customer);
      return;
    }

    // Downgrade user to free tier
    await updateUserSubscription(user.id, {
      tier: 'free',
      status: 'canceled',
      trialEndsAt: null,
    });

    console.log(`Subscription canceled for user ${user.id}`);
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

async function handleTrialWillEnd(subscription) {
  console.log('Trial will end for subscription:', subscription.id);
  
  try {
    const user = await getUserByStripeCustomerId(subscription.customer);
    
    if (!user) {
      console.error('No user found for customer:', subscription.customer);
      return;
    }

    // Send trial ending email
    await sendTrialEndingEmail(user.email);

    console.log(`Trial ending email sent to user ${user.id}`);
  } catch (error) {
    console.error('Error handling trial will end:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  console.log('Invoice payment succeeded:', invoice.id);
  
  try {
    const user = await getUserByStripeCustomerId(invoice.customer);
    
    if (!user) {
      console.error('No user found for customer:', invoice.customer);
      return;
    }

    // Ensure subscription is active
    await updateUserSubscription(user.id, {
      status: 'active',
    });

    console.log(`Payment succeeded for user ${user.id}`);
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleInvoicePaymentFailed(invoice) {
  console.log('Invoice payment failed:', invoice.id);
  
  try {
    const user = await getUserByStripeCustomerId(invoice.customer);
    
    if (!user) {
      console.error('No user found for customer:', invoice.customer);
      return;
    }

    // Mark subscription as past due
    await updateUserSubscription(user.id, {
      status: 'past_due',
    });

    console.log(`Payment failed for user ${user.id}`);
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}
