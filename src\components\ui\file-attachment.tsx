import React, { useState, useRef } from 'react';
import { FileReference } from '@/types/task';
import { storeFile, openFile, deleteFile, formatFileSize, getFileTypeInfo } from '@/lib/file-utils';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Paperclip,
    Trash2,
    Upload,
    File,
    Image,
    FileText,
    Eye,
    AlertCircle,
    X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileAttachmentProps {
    attachments: Array<string | FileReference>;
    onAttachmentsChange: (attachments: Array<string | FileReference>) => void;
    onDeleteAttachment?: (index: number) => void;
    maxFiles?: number;
    maxFileSize?: number; // in MB
    acceptedTypes?: string[];
    showPreview?: boolean;
    disabled?: boolean;
}

const getFileIcon = (type: string) => {
    const typeInfo = getFileTypeInfo(type);
    switch (typeInfo.icon) {
        case 'image': return <Image className="h-4 w-4" />;
        case 'document': return <FileText className="h-4 w-4" />;
        default: return <File className="h-4 w-4" />;
    }
};

const getFileTypeColor = (type: string) => {
    const typeInfo = getFileTypeInfo(type);
    switch (typeInfo.color) {
        case 'green': return 'bg-green-100 text-green-700 border-green-300';
        case 'blue': return 'bg-blue-100 text-blue-700 border-blue-300';
        case 'red': return 'bg-red-100 text-red-700 border-red-300';
        case 'purple': return 'bg-purple-100 text-purple-700 border-purple-300';
        case 'orange': return 'bg-orange-100 text-orange-700 border-orange-300';
        default: return 'bg-gray-100 text-gray-700 border-gray-300';
    }
};

export const FileAttachment: React.FC<FileAttachmentProps> = ({
    attachments,
    onAttachmentsChange,
    onDeleteAttachment,
    maxFiles = 10,
    maxFileSize = 10, // 10MB default
    acceptedTypes = [],
    showPreview = true,
    disabled = false
}) => {
    const [dragOver, setDragOver] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileUpload = async (files: FileList) => {
        if (disabled) return;

        setError(null);
        setUploading(true);

        try {
            const newAttachments = [...attachments];
            const maxSizeBytes = maxFileSize * 1024 * 1024;

            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // Check file count limit
                if (newAttachments.length >= maxFiles) {
                    setError(`Maximum ${maxFiles} files allowed`);
                    break;
                }

                // Check file size
                if (file.size > maxSizeBytes) {
                    setError(`File "${file.name}" is too large. Maximum size is ${maxFileSize}MB`);
                    continue;
                }

                // Check file type if restrictions exist
                if (acceptedTypes.length > 0 && !acceptedTypes.some(type => file.type.includes(type))) {
                    setError(`File type "${file.type}" is not allowed`);
                    continue;
                }

                // Store file in IndexedDB
                try {
                    const storedFileRef = await storeFile(file);
                    newAttachments.push(storedFileRef);
                } catch (dbError) {
                    console.error('Error storing attachment:', dbError);
                    setError(`Failed to store file "${file.name}"`);
                }
            }

            onAttachmentsChange(newAttachments);
        } catch (error) {
            console.error('Error uploading files:', error);
            setError('Failed to upload files');
        } finally {
            setUploading(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(false);

        if (disabled) return;

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        if (!disabled) {
            setDragOver(true);
        }
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(false);
    };

    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && files.length > 0) {
            handleFileUpload(files);
        }
        // Reset input value to allow selecting the same file again
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleOpenFile = async (fileRef: FileReference) => {
        try {
            await openFile(fileRef);
        } catch (error) {
            console.error('Error opening file:', error);
            setError(`Failed to open file "${fileRef.name}"`);
        }
    };

    const handleDeleteAttachment = async (index: number) => {
        const attachment = attachments[index];

        // If it's a FileReference, delete from IndexedDB
        if (typeof attachment !== 'string' && 'key' in attachment) {
            try {
                await deleteFile(attachment.key);
            } catch (error) {
                console.error('Error deleting file from storage:', error);
            }
        }

        // Remove from attachments array
        const newAttachments = attachments.filter((_, i) => i !== index);
        onAttachmentsChange(newAttachments);

        // Call optional callback
        if (onDeleteAttachment) {
            onDeleteAttachment(index);
        }
    };

    const fileAttachments = attachments.filter(att => typeof att !== 'string' || att.trim() !== '') as FileReference[];

    return (
        <div className="space-y-3">
            <Label className="text-sm font-semibold flex items-center gap-2">
                <Paperclip className="h-4 w-4" />
                File Attachments
                {fileAttachments.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                        {fileAttachments.length}
                    </Badge>
                )}
            </Label>

            {/* Upload Area */}
            <div
                className={cn(
                    "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
                    dragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                    disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary/50 hover:bg-muted/50"
                )}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onClick={() => {
                  if (!disabled && fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }}
            >
                <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    onChange={handleFileInputChange}
                    className="hidden"
                    accept={acceptedTypes.length > 0 ? acceptedTypes.join(',') : undefined}
                    disabled={disabled}
                />

                <div className="space-y-2">
                    <Upload className={cn("h-8 w-8 mx-auto", uploading ? "animate-pulse" : "")} />
                    <div>
                        <p className="text-sm font-medium">
                            {uploading ? 'Uploading files...' : 'Drop files here or click to browse'}
                        </p>
                        <p className="text-xs text-muted-foreground">
                            Maximum {maxFiles} files, {maxFileSize}MB each
                            {acceptedTypes.length > 0 && ` • ${acceptedTypes.join(', ')}`}
                        </p>
                    </div>
                </div>
            </div>

            {/* Error Display */}
            {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">{error}</span>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setError(null)}
                        className="ml-auto h-6 w-6 p-0"
                    >
                        <X className="h-3 w-3" />
                    </Button>
                </div>
            )}

            {/* Attached Files List */}
            {fileAttachments.length > 0 && (
                <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">Attached Files</Label>
                    <div className="space-y-2">
                        {fileAttachments.map((attachment, index) => {
                            const isFileRef = typeof attachment !== 'string';
                            const fileRef = isFileRef ? attachment as FileReference : null;

                            if (!fileRef) return null;

                            return (
                                <Card key={fileRef.key} className="p-3">
                                    <div className="flex items-center justify-between gap-3">
                                        <div className="flex items-center gap-3 flex-1 min-w-0">
                                            <div className={cn(
                                                "p-2 rounded-lg border",
                                                getFileTypeColor(fileRef.type)
                                            )}>
                                                {getFileIcon(fileRef.type)}
                                            </div>

                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium truncate" title={fileRef.name}>
                                                    {fileRef.name}
                                                </p>
                                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                                    <span>{formatFileSize(fileRef.size)}</span>
                                                    <span>•</span>
                                                    <span>{fileRef.type}</span>
                                                    {fileRef.uploadedAt && (
                                                        <>
                                                            <span>•</span>
                                                            <span>{new Date(fileRef.uploadedAt).toLocaleDateString()}</span>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-1">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleOpenFile(fileRef)}
                                                className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                                title="Open file"
                                            >
                                                <Eye className="h-4 w-4" />
                                            </Button>

                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleDeleteAttachment(index)}
                                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                                title="Delete file"
                                                disabled={disabled}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </Card>
                            );
                        })}
                    </div>
                </div>
            )}
        </div>
    );
};