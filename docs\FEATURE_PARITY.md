# Feature Parity Report (Web vs Mobile)

## Missing in Web App
1. **Notification Settings**
   - Mobile: `NotificationSettingsScreen.tsx`
   - Web: Needs implementation

2. **Analytics Detail Views**
   - Mobile: `AnalyticsDetailScreen.tsx`
   - Web: Only has basic analytics dashboard

3. **Task Management**
   - Mobile has dedicated screens for:
     - Task assignment (`AssignTaskScreen.tsx`)
     - Detailed editing (`EditTaskScreen.tsx`)
     - Task details modal (`TaskDetailsModalScreen.tsx`)

4. **Onboarding Flows**
   - Mobile has complete onboarding sequence:
     - `OnboardingWelcomeScreen.tsx`
     - `OnboardingTourScreen.tsx`
   - Web only has basic `Onboarding.tsx`

## Missing in Mobile App
1. **Subscription Management**
   - Web: `Subscription.tsx`
   - Mobile: Not implemented

## Implementation Differences
1. **Settings**
   - Web: Basic implementation (`Settings.tsx`)
   - Mobile: Full-featured (`SettingsScreen.tsx`)

2. **Calendar/Views**
   - Web: Component-based (`ContributionCalendar.tsx`)
   - Mobile: Screen-based (`CalendarScreen.tsx`)

## Recommended Actions
1. Create web versions of missing mobile features
2. Implement mobile subscription management
3. Standardize settings implementation
4. Align task management interfaces