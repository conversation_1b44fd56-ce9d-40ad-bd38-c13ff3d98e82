import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Mic, MicOff, Square, Volume2, VolumeX, Settings, Refresh<PERSON>w, Copy, Trash2, Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

// Extended SpeechRecognition interface
interface SpeechRecognitionExtended extends EventTarget {
  continuous: boolean
  interimResults: boolean
  lang: string
  maxAlternatives: number
  serviceURI: string
  grammars?: any
  onstart: ((this: any, ev: Event) => any) | null
  onresult: ((this: any, ev: any) => any) | null
  onerror: ((this: any, ev: any) => any) | null
  onend: ((this: any, ev: Event) => any) | null
  start: () => void
  stop: () => void
}

interface SpeechRecognitionResultExtended extends SpeechRecognitionResult {
  isFinal: boolean
  [index: number]: SpeechRecognitionAlternative
}

interface SpeechRecognitionEventExtended extends Event {
  resultIndex: number
  results: SpeechRecognitionResultList & {
    [index: number]: SpeechRecognitionResultExtended
  }
}

interface VoiceToTextProps {
  onTextGenerated: (text: string) => void
  onClose?: () => void
  placeholder?: string
  language?: string
  autoStart?: boolean
  showLanguageSelector?: boolean
  className?: string
  disabled?: boolean
}

// Language options for speech recognition
const LANGUAGE_OPTIONS = [
  { code: 'en-US', name: 'English (US)', flag: '🇺🇸' },
  { code: 'en-GB', name: 'English (UK)', flag: '🇬🇧' },
  { code: 'es-ES', name: 'Spanish (Spain)', flag: '🇪🇸' },
  { code: 'es-MX', name: 'Spanish (Mexico)', flag: '🇲🇽' },
  { code: 'fr-FR', name: 'French (France)', flag: '🇫🇷' },
  { code: 'de-DE', name: 'German (Germany)', flag: '🇩🇪' },
  { code: 'it-IT', name: 'Italian (Italy)', flag: '🇮🇹' },
  { code: 'pt-BR', name: 'Portuguese (Brazil)', flag: '🇧🇷' },
  { code: 'ru-RU', name: 'Russian (Russia)', flag: '🇷🇺' },
  { code: 'ja-JP', name: 'Japanese (Japan)', flag: '🇯🇵' },
  { code: 'ko-KR', name: 'Korean (South Korea)', flag: '🇰🇷' },
  { code: 'zh-CN', name: 'Chinese (Mandarin)', flag: '🇨🇳' },
  { code: 'ar-SA', name: 'Arabic (Saudi Arabia)', flag: '🇸🇦' },
  { code: 'hi-IN', name: 'Hindi (India)', flag: '🇮🇳' },
]

export const VoiceToText: React.FC<VoiceToTextProps> = ({
  onTextGenerated,
  onClose,
  placeholder = 'Start speaking to dictate text...',
  language = 'en-US',
  autoStart = false,
  showLanguageSelector = true,
  className,
  disabled = false
}) => {
  const [isListening, setIsListening] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [interimTranscript, setInterimTranscript] = useState('')
  const [isSupported, setIsSupported] = useState(false)
  const [selectedLanguage, setSelectedLanguage] = useState(language)
  const [confidence, setConfidence] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [isInitializing, setIsInitializing] = useState(false)
  const [wordCount, setWordCount] = useState(0)
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false)
  
  const { toast } = useToast()
  const recognitionRef = useRef<SpeechRecognitionExtended | null>(null)
  const finalTranscriptRef = useRef('')
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Check for Web Speech API support
  useEffect(() => {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    if (SpeechRecognition) {
      setIsSupported(true)
    } else {
      setIsSupported(false)
      setError('Speech recognition is not supported in this browser. Please use Chrome, Safari, or Edge.')
    }
  }, [])

  // Initialize speech recognition
  useEffect(() => {
    if (!isSupported || disabled) return

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    if (!SpeechRecognition) return

    recognitionRef.current = new SpeechRecognition() as SpeechRecognitionExtended
    const recognition = recognitionRef.current

    recognition.continuous = true
    recognition.interimResults = true
    recognition.lang = selectedLanguage
    recognition.maxAlternatives = 1

    recognition.onstart = () => {
      setIsListening(true)
      setError(null)
      setIsInitializing(false)
      toast({
        title: '🎤 Listening...',
        description: 'Start speaking to dictate text',
        duration: 2000,
      })
    }

    recognition.onresult = (event: SpeechRecognitionEventExtended) => {
      let interimTranscript = ''
      let finalTranscript = finalTranscriptRef.current

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i]
        if (result.isFinal) {
          finalTranscript += result[0].transcript
          setConfidence(result[0].confidence || 0)
        } else {
          interimTranscript += result[0].transcript
        }
      }

      finalTranscriptRef.current = finalTranscript
      setTranscript(finalTranscript)
      setInterimTranscript(interimTranscript)
      setWordCount(finalTranscript.trim().split(/\s+/).filter(word => word.length > 0).length)
    }

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error)
      setIsListening(false)
      setIsInitializing(false)
      
      let errorMessage = 'An error occurred during speech recognition.'
      
      switch (event.error) {
        case 'network':
          errorMessage = 'Network error. Please check your internet connection.'
          break
        case 'not-allowed':
          errorMessage = 'Microphone permission denied. Please allow microphone access.'
          break
        case 'no-speech':
          errorMessage = 'No speech detected. Please try again.'
          break
        case 'audio-capture':
          errorMessage = 'Audio capture failed. Please check your microphone.'
          break
        case 'service-not-allowed':
          errorMessage = 'Speech recognition service not allowed.'
          break
        case 'bad-grammar':
          errorMessage = 'Grammar error in speech recognition.'
          break
        case 'language-not-supported':
          errorMessage = `Language ${selectedLanguage} is not supported.`
          break
        default:
          errorMessage = `Speech recognition error: ${event.error}`
      }

      setError(errorMessage)
      toast({
        title: 'Speech Recognition Error',
        description: errorMessage,
        variant: 'destructive',
        duration: 4000,
      })
    }

    recognition.onend = () => {
      setIsListening(false)
      setIsInitializing(false)
      
      // Auto-restart if we were listening and it ended unexpectedly
      if (isListening && !error) {
        restartTimeoutRef.current = setTimeout(() => {
          if (recognitionRef.current && isListening) {
            startListening()
          }
        }, 1000)
      }
    }

    // Auto-start if requested
    if (autoStart) {
      startListening()
    }

    return () => {
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current)
      }
      if (recognition) {
        recognition.stop()
      }
    }
  }, [isSupported, selectedLanguage, disabled])

  const startListening = () => {
    if (!recognitionRef.current || disabled) return

    setIsInitializing(true)
    setError(null)
    
    try {
      recognitionRef.current.start()
    } catch (error) {
      console.error('Failed to start speech recognition:', error)
      setError('Failed to start speech recognition. Please try again.')
      setIsInitializing(false)
    }
  }

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop()
    }
    setIsListening(false)
    setIsInitializing(false)
    
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current)
    }
  }

  const clearTranscript = () => {
    setTranscript('')
    setInterimTranscript('')
    finalTranscriptRef.current = ''
    setWordCount(0)
    setConfidence(0)
  }

  const copyToClipboard = async () => {
    if (!transcript.trim()) return

    try {
      await navigator.clipboard.writeText(transcript)
      toast({
        title: '📋 Copied!',
        description: 'Text copied to clipboard',
        duration: 2000,
      })
    } catch (error) {
      console.error('Failed to copy text:', error)
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy text to clipboard',
        variant: 'destructive',
        duration: 3000,
      })
    }
  }

  const useTranscript = () => {
    if (!transcript.trim()) return

    onTextGenerated(transcript)
    toast({
      title: '✅ Text Generated',
      description: 'Dictated text has been added',
      duration: 3000,
    })
    
    if (onClose) {
      onClose()
    }
  }

  const toggleLanguageDropdown = () => {
    setShowLanguageDropdown(!showLanguageDropdown)
  }

  const selectLanguage = (langCode: string) => {
    setSelectedLanguage(langCode)
    setShowLanguageDropdown(false)
    
    // Restart recognition with new language if currently listening
    if (isListening) {
      stopListening()
      setTimeout(() => startListening(), 500)
    }
  }

  const currentLanguage = LANGUAGE_OPTIONS.find(lang => lang.code === selectedLanguage) || LANGUAGE_OPTIONS[0]

  if (!isSupported) {
    return (
      <Card className={cn('w-full max-w-2xl mx-auto', className)}>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="text-6xl">🚫</div>
            <h3 className="text-lg font-semibold">Speech Recognition Not Supported</h3>
            <p className="text-muted-foreground">
              Your browser doesn't support speech recognition. Please use Chrome, Safari, or Edge for the best experience.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('w-full max-w-2xl mx-auto', className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg">Voice-to-Text Dictation</CardTitle>
            {isListening && (
              <Badge variant="outline" className="bg-red-100 text-red-700 border-red-300">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
                Listening
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {showLanguageSelector && (
              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleLanguageDropdown}
                  className="gap-2"
                >
                  <span>{currentLanguage.flag}</span>
                  <span className="hidden sm:inline">{currentLanguage.name}</span>
                  <Settings className="h-4 w-4" />
                </Button>
                
                {showLanguageDropdown && (
                  <div className="absolute right-0 top-full mt-2 w-64 bg-white dark:bg-gray-800 border rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                    {LANGUAGE_OPTIONS.map((lang) => (
                      <button
                        key={lang.code}
                        onClick={() => selectLanguage(lang.code)}
                        className={cn(
                          'w-full px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2',
                          selectedLanguage === lang.code && 'bg-blue-50 dark:bg-blue-900/20'
                        )}
                      >
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
          </div>
        )}

        {/* Transcript Display */}
        <div className="min-h-[120px] p-4 border rounded-lg bg-gray-50 dark:bg-gray-900 relative">
          {transcript || interimTranscript ? (
            <div className="text-sm">
              <span className="text-gray-900 dark:text-gray-100">
                {transcript}
              </span>
              <span className="text-gray-500 dark:text-gray-400 italic">
                {interimTranscript}
              </span>
              {isListening && (
                <span className="inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1"></span>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground text-sm">{placeholder}</p>
          )}
        </div>

        {/* Stats */}
        {(transcript || interimTranscript) && (
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Words: {wordCount}</span>
            {confidence > 0 && (
              <span>Confidence: {Math.round(confidence * 100)}%</span>
            )}
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              onClick={isListening ? stopListening : startListening}
              disabled={disabled || isInitializing}
              className={cn(
                'flex items-center gap-2',
                isListening ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'
              )}
            >
              {isInitializing ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : isListening ? (
                <Square className="h-4 w-4" />
              ) : (
                <Mic className="h-4 w-4" />
              )}
              {isInitializing ? 'Initializing...' : isListening ? 'Stop' : 'Start'}
            </Button>

            {transcript && (
              <Button
                onClick={clearTranscript}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Clear
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            {transcript && (
              <>
                <Button
                  onClick={copyToClipboard}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
                
                <Button
                  onClick={useTranscript}
                  className="flex items-center gap-2 bg-green-500 hover:bg-green-600"
                >
                  <Check className="h-4 w-4" />
                  Use Text
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Help Text */}
        <div className="text-xs text-muted-foreground text-center">
          💡 <strong>Tip:</strong> Speak clearly and pause between sentences for best results. 
          Say "period" or "comma" to add punctuation.
        </div>
      </CardContent>
    </Card>
  )
}
