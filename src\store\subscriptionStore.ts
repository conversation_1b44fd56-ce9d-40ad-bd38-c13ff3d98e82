import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { SubscriptionTier, SubscriptionStatus, UserSubscription, StorageUsage } from '@/types/subscription';
import { getSubscriptionPlan, getStorageLimit, TRIAL_DURATION_DAYS } from '@/config/subscription';

interface SubscriptionStore {
  // State
  subscription: UserSubscription | null;
  storageUsage: StorageUsage | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setSubscription: (subscription: UserSubscription) => void;
  setStorageUsage: (usage: StorageUsage) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Computed getters
  getCurrentPlan: () => ReturnType<typeof getSubscriptionPlan>;
  isTrialActive: () => boolean;
  isProUser: () => boolean;
  canUploadFile: (fileSize: number) => boolean;
  getTrialDaysRemaining: () => number;
  getStoragePercentage: () => number;
  
  // Mutations
  updateStorageUsage: (bytesUsed: number, filesCount: number) => void;
  initializeNewUser: (userId: string) => void;
  reset: () => void;
}

const initialState = {
  subscription: null,
  storageUsage: null,
  isLoading: false,
  error: null,
};

export const useSubscriptionStore = create<SubscriptionStore>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Actions
      setSubscription: (subscription) => {
        set({ subscription });
        // Update storage usage when subscription changes
        if (subscription) {
          const limit = getStorageLimit(subscription.tier);
          const current = get().storageUsage;
          if (current) {
            set({
              storageUsage: {
                ...current,
                limit,
                percentage: (current.used / limit) * 100,
              },
            });
          }
        }
      },
      
      setStorageUsage: (usage) => set({ storageUsage: usage }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      
      // Computed getters
      getCurrentPlan: () => {
        const subscription = get().subscription;
        return getSubscriptionPlan(subscription?.tier || 'free');
      },
      
      isTrialActive: () => {
        const subscription = get().subscription;
        if (!subscription || subscription.status !== 'trial') return false;
        if (!subscription.trialEndsAt) return false;
        return new Date(subscription.trialEndsAt) > new Date();
      },
      
      isProUser: () => {
        const subscription = get().subscription;
        return subscription?.tier === 'pro' && 
               (subscription.status === 'active' || subscription.status === 'trial');
      },
      
      canUploadFile: (fileSize: number) => {
        const subscription = get().subscription;
        const storageUsage = get().storageUsage;
        const plan = getSubscriptionPlan(subscription?.tier || 'free');
        
        // Check file size limit
        if (fileSize > plan.limits.maxFileSize) return false;
        
        // Check storage limit
        if (storageUsage && (storageUsage.used + fileSize) > plan.limits.maxStorageTotal) {
          return false;
        }
        
        return true;
      },
      
      getTrialDaysRemaining: () => {
        const subscription = get().subscription;
        if (!subscription?.trialEndsAt) return 0;
        
        const trialEnd = new Date(subscription.trialEndsAt);
        const now = new Date();
        const diffTime = trialEnd.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return Math.max(0, diffDays);
      },
      
      getStoragePercentage: () => {
        const storageUsage = get().storageUsage;
        if (!storageUsage) return 0;
        return Math.min(100, (storageUsage.used / storageUsage.limit) * 100);
      },
      
      // Mutations
      updateStorageUsage: (bytesUsed: number, filesCount: number) => {
        const subscription = get().subscription;
        const limit = getStorageLimit(subscription?.tier || 'free');
        
        set({
          storageUsage: {
            used: bytesUsed,
            limit,
            percentage: (bytesUsed / limit) * 100,
            filesCount,
          },
        });
      },
      
      initializeNewUser: (userId: string) => {
        const trialEndsAt = new Date();
        trialEndsAt.setDate(trialEndsAt.getDate() + TRIAL_DURATION_DAYS);
        
        const newSubscription: UserSubscription = {
          id: userId,
          tier: 'pro', // Start with Pro trial
          status: 'trial',
          trialEndsAt: trialEndsAt.toISOString(),
          stripeCustomerId: null,
          stripeSubscriptionId: null,
          storageUsedBytes: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        const initialStorageUsage: StorageUsage = {
          used: 0,
          limit: getStorageLimit('pro'),
          percentage: 0,
          filesCount: 0,
        };
        
        set({
          subscription: newSubscription,
          storageUsage: initialStorageUsage,
        });
      },
      
      reset: () => set(initialState),
    }),
    {
      name: 'subscription-store',
      partialize: (state) => ({
        subscription: state.subscription,
        storageUsage: state.storageUsage,
      }),
    }
  )
);
