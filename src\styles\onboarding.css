/* Onboarding specific styles */

/* Smooth animations for onboarding flow */
.onboarding-enter {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.onboarding-enter-active {
  opacity: 1;
  transform: translateY(0);
}

.onboarding-exit {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.onboarding-exit-active {
  opacity: 0;
  transform: translateY(-20px);
}

/* Tour overlay styles */
.tour-overlay {
  backdrop-filter: blur(2px);
  background: rgba(0, 0, 0, 0.6);
}

/* Welcome page gradient backgrounds */
.welcome-gradient {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(147, 51, 234, 0.1) 100%);
}

.welcome-gradient-dark {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.05) 0%, 
    rgba(147, 51, 234, 0.05) 100%);
}

/* Calendar placeholder animation */
.calendar-placeholder {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Floating button animation */
.floating-button-hint {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Tour progress bar */
.tour-progress {
  background: linear-gradient(90deg, 
    var(--primary) 0%, 
    var(--primary) var(--progress, 0%), 
    transparent var(--progress, 0%), 
    transparent 100%);
  transition: all 0.3s ease;
}

/* Step indicator animation */
.step-indicator {
  transition: all 0.2s ease;
}

.step-indicator.active {
  transform: scale(1.2);
  box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.2);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .onboarding-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
  
  .tour-content {
    padding: 1rem;
  }
  
  .welcome-header {
    font-size: 1.5rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .welcome-gradient,
  .welcome-gradient-dark {
    background: none;
    border: 2px solid var(--border);
  }
  
  .tour-overlay {
    background: rgba(0, 0, 0, 0.9);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .onboarding-enter,
  .onboarding-exit,
  .step-indicator,
  .floating-button-hint,
  .calendar-placeholder {
    animation: none;
    transition: none;
  }
}

/* Focus styles for accessibility */
.onboarding-button:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Loading states */
.onboarding-loading {
  position: relative;
  overflow: hidden;
}

.onboarding-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Success state animations */
.onboarding-success {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
