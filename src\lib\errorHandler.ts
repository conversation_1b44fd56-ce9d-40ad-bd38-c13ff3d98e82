// Centralized Error Handling System
import { toast } from "@/hooks/use-toast";

// Standard error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION', 
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  STORAGE = 'STORAGE',
  FILE_UPLOAD = 'FILE_UPLOAD',
  VOICE_RECORDING = 'VOICE_RECORDING',
  DATABASE = 'DATABASE',
  UNKNOWN = 'UNKNOWN'
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string | number;
  details?: any;
  stack?: string;
  timestamp: Date;
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',       // User can continue, minor inconvenience
  MEDIUM = 'MEDIUM', // Feature degradation but app functional
  HIGH = 'HIGH',     // Major feature broken
  CRITICAL = 'CRITICAL' // App unusable
}

class ErrorHandler {
  private errorLog: AppError[] = [];
  private maxLogSize = 100;

  /**
   * Create a standardized error object
   */
  createError(
    type: ErrorType,
    message: string,
    code?: string | number,
    details?: any
  ): AppError {
    return {
      type,
      message,
      code,
      details,
      timestamp: new Date(),
      stack: new Error().stack
    };
  }

  /**
   * Handle error with appropriate user feedback and logging
   */
  handle(error: AppError | Error | any, severity: ErrorSeverity = ErrorSeverity.MEDIUM): AppError {
    let appError: AppError;

    // Convert various error types to AppError
    if (this.isAppError(error)) {
      appError = error;
    } else if (error instanceof Error) {
      appError = this.fromError(error);
    } else {
      appError = this.createError(
        ErrorType.UNKNOWN,
        typeof error === 'string' ? error : 'An unexpected error occurred',
        undefined,
        error
      );
    }

    // Log error
    this.logError(appError);

    // Show user notification based on severity
    this.notifyUser(appError, severity);

    // Report to external service in production
    this.reportError(appError, severity);

    return appError;
  }

  /**
   * Handle async operations with automatic error handling
   */
  async handleAsync<T>(
    operation: () => Promise<T>,
    options: {
      errorType?: ErrorType;
      customMessage?: string;
      severity?: ErrorSeverity;
      showToast?: boolean;
    } = {}
  ): Promise<{ data: T | null; error: AppError | null }> {
    try {
      const data = await operation();
      return { data, error: null };
    } catch (error) {
      const appError = this.handle(
        options.customMessage 
          ? this.createError(options.errorType || ErrorType.UNKNOWN, options.customMessage, undefined, error)
          : error,
        options.severity
      );

      return { data: null, error: appError };
    }
  }

  /**
   * Convert native Error to AppError
   */
  private fromError(error: Error): AppError {
    // Detect error type based on message/name
    let type = ErrorType.UNKNOWN;
    
    if (error.message.includes('fetch') || error.message.includes('network')) {
      type = ErrorType.NETWORK;
    } else if (error.message.includes('auth') || error.message.includes('unauthorized')) {
      type = ErrorType.AUTHENTICATION;
    } else if (error.message.includes('validation') || error.message.includes('invalid')) {
      type = ErrorType.VALIDATION;
    } else if (error.message.includes('permission') || error.message.includes('forbidden')) {
      type = ErrorType.PERMISSION;
    } else if (error.message.includes('storage') || error.message.includes('upload')) {
      type = ErrorType.STORAGE;
    }

    return this.createError(type, error.message, undefined, error);
  }

  /**
   * Check if object is AppError
   */
  private isAppError(error: any): error is AppError {
    return error && typeof error === 'object' && 'type' in error && 'message' in error && 'timestamp' in error;
  }

  /**
   * Log error for debugging/monitoring
   */
  private logError(error: AppError): void {
    console.error('Application Error:', {
      type: error.type,
      message: error.message,
      code: error.code,
      details: error.details,
      timestamp: error.timestamp,
      stack: error.stack
    });

    // Add to error log (with size limit)
    this.errorLog.unshift(error);
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }
  }

  /**
   * Show user notification
   */
  private notifyUser(error: AppError, severity: ErrorSeverity): void {
    const userMessage = this.getUserMessage(error, severity);
    
    toast({
      title: this.getToastTitle(severity),
      description: userMessage,
      variant: severity === ErrorSeverity.LOW ? 'default' : 'destructive',
      duration: this.getToastDuration(severity)
    });
  }

  /**
   * Get user-friendly error message
   */
  private getUserMessage(error: AppError, severity: ErrorSeverity): string {
    // Critical errors get generic message to avoid exposing sensitive info
    if (severity === ErrorSeverity.CRITICAL) {
      return "Something went wrong. Please refresh the page or contact support.";
    }

    switch (error.type) {
      case ErrorType.NETWORK:
        return "Connection issue. Please check your internet and try again.";
      case ErrorType.AUTHENTICATION:
        return "Authentication failed. Please sign in again.";
      case ErrorType.VALIDATION:
        return error.message || "Please check your input and try again.";
      case ErrorType.PERMISSION:
        return "You don't have permission to perform this action.";
      case ErrorType.STORAGE:
      case ErrorType.FILE_UPLOAD:
        return "File operation failed. Please try again.";
      case ErrorType.VOICE_RECORDING:
        return "Voice recording failed. Please check microphone permissions.";
      case ErrorType.DATABASE:
        return "Database operation failed. Please try again.";
      default:
        return error.message || "An unexpected error occurred.";
    }
  }

  /**
   * Get toast title based on severity
   */
  private getToastTitle(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.LOW:
        return "Notice";
      case ErrorSeverity.MEDIUM:
        return "Warning";
      case ErrorSeverity.HIGH:
        return "Error";
      case ErrorSeverity.CRITICAL:
        return "Critical Error";
      default:
        return "Error";
    }
  }

  /**
   * Get toast duration based on severity
   */
  private getToastDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 1000;
      case ErrorSeverity.MEDIUM:
        return 1000;
      case ErrorSeverity.HIGH:
        return 1000;
      case ErrorSeverity.CRITICAL:
        return 1000;
      default:
        return 1000;
    }
  }

  /**
   * Report error to external monitoring service
   */
  private reportError(error: AppError, severity: ErrorSeverity): void {
    // Only report medium+ severity errors in production
    if (process.env.NODE_ENV !== 'production' || severity === ErrorSeverity.LOW) {
      return;
    }

    // Here you would integrate with services like:
    // - Sentry
    // - LogRocket
    // - DataDog
    // - Custom logging endpoint
    
    console.info('Error reported to monitoring service:', error);
  }

  /**
   * Get recent error log (for debugging)
   */
  getErrorLog(): AppError[] {
    return [...this.errorLog];
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Get error statistics
   */
  getErrorStats(): { [key in ErrorType]: number } {
    const stats = {} as { [key in ErrorType]: number };
    
    // Initialize all error types
    Object.values(ErrorType).forEach(type => {
      stats[type] = 0;
    });

    // Count occurrences
    this.errorLog.forEach(error => {
      stats[error.type]++;
    });

    return stats;
  }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();

// Convenience functions for common error scenarios
export const handleNetworkError = (error: any) => 
  errorHandler.handle(errorHandler.createError(ErrorType.NETWORK, "Network request failed", undefined, error), ErrorSeverity.MEDIUM);

export const handleAuthError = (error: any) =>
  errorHandler.handle(errorHandler.createError(ErrorType.AUTHENTICATION, "Authentication failed", undefined, error), ErrorSeverity.HIGH);

export const handleValidationError = (message: string, details?: any) =>
  errorHandler.handle(errorHandler.createError(ErrorType.VALIDATION, message, undefined, details), ErrorSeverity.LOW);

export const handleFileUploadError = (error: any) =>
  errorHandler.handle(errorHandler.createError(ErrorType.FILE_UPLOAD, "File upload failed", undefined, error), ErrorSeverity.MEDIUM);

export const handleVoiceRecordingError = (error: any) =>
  errorHandler.handle(errorHandler.createError(ErrorType.VOICE_RECORDING, "Voice recording failed", undefined, error), ErrorSeverity.MEDIUM);

export const handleDatabaseError = (error: any) =>
  errorHandler.handle(errorHandler.createError(ErrorType.DATABASE, "Database operation failed", undefined, error), ErrorSeverity.MEDIUM);

// React hook for error handling in components
export const useErrorHandler = () => {
  return {
    handleError: errorHandler.handle.bind(errorHandler),
    handleAsync: errorHandler.handleAsync.bind(errorHandler),
    createError: errorHandler.createError.bind(errorHandler),
    getErrorLog: errorHandler.getErrorLog.bind(errorHandler),
    getErrorStats: errorHandler.getErrorStats.bind(errorHandler)
  };
};
