import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'app.lovable.fde70a150ea64291a34a0804fec46bcd',
  appName: 'TaskManager Pro',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    url: 'https://fde70a15-0ea6-4291-a34a-0804fec46bcd.lovableproject.com?forceHideBadge=true',
    cleartext: true
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#ffffff',
      androidSplashResourceName: 'splash',
      showSpinner: false
    },
    StatusBar: {
      style: 'dark'
    },
    Keyboard: {
      resize: 'body',
      style: 'dark'
    },
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"]
    },
    LocalNotifications: {
      smallIcon: "ic_stat_icon_config_sample",
      iconColor: "#488AFF",
      sound: "beep.wav"
    },
    Camera: {
      permissions: {
        camera: "We need camera access to take photos for task attachments"
      }
    },
    Geolocation: {
      permissions: {
        location: "We need location access to add location to your tasks"
      }
    },
    Filesystem: {
      iosPresentationStyle: "popover"
    },
    Haptics: {},
    Device: {},
    Network: {},
    App: {
      deepLinkingEnabled: true,
      appScheme: "taskmanager"
    }
  },
  // App shortcuts for home screen quick actions
  cordova: {
    plugins: {
      'cordova-plugin-app-shortcuts': {
        shortcuts: [
          {
            id: 'new-task',
            shortLabel: 'New Task',
            longLabel: 'Create New Task',
            icon: 'plus',
            data: {
              action: 'new-task'
            }
          },
          {
            id: 'view-today',
            shortLabel: 'Today',
            longLabel: 'View Today\'s Tasks',
            icon: 'calendar',
            data: {
              action: 'view-today'
            }
          },
          {
            id: 'quick-voice',
            shortLabel: 'Voice Task',
            longLabel: 'Create Task with Voice',
            icon: 'microphone',
            data: {
              action: 'voice-task'
            }
          }
        ]
      }
    }
  }
};

export default config;