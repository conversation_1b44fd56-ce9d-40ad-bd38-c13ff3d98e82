import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface UIState {
  // Modal states
  isTaskModalOpen: boolean
  isSettingsModalOpen: boolean
  isProfileModalOpen: boolean
  
  // Sidebar state
  isSidebarOpen: boolean
  sidebarView: 'calendar' | 'tasks' | 'analytics' | 'settings'
  
  // View states
  currentView: 'calendar' | 'list' | 'board' | 'analytics'
  currentDate: Date
  
  // Loading states
  isGlobalLoading: boolean
  loadingStates: Record<string, boolean>
  
  // Toast/notification states
  toasts: Array<{
    id: string
    type: 'success' | 'error' | 'info' | 'warning'
    message: string
    duration?: number
  }>
  
  // Theme and preferences
  theme: 'light' | 'dark' | 'system'
  compactMode: boolean
  
  // Actions
  setTaskModalOpen: (open: boolean) => void
  setSettingsModalOpen: (open: boolean) => void
  setProfileModalOpen: (open: boolean) => void
  
  setSidebarOpen: (open: boolean) => void
  setSidebarView: (view: UIState['sidebarView']) => void
  
  setCurrentView: (view: UIState['currentView']) => void
  setCurrentDate: (date: Date) => void
  
  setGlobalLoading: (loading: boolean) => void
  setLoadingState: (key: string, loading: boolean) => void
  getLoadingState: (key: string) => boolean
  
  addToast: (toast: Omit<UIState['toasts'][0], 'id'>) => void
  removeToast: (id: string) => void
  clearToasts: () => void
  
  setTheme: (theme: UIState['theme']) => void
  setCompactMode: (compact: boolean) => void
  
  // Utility actions
  toggleSidebar: () => void
  closeAllModals: () => void
}

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      // Initial state
      isTaskModalOpen: false,
      isSettingsModalOpen: false,
      isProfileModalOpen: false,
      
      isSidebarOpen: true,
      sidebarView: 'calendar',
      
      currentView: 'calendar',
      currentDate: new Date(),
      
      isGlobalLoading: false,
      loadingStates: {},
      
      toasts: [],
      
      theme: 'system',
      compactMode: false,
      
      // Modal actions
      setTaskModalOpen: (open) => set({ isTaskModalOpen: open }),
      setSettingsModalOpen: (open) => set({ isSettingsModalOpen: open }),
      setProfileModalOpen: (open) => set({ isProfileModalOpen: open }),
      
      // Sidebar actions
      setSidebarOpen: (open) => set({ isSidebarOpen: open }),
      setSidebarView: (view) => set({ sidebarView: view }),
      
      // View actions
      setCurrentView: (view) => set({ currentView: view }),
      setCurrentDate: (date) => set({ currentDate: date }),
      
      // Loading actions
      setGlobalLoading: (loading) => set({ isGlobalLoading: loading }),
      setLoadingState: (key, loading) => set((state) => ({
        loadingStates: { ...state.loadingStates, [key]: loading }
      })),
      getLoadingState: (key) => get().loadingStates[key] || false,
      
      // Toast actions
      addToast: (toast) => set((state) => ({
        toasts: [...state.toasts, { ...toast, id: Date.now().toString() }]
      })),
      removeToast: (id) => set((state) => ({
        toasts: state.toasts.filter(toast => toast.id !== id)
      })),
      clearToasts: () => set({ toasts: [] }),
      
      // Theme actions
      setTheme: (theme) => set({ theme }),
      setCompactMode: (compact) => set({ compactMode: compact }),
      
      // Utility actions
      toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
      closeAllModals: () => set({
        isTaskModalOpen: false,
        isSettingsModalOpen: false,
        isProfileModalOpen: false
      })
    }),
    {
      name: 'ui-store'
    }
  )
)
