const {
  stripe,
  config,
  getPriceByLookupKey,
  createOrRetrieveCustomer,
  formatErrorResponse,
  formatSuccessResponse,
} = require('./config');

/**
 * Creates a Stripe Checkout Session for subscription
 * 
 * Expected body:
 * {
 *   userId: string,
 *   email: string,
 *   name?: string,
 *   priceId?: string,
 *   lookupKey?: string, // alternative to priceId
 *   billingCycle?: 'monthly' | 'yearly',
 *   successUrl?: string,
 *   cancelUrl?: string
 * }
 */
module.exports = async (req, res) => {
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).json({ message: 'OK' });
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      userId,
      email,
      name,
      priceId,
      lookupKey,
      billingCycle = 'monthly',
      successUrl,
      cancelUrl,
    } = req.body;

    // Validate required fields
    if (!userId || !email) {
      return res.status(400).json({
        error: 'Missing required fields: userId, email'
      });
    }

    // Determine price ID
    let finalPriceId = priceId;
    if (!finalPriceId && lookupKey) {
      const price = await getPriceByLookupKey(lookupKey);
      finalPriceId = price.id;
    } else if (!finalPriceId) {
      // Use default price based on billing cycle
      const defaultLookupKey = billingCycle === 'yearly' ? 'pro_yearly' : 'pro_monthly';
      const price = await getPriceByLookupKey(defaultLookupKey);
      finalPriceId = price.id;
    }

    // Create or retrieve customer
    const customer = await createOrRetrieveCustomer(userId, email, name);

    // Set up URLs
    const baseUrl = config.frontendUrl;
    const finalSuccessUrl = successUrl || `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}`;
    const finalCancelUrl = cancelUrl || `${baseUrl}/subscription/cancel`;

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: finalPriceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: finalSuccessUrl,
      cancel_url: finalCancelUrl,
      
      // Subscription configuration
      subscription_data: {
        trial_period_days: config.trialPeriodDays,
        metadata: {
          userId: userId,
        },
      },
      
      // Customer portal configuration
      customer_update: {
        address: 'auto',
        name: 'auto',
        shipping: 'auto',
      },
      
      // Metadata
      metadata: {
        userId: userId,
        billingCycle: billingCycle,
      },
      
      // Allow promotion codes
      allow_promotion_codes: true,
      
      // Billing address collection
      billing_address_collection: 'auto',
      
      // Automatic tax calculation (if enabled in Stripe)
      automatic_tax: {
        enabled: true,
      },
    });

    return res.status(200).json({
      sessionId: session.id,
      url: session.url,
      customerId: customer.id,
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    
    // Handle specific Stripe errors
    if (error.type === 'StripeCardError') {
      return res.status(400).json({
        error: 'Your card was declined. Please try a different payment method.'
      });
    }
    
    if (error.type === 'StripeRateLimitError') {
      return res.status(429).json({
        error: 'Too many requests. Please try again later.'
      });
    }
    
    if (error.type === 'StripeInvalidRequestError') {
      return res.status(400).json({
        error: 'Invalid request. Please check your payment details.'
      });
    }
    
    if (error.type === 'StripeAPIError') {
      return res.status(500).json({
        error: 'Payment processing temporarily unavailable. Please try again later.'
      });
    }
    
    if (error.type === 'StripeConnectionError') {
      return res.status(500).json({
        error: 'Network error. Please check your connection and try again.'
      });
    }
    
    return res.status(500).json({
      error: 'An unexpected error occurred. Please try again.'
    });
  }
};
