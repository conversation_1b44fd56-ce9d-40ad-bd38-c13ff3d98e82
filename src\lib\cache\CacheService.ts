import { injectable } from 'tsyringe'
import { createClient } from 'redis'
import { logger } from '@/lib/logger'

@injectable()
export class CacheService {
  private client: ReturnType<typeof createClient>
  private isConnected = false

  constructor() {
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    })
    
    this.client.on('error', (err) => {
      logger.error('Redis error:', err)
      this.isConnected = false
    })
  }

  async connect(): Promise<void> {
    if (this.isConnected) return
    await this.client.connect()
    this.isConnected = true
  }

  async get(key: string): Promise<string | null> {
    if (!this.isConnected) return null
    const value = await this.client.get(key)
    return value?.toString() ?? null
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    if (!this.isConnected) return
    const options = ttl ? { EX: ttl } : undefined
    await this.client.set(key, value, options)
  }

  async del(key: string): Promise<void> {
    if (!this.isConnected) return
    await this.client.del(key)
  }

  async disconnect(): Promise<void> {
    if (!this.isConnected) return
    await this.client.disconnect()
    this.isConnected = false
  }
}