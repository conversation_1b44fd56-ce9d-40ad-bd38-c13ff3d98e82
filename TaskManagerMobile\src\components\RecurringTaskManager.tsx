import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Switch,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RecurringTaskService, RecurringPattern } from '../services/recurringTaskService';
import { Task } from '../types/task';

interface RecurringTaskManagerProps {
  task: Task;
  visible: boolean;
  onClose: () => void;
  onSave: (recurringTaskId: string) => void;
}

export default function RecurringTaskManager({
  task,
  visible,
  onClose,
  onSave,
}: RecurringTaskManagerProps) {
  const [isRecurring, setIsRecurring] = useState(false);
  const [patternType, setPatternType] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('daily');
  const [frequency, setFrequency] = useState(1);
  const [selectedDays, setSelectedDays] = useState<number[]>([]);
  const [dayOfMonth, setDayOfMonth] = useState(1);
  const [selectedMonths, setSelectedMonths] = useState<number[]>([]);
  const [hasEndDate, setHasEndDate] = useState(false);
  const [endDate, setEndDate] = useState<string>('');

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const months = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];

  const toggleDay = (day: number) => {
    setSelectedDays(prev =>
      prev.includes(day) ? prev.filter(d => d !== day) : [...prev, day]
    );
  };

  const toggleMonth = (month: number) => {
    setSelectedMonths(prev =>
      prev.includes(month) ? prev.filter(m => m !== month) : [...prev, month]
    );
  };

  const getPattern = (): RecurringPattern => {
    switch (patternType) {
      case 'daily':
        return RecurringTaskService.createDailyPattern();
      case 'weekly':
        return RecurringTaskService.createWeeklyPattern(selectedDays);
      case 'monthly':
        return RecurringTaskService.createMonthlyPattern(dayOfMonth);
      case 'yearly':
        return RecurringTaskService.createYearlyPattern(selectedMonths);
      default:
        return RecurringTaskService.createDailyPattern();
    }
  };

  const handleSave = async () => {
    if (!isRecurring) {
      onClose();
      return;
    }

    try {
      const pattern = getPattern();
      const recurringTaskId = await RecurringTaskService.createRecurringTask(
        {
          title: task.title || '',
          description: task.description || '',
          status: task.status || 'pending',
          priority: task.priority || 'medium',
          category: task.category || '',
          date: task.date || new Date().toISOString(),
          due_date: task.due_date || task.date || new Date().toISOString(),
          user_id: task.user_id || '',
        },
        pattern,
        frequency,
        hasEndDate && endDate ? new Date(endDate) : undefined
      );
      onSave(recurringTaskId);
      onClose();
    } catch (error) {
      console.error('Error creating recurring task:', error);
    }
  };

  const renderPatternOptions = () => {
    switch (patternType) {
      case 'weekly':
        return (
          <View style={styles.patternOptions}>
            <Text style={styles.patternLabel}>Select Days:</Text>
            <View style={styles.daysGrid}>
              {weekDays.map((day, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dayButton,
                    selectedDays.includes(index) && styles.selectedDay,
                  ]}
                  onPress={() => toggleDay(index)}
                >
                  <Text
                    style={[
                      styles.dayText,
                      selectedDays.includes(index) && styles.selectedDayText,
                    ]}
                  >
                    {day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 'monthly':
        return (
          <View style={styles.patternOptions}>
            <Text style={styles.patternLabel}>Day of Month:</Text>
            <View style={styles.numberGrid}>
              {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                <TouchableOpacity
                  key={day}
                  style={[
                    styles.numberButton,
                    dayOfMonth === day && styles.selectedNumber,
                  ]}
                  onPress={() => setDayOfMonth(day)}
                >
                  <Text
                    style={[
                      styles.numberText,
                      dayOfMonth === day && styles.selectedNumberText,
                    ]}
                  >
                    {day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 'yearly':
        return (
          <View style={styles.patternOptions}>
            <Text style={styles.patternLabel}>Select Months:</Text>
            <View style={styles.monthsGrid}>
              {months.map((month, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.monthButton,
                    selectedMonths.includes(index + 1) && styles.selectedMonth,
                  ]}
                  onPress={() => toggleMonth(index + 1)}
                >
                  <Text
                    style={[
                      styles.monthText,
                      selectedMonths.includes(index + 1) && styles.selectedMonthText,
                    ]}
                  >
                    {month}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'No date selected';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Recurring Task Setup</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#1f2937" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* Enable Recurring */}
            <View style={styles.section}>
              <View style={styles.switchRow}>
                <Text style={styles.sectionTitle}>Enable Recurring</Text>
                <Switch
                  value={isRecurring}
                  onValueChange={setIsRecurring}
                  trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
                />
              </View>
            </View>

            {isRecurring && (
              <>
                {/* Pattern Type */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Repeat Pattern</Text>
                  <View style={styles.patternButtons}>
                    {['daily', 'weekly', 'monthly', 'yearly'].map(type => (
                      <TouchableOpacity
                        key={type}
                        style={[
                          styles.patternButton,
                          patternType === type && styles.selectedPattern,
                        ]}
                        onPress={() => setPatternType(type as any)}
                      >
                        <Text
                          style={[
                            styles.patternButtonText,
                            patternType === type && styles.selectedPatternText,
                          ]}
                        >
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Frequency */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Frequency</Text>
                  <View style={styles.frequencyContainer}>
                    <TouchableOpacity
                      style={styles.frequencyButton}
                      onPress={() => setFrequency(Math.max(1, frequency - 1))}
                    >
                      <Ionicons name="remove" size={20} color="#6b7280" />
                    </TouchableOpacity>
                    <Text style={styles.frequencyText}>{frequency}</Text>
                    <TouchableOpacity
                      style={styles.frequencyButton}
                      onPress={() => setFrequency(frequency + 1)}
                    >
                      <Ionicons name="add" size={20} color="#6b7280" />
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Pattern Options */}
                {renderPatternOptions()}

                {/* End Date */}
                <View style={styles.section}>
                  <View style={styles.switchRow}>
                    <Text style={styles.sectionTitle}>End Date</Text>
                    <Switch
                      value={hasEndDate}
                      onValueChange={setHasEndDate}
                      trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
                    />
                  </View>
                  {hasEndDate && (
                    <TextInput
                      style={styles.dateInput}
                      placeholder="YYYY-MM-DD"
                      value={endDate}
                      onChangeText={setEndDate}
                    />
                  )}
                </View>

                {/* Summary */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Summary</Text>
                  <View style={styles.summary}>
                    <Text style={styles.summaryText}>
                      This task will repeat {frequency} time(s) {patternType}
                      {patternType === 'weekly' && selectedDays.length > 0
                        ? ` on ${selectedDays.map(d => weekDays[d]).join(', ')}`
                        : ''}
                      {patternType === 'monthly' && ` on day ${dayOfMonth}`}
                      {patternType === 'yearly' && selectedMonths.length > 0
                        ? ` in ${selectedMonths.map(m => months[m - 1]).join(', ')}`
                        : ''}
                      {hasEndDate && endDate ? ` until ${formatDate(endDate)}` : ''}
                    </Text>
                  </View>
                </View>
              </>
            )}
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 20,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  patternButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  patternButton: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    alignItems: 'center',
  },
  selectedPattern: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  patternButtonText: {
    fontSize: 14,
    color: '#6b7280',
  },
  selectedPatternText: {
    color: '#ffffff',
  },
  frequencyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 20,
  },
  frequencyButton: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 20,
  },
  frequencyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    minWidth: 40,
    textAlign: 'center',
  },
  patternOptions: {
    marginTop: 12,
  },
  patternLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  dayButton: {
    width: 40,
    height: 40,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedDay: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  dayText: {
    fontSize: 12,
    color: '#6b7280',
  },
  selectedDayText: {
    color: '#ffffff',
  },
  numberGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  numberButton: {
    width: 35,
    height: 35,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedNumber: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  numberText: {
    fontSize: 12,
    color: '#6b7280',
  },
  selectedNumberText: {
    color: '#ffffff',
  },
  monthsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  monthButton: {
    width: 50,
    height: 35,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedMonth: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  monthText: {
    fontSize: 11,
    color: '#6b7280',
  },
  selectedMonthText: {
    color: '#ffffff',
  },
  dateInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    marginTop: 8,
  },
  summary: {
    padding: 12,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
  },
  summaryText: {
    fontSize: 14,
    color: '#1f2937',
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    color: '#6b7280',
  },
  saveButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 14,
    color: '#ffffff',
    fontWeight: '600',
  },
});