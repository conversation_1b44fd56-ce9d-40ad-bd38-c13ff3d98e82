import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Text,
} from 'react-native';
import { YearlyCalendar } from '../components/YearlyCalendar';
import { CalendarToggle } from '../components/CalendarToggle';
import { TaskList } from '../components/TaskList';
import { CreateTaskModal } from '../components/CreateTaskModal';
import { useTaskStore } from '../stores/taskStore';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import ExportModal from '../components/ExportModal';

export default function HomeScreen() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [showCreateTask, setShowCreateTask] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [viewMode, setViewMode] = useState<'yearly' | 'monthly'>('yearly');
  
  const { tasks, tasksByDate, loadTasks } = useTaskStore();

  useEffect(() => {
    loadTasks();
  }, []);

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  const handleYearChange = (year: number) => {
    setCurrentYear(year);
  };

  const handleCreateTask = () => {
    setShowCreateTask(true);
  };

  const getTodayTasks = () => {
    const todayKey = format(selectedDate, 'yyyy-MM-dd');
    return tasksByDate[todayKey] || [];
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Task Calendar</Text>
            <Text style={styles.headerSubtitle}>
              {format(selectedDate, 'EEEE, MMMM d, yyyy')}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.exportButton}
            onPress={() => setShowExportModal(true)}
          >
            <Ionicons name="download-outline" size={24} color="#3b82f6" />
          </TouchableOpacity>
        </View>

        {/* Calendar Toggle */}
        <CalendarToggle
          viewMode={viewMode}
          onViewModeChange={setViewMode}
        />

        {/* Calendar */}
        <View style={styles.calendarContainer}>
          <YearlyCalendar
            year={currentYear}
            tasksByDate={tasksByDate}
            selectedDate={selectedDate}
            onDateSelect={handleDateSelect}
            onYearChange={handleYearChange}
            onCreateTask={handleCreateTask}
          />
        </View>

        {/* Today's Tasks */}
        <View style={styles.tasksContainer}>
          <View style={styles.tasksHeader}>
            <Text style={styles.tasksTitle}>
              Tasks for {format(selectedDate, 'MMM d')}
            </Text>
            <Text style={styles.tasksCount}>
              {getTodayTasks().length} task{getTodayTasks().length !== 1 ? 's' : ''}
            </Text>
          </View>
          
          <TaskList
            selectedDate={selectedDate}
            tasks={tasks}
            onCreateTask={handleCreateTask}
          />
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={handleCreateTask}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>

      <CreateTaskModal
        visible={showCreateTask}
        onClose={() => setShowCreateTask(false)}
        selectedDate={selectedDate}
      />

      <ExportModal
        visible={showExportModal}
        onClose={() => setShowExportModal(false)}
        tasks={tasks}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100,
  },
  header: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  exportButton: {
    padding: 8,
  },
  calendarContainer: {
    backgroundColor: '#ffffff',
    marginVertical: 8,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tasksContainer: {
    paddingHorizontal: 16,
    marginTop: 8,
  },
  tasksHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  tasksTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  tasksCount: {
    fontSize: 14,
    color: '#6b7280',
  },
  fab: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
