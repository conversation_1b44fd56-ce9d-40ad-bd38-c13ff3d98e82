-- Task Assignment System Migration
-- Creates tables for assignees, task assignments, invitations, and assignee groups

-- Create assignee groups table
CREATE TABLE IF NOT EXISTS assignee_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  owner_id TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create assignees table (references existing profiles)
CREATE TABLE IF NOT EXISTS assignees (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  added_by TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  group_id UUID REFERENCES assignee_groups(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
  permissions JSONB DEFAULT '{"can_view_tasks": true, "can_comment": true}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, added_by)
);

-- Create invitations table
CREATE TABLE IF NOT EXISTS assignee_invitations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  invited_by TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  invited_email TEXT NOT NULL,
  invited_phone TEXT,
  invitation_type TEXT NOT NULL CHECK (invitation_type IN ('email', 'sms', 'qr', 'bluetooth', 'in_app')),
  invitation_code TEXT UNIQUE NOT NULL,
  custom_message TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  accepted_at TIMESTAMP WITH TIME ZONE,
  accepted_by TEXT REFERENCES profiles(id),
  group_id UUID REFERENCES assignee_groups(id) ON DELETE SET NULL,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recreate task_assignments table with enhanced features
CREATE TABLE IF NOT EXISTS task_assignments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  assigned_to TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  assigned_by TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (
    status IN ('pending', 'accepted', 'declined', 'in_progress', 'completed', 'cancelled')
  ),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  assignment_comments TEXT,
  response_comments TEXT,
  assignee_notes TEXT,
  due_date TIMESTAMP WITH TIME ZONE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  responded_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  estimated_hours DECIMAL(5,2),
  actual_hours DECIMAL(5,2),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(task_id, assigned_to)
);

-- Create assignment notifications table
CREATE TABLE IF NOT EXISTS assignment_notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  assignment_id UUID NOT NULL REFERENCES task_assignments(id) ON DELETE CASCADE,
  recipient_id TEXT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL CHECK (
    notification_type IN ('assignment_created', 'assignment_accepted', 'assignment_declined', 
                         'assignment_completed', 'assignment_updated', 'assignment_reminder')
  ),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assignee_groups_owner_id ON assignee_groups(owner_id);
CREATE INDEX IF NOT EXISTS idx_assignees_user_id ON assignees(user_id);
CREATE INDEX IF NOT EXISTS idx_assignees_added_by ON assignees(added_by);
CREATE INDEX IF NOT EXISTS idx_assignees_group_id ON assignees(group_id);
CREATE INDEX IF NOT EXISTS idx_assignee_invitations_invited_by ON assignee_invitations(invited_by);
CREATE INDEX IF NOT EXISTS idx_assignee_invitations_invited_email ON assignee_invitations(invited_email);
CREATE INDEX IF NOT EXISTS idx_assignee_invitations_status ON assignee_invitations(status);
CREATE INDEX IF NOT EXISTS idx_assignee_invitations_code ON assignee_invitations(invitation_code);
CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON task_assignments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_assigned_to ON task_assignments(assigned_to);
CREATE INDEX IF NOT EXISTS idx_task_assignments_assigned_by ON task_assignments(assigned_by);
CREATE INDEX IF NOT EXISTS idx_task_assignments_status ON task_assignments(status);
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_assignment_id ON assignment_notifications(assignment_id);
CREATE INDEX IF NOT EXISTS idx_assignment_notifications_recipient_id ON assignment_notifications(recipient_id);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_assignee_groups_updated_at
  BEFORE UPDATE ON assignee_groups
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assignees_updated_at
  BEFORE UPDATE ON assignees
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assignee_invitations_updated_at
  BEFORE UPDATE ON assignee_invitations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_assignments_updated_at
  BEFORE UPDATE ON task_assignments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle assignment notifications
CREATE OR REPLACE FUNCTION handle_assignment_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Create notification for assignee when task is assigned
  IF TG_OP = 'INSERT' THEN
    INSERT INTO assignment_notifications (assignment_id, recipient_id, notification_type, title, message)
    VALUES (
      NEW.id,
      NEW.assigned_to,
      'assignment_created',
      'New Task Assignment',
      'You have been assigned a new task: ' || (SELECT title FROM tasks WHERE id = NEW.task_id)
    );
    
    -- Create notification for assigner
    INSERT INTO assignment_notifications (assignment_id, recipient_id, notification_type, title, message)
    VALUES (
      NEW.id,
      NEW.assigned_by,
      'assignment_created',
      'Task Assignment Created',
      'Task assignment created for: ' || (SELECT title FROM tasks WHERE id = NEW.task_id)
    );
  END IF;
  
  -- Handle status updates
  IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
    -- Notify assigner about status changes
    INSERT INTO assignment_notifications (assignment_id, recipient_id, notification_type, title, message)
    VALUES (
      NEW.id,
      NEW.assigned_by,
      'assignment_updated',
      'Assignment Status Updated',
      'Assignment status changed to: ' || NEW.status || ' for task: ' || (SELECT title FROM tasks WHERE id = NEW.task_id)
    );
    
    -- Update responded_at when status changes from pending
    IF OLD.status = 'pending' AND NEW.status != 'pending' THEN
      NEW.responded_at = NOW();
    END IF;
    
    -- Update started_at when status changes to in_progress
    IF NEW.status = 'in_progress' AND OLD.status != 'in_progress' THEN
      NEW.started_at = NOW();
    END IF;
    
    -- Update completed_at when status changes to completed
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
      NEW.completed_at = NOW();
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create function to handle invitation acceptance
CREATE OR REPLACE FUNCTION handle_invitation_acceptance()
RETURNS TRIGGER AS $$
BEGIN
  -- When invitation is accepted, create assignee relationship
  IF TG_OP = 'UPDATE' AND OLD.status = 'pending' AND NEW.status = 'accepted' THEN
    INSERT INTO assignees (user_id, added_by, group_id, status)
    VALUES (NEW.accepted_by, NEW.invited_by, NEW.group_id, 'active')
    ON CONFLICT (user_id, added_by) DO UPDATE SET
      status = 'active',
      group_id = NEW.group_id,
      updated_at = NOW();
    
    NEW.accepted_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_assignment_notification
  AFTER INSERT OR UPDATE ON task_assignments
  FOR EACH ROW EXECUTE FUNCTION handle_assignment_notification();

CREATE TRIGGER trigger_invitation_acceptance
  BEFORE UPDATE ON assignee_invitations
  FOR EACH ROW EXECUTE FUNCTION handle_invitation_acceptance();

-- Enable Row Level Security
ALTER TABLE assignee_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE assignees ENABLE ROW LEVEL SECURITY;
ALTER TABLE assignee_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE assignment_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for assignee_groups
CREATE POLICY "Users can view their own groups" ON assignee_groups
  FOR SELECT USING (auth.uid()::text = owner_id);

CREATE POLICY "Users can create their own groups" ON assignee_groups
  FOR INSERT WITH CHECK (auth.uid()::text = owner_id);

CREATE POLICY "Users can update their own groups" ON assignee_groups
  FOR UPDATE USING (auth.uid()::text = owner_id);

CREATE POLICY "Users can delete their own groups" ON assignee_groups
  FOR DELETE USING (auth.uid()::text = owner_id);

-- Create RLS policies for assignees
CREATE POLICY "Users can view their assignees and assignments" ON assignees
  FOR SELECT USING (auth.uid()::text = added_by OR auth.uid()::text = user_id);

CREATE POLICY "Users can create assignees" ON assignees
  FOR INSERT WITH CHECK (auth.uid()::text = added_by);

CREATE POLICY "Users can update their assignees" ON assignees
  FOR UPDATE USING (auth.uid()::text = added_by);

CREATE POLICY "Users can delete their assignees" ON assignees
  FOR DELETE USING (auth.uid()::text = added_by);

-- Create RLS policies for assignee_invitations
CREATE POLICY "Users can view their invitations" ON assignee_invitations
  FOR SELECT USING (auth.uid()::text = invited_by OR auth.uid()::text = accepted_by);

CREATE POLICY "Users can create invitations" ON assignee_invitations
  FOR INSERT WITH CHECK (auth.uid()::text = invited_by);

CREATE POLICY "Users can update their invitations" ON assignee_invitations
  FOR UPDATE USING (auth.uid()::text = invited_by OR auth.uid()::text = accepted_by);

CREATE POLICY "Users can delete their invitations" ON assignee_invitations
  FOR DELETE USING (auth.uid()::text = invited_by);

-- Create RLS policies for task_assignments
CREATE POLICY "Users can view their assignments" ON task_assignments
  FOR SELECT USING (auth.uid()::text = assigned_by OR auth.uid()::text = assigned_to);

CREATE POLICY "Users can create assignments" ON task_assignments
  FOR INSERT WITH CHECK (auth.uid()::text = assigned_by);

CREATE POLICY "Users can update assignment status" ON task_assignments
  FOR UPDATE USING (auth.uid()::text = assigned_by OR auth.uid()::text = assigned_to);

CREATE POLICY "Users can delete their assignments" ON task_assignments
  FOR DELETE USING (auth.uid()::text = assigned_by);

-- Create RLS policies for assignment_notifications
CREATE POLICY "Users can view their assignment notifications" ON assignment_notifications
  FOR SELECT USING (auth.uid()::text = recipient_id);

CREATE POLICY "Users can update their assignment notifications" ON assignment_notifications
  FOR UPDATE USING (auth.uid()::text = recipient_id);

-- Create function to generate invitation codes
CREATE OR REPLACE FUNCTION generate_invitation_code()
RETURNS TEXT AS $$
BEGIN
  RETURN upper(substring(md5(random()::text || clock_timestamp()::text) from 1 for 8));
END;
$$ LANGUAGE plpgsql;

-- Create function to get unassigned tasks for a user
CREATE OR REPLACE FUNCTION get_unassigned_tasks(user_id_param TEXT)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  priority task_priority,
  status task_status,
  due_date DATE,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT t.id, t.title, t.description, t.priority, t.status, t.due_date, t.created_at
  FROM tasks t
  WHERE t.user_id = user_id_param
    AND t.status IN ('pending', 'on-hold')
    AND NOT EXISTS (
      SELECT 1 FROM task_assignments ta 
      WHERE ta.task_id = t.id 
        AND ta.status NOT IN ('declined', 'cancelled')
    )
  ORDER BY t.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get user's assignees
CREATE OR REPLACE FUNCTION get_user_assignees(user_id_param TEXT)
RETURNS TABLE (
  assignee_id UUID,
  user_id TEXT,
  full_name TEXT,
  email TEXT,
  avatar_url TEXT,
  group_id UUID,
  group_name TEXT,
  status TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id as assignee_id,
    a.user_id,
    p.full_name,
    p.email,
    p.avatar_url,
    a.group_id,
    ag.name as group_name,
    a.status,
    a.created_at
  FROM assignees a
  JOIN profiles p ON a.user_id = p.id
  LEFT JOIN assignee_groups ag ON a.group_id = ag.id
  WHERE a.added_by = user_id_param
    AND a.status = 'active'
  ORDER BY p.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get assigned tasks for a user
CREATE OR REPLACE FUNCTION get_assigned_tasks(user_id_param TEXT)
RETURNS TABLE (
  assignment_id UUID,
  task_id UUID,
  task_title TEXT,
  task_description TEXT,
  task_priority task_priority,
  assignment_status TEXT,
  assigned_by TEXT,
  assigner_name TEXT,
  assignment_comments TEXT,
  response_comments TEXT,
  assigned_at TIMESTAMP WITH TIME ZONE,
  due_date TIMESTAMP WITH TIME ZONE,
  progress_percentage INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ta.id as assignment_id,
    t.id as task_id,
    t.title as task_title,
    t.description as task_description,
    t.priority as task_priority,
    ta.status as assignment_status,
    ta.assigned_by,
    p.full_name as assigner_name,
    ta.assignment_comments,
    ta.response_comments,
    ta.assigned_at,
    ta.due_date,
    ta.progress_percentage
  FROM task_assignments ta
  JOIN tasks t ON ta.task_id = t.id
  JOIN profiles p ON ta.assigned_by = p.id
  WHERE ta.assigned_to = user_id_param
  ORDER BY ta.assigned_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;