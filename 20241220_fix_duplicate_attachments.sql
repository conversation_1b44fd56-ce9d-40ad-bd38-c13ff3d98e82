-- Migration to fix duplicate file attachments
-- This script removes duplicate entries and adds a unique constraint

-- Step 1: Remove duplicate entries, keeping only the oldest one for each (task_id, filename, user_id) combination
WITH duplicates AS (
  SELECT 
    id,
    ROW_NUMBER() OVER (
      PARTITION BY task_id, filename, user_id 
      ORDER BY uploaded_at ASC
    ) as row_num
  FROM file_attachments
)
DELETE FROM file_attachments 
WHERE id IN (
  SELECT id 
  FROM duplicates 
  WHERE row_num > 1
);

-- Step 2: Add unique constraint to prevent future duplicates
ALTER TABLE file_attachments 
ADD CONSTRAINT unique_task_file_user 
UNIQUE (task_id, filename, user_id);

-- Step 3: Add comment for documentation
COMMENT ON CONSTRAINT unique_task_file_user ON file_attachments IS 
'Ensures no duplicate files can be attached to the same task by the same user';