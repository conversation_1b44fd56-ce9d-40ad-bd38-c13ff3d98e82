# Frontend Environment Variables
# Copy this file to .env and fill in your actual values

# Stripe Configuration (Frontend)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# API Configuration
VITE_API_URL=http://localhost:3001

# Supabase Configuration (Frontend)
VITE_SUPABASE_URL=your_supabase_project_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# ===========================================
# API Environment Variables (for api/.env)
# ===========================================

# Stripe Configuration (Backend)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs (get these from Stripe Dashboard after creating products)
STRIPE_PRO_MONTHLY_PRICE_ID=price_your_pro_monthly_price_id
STRIPE_PRO_YEARLY_PRICE_ID=price_your_pro_yearly_price_id

# Supabase Configuration (Backend - use service role key for server operations)
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Application Configuration
FRONTEND_URL=http://localhost:5173
PORT=3001
NODE_ENV=development

# Email Service Configuration (optional - implement based on your choice)
# SENDGRID_API_KEY=your_sendgrid_api_key_here
# MAILGUN_API_KEY=your_mailgun_api_key_here
# SMTP_HOST=your_smtp_host_here
# SMTP_PORT=587
# SMTP_USER=your_smtp_username_here
# SMTP_PASS=your_smtp_password_here

# ===========================================
# Production Environment Variables
# ===========================================
# When going to production, replace all test keys with live keys:
# - pk_test_ -> pk_live_
# - sk_test_ -> sk_live_
# - whsec_test_ -> whsec_live_
# Update FRONTEND_URL to your production domain
