-- Updated Supabase Schema with Clerk-compatible text user_id types
-- This schema aligns with the provided definitions and uses text for user_id fields

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high');
CREATE TYPE task_status AS ENUM ('pending', 'completed', 'cancelled', 'on-hold');

-- Create profiles table with Clerk-compatible text user_id
CREATE TABLE profiles (
  id TEXT PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  notification_preferences JSONB DEFAULT '{"push": true, "email": true, "reminder": true}'::jsonb,
  timezone TEXT DEFAULT 'UTC'::text,
  work_hours JSONB DEFAULT '{"end": "17:00", "days": [1, 2, 3, 4, 5], "start": "09:00"}'::jsonb,
  notification_settings JSONB DEFAULT '{}'::jsonb,
  push_token TEXT,
  widget_enabled BOOLEAN DEFAULT true
);

-- Create tasks table with Clerk-compatible text user_id
CREATE TABLE tasks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id TEXT NOT NULL,
  date DATE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  priority task_priority NOT NULL DEFAULT 'medium'::task_priority,
  status task_status NOT NULL DEFAULT 'pending'::task_status,
  completed_at TIMESTAMP WITH TIME ZONE,
  due_date DATE,
  category TEXT,
  tags TEXT[],
  comments JSONB DEFAULT '[]'::jsonb,
  links TEXT[],
  attachments TEXT[],
  voice_notes TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  voice_note_url TEXT,
  voice_note_duration INTEGER,
  completion_percentage INTEGER DEFAULT 0,
  estimated_hours NUMERIC(5,2),
  actual_hours NUMERIC(5,2),
  recurring_pattern JSONB,
  reminder_settings JSONB,
  location TEXT,
  weather_dependent BOOLEAN DEFAULT false,
  recurring_task_id UUID,
  file_urls TEXT[] DEFAULT '{}'::text[]
);

-- Create file_attachments table with Clerk-compatible text user_id
CREATE TABLE file_attachments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id TEXT NOT NULL,
  task_id UUID NOT NULL,
  filename TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  storage_path TEXT NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table with Clerk-compatible text user_id
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recurring_tasks table with Clerk-compatible text user_id
CREATE TABLE recurring_tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'pending'::text,
  priority TEXT DEFAULT 'medium'::text,
  category TEXT,
  pattern JSONB NOT NULL,
  frequency INTEGER DEFAULT 1,
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE,
  next_occurrence TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);



-- Create task_analytics view
CREATE OR REPLACE VIEW task_analytics AS
SELECT
  user_id,
  count(*) as total_tasks,
  count(*) filter (
    where
      status = 'completed'::task_status
  ) as completed_tasks,
  count(*) filter (
    where
      status = 'pending'::task_status
  ) as pending_tasks,
  count(*) filter (
    where
      status = 'on-hold'::task_status
  ) as on_hold_tasks,
  count(*) filter (
    where
      status = 'cancelled'::task_status
  ) as cancelled_tasks,

  count(*) filter (
    where
      due_date < CURRENT_DATE
      and status <> 'completed'::task_status
  ) as overdue_tasks,
  count(*) filter (
    where
      priority = 'high'::task_priority
  ) as high_priority_tasks,
  avg(completion_percentage) as avg_completion_percentage,
  sum(estimated_hours) as total_estimated_hours,
  sum(actual_hours) as total_actual_hours
from
  tasks
group by
  user_id;

-- Add foreign key constraints
ALTER TABLE file_attachments ADD CONSTRAINT file_attachments_task_id_fkey 
  FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE;

ALTER TABLE notifications ADD CONSTRAINT notifications_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES profiles (id) ON DELETE CASCADE;

ALTER TABLE recurring_tasks ADD CONSTRAINT recurring_tasks_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES profiles (id) ON DELETE CASCADE;

 


ALTER TABLE tasks ADD CONSTRAINT tasks_recurring_task_id_fkey 
  FOREIGN KEY (recurring_task_id) REFERENCES recurring_tasks (id);

-- Add check constraints

ALTER TABLE tasks ADD CONSTRAINT tasks_completion_percentage_check 
  CHECK ((completion_percentage >= 0) AND (completion_percentage <= 100));



-- Create indexes for better performance
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_date ON tasks(date);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_priority ON tasks(priority);

CREATE INDEX idx_tasks_recurring_task_id ON tasks(recurring_task_id);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);

CREATE INDEX idx_file_attachments_task_id ON file_attachments(task_id);
CREATE INDEX idx_file_attachments_user_id ON file_attachments(user_id);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);

CREATE INDEX idx_recurring_tasks_user_id ON recurring_tasks(user_id);
CREATE INDEX idx_recurring_tasks_next_occurrence ON recurring_tasks(next_occurrence);



-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at 
  BEFORE UPDATE ON profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at 
  BEFORE UPDATE ON tasks 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_recurring_tasks_updated_at 
  BEFORE UPDATE ON recurring_tasks 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();





-- Row Level Security (RLS) Policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_tasks ENABLE ROW LEVEL SECURITY;


-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid()::text = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid()::text = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid()::text = id);

-- Tasks policies
CREATE POLICY "Users can view own tasks" ON tasks
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can create own tasks" ON tasks
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own tasks" ON tasks
  FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own tasks" ON tasks
  FOR DELETE USING (auth.uid()::text = user_id);

-- File attachments policies
CREATE POLICY "Users can view own file attachments" ON file_attachments
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can create own file attachments" ON file_attachments
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own file attachments" ON file_attachments
  FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own file attachments" ON file_attachments
  FOR DELETE USING (auth.uid()::text = user_id);

-- Notifications policies
CREATE POLICY "Users can view their notifications" ON notifications
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can update their notifications" ON notifications
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Recurring tasks policies
CREATE POLICY "Users can view own recurring tasks" ON recurring_tasks
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can create own recurring tasks" ON recurring_tasks
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own recurring tasks" ON recurring_tasks
  FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own recurring tasks" ON recurring_tasks
  FOR DELETE USING (auth.uid()::text = user_id);


-- Create storage bucket for file attachments
INSERT INTO storage.buckets (id, name, public) 
VALUES ('task-attachments', 'task-attachments', true) 
ON CONFLICT (id) DO NOTHING;

-- Storage policies
CREATE POLICY "Users can upload own files" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'task-attachments' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view own files" ON storage.objects
  FOR SELECT USING (bucket_id = 'task-attachments' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update own files" ON storage.objects
  FOR UPDATE USING (bucket_id = 'task-attachments' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own files" ON storage.objects
  FOR DELETE USING (bucket_id = 'task-attachments' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Create function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();