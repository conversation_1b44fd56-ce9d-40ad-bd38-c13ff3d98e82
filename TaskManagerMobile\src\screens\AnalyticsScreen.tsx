import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

type RootStackParamList = {
  AnalyticsDetail: { filterType: string; title: string };
  export: { filterType: string; title: string };
  // Add other screens as needed
};

type AnalyticsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AnalyticsDetail'>;
import { useTaskStore } from '../stores/taskStore';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isWithinInterval } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import ExportService from '../services/exportService';
import ExportModal from '../components/ExportModal';

export default function AnalyticsScreen() {
  const { tasks, loadTasks } = useTaskStore();
  const navigation = useNavigation<AnalyticsScreenNavigationProp>();
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'all'>('week');
  const [showExportModal, setShowExportModal] = useState(false);

  useEffect(() => {
    loadTasks();
  }, []);

  const getFilteredTasks = () => {
    const now = new Date();
    
    switch (timeRange) {
      case 'week':
        const weekStart = startOfWeek(now);
        const weekEnd = endOfWeek(now);
        return tasks.filter(task => 
          isWithinInterval(new Date(task.date), { start: weekStart, end: weekEnd })
        );
      case 'month':
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        return tasks.filter(task => 
          isWithinInterval(new Date(task.date), { start: monthStart, end: monthEnd })
        );
      default:
        return tasks;
    }
  };

  const filteredTasks = getFilteredTasks();

  const getTaskStats = () => {
    const total = filteredTasks.length;
    const completed = filteredTasks.filter(t => t.status === 'completed').length;
    const inProgress = filteredTasks.filter(t => t.status === 'in-progress').length;
    const pending = filteredTasks.filter(t => t.status === 'pending').length;
    const onHold = filteredTasks.filter(t => t.status === 'on-hold').length;
    const cancelled = filteredTasks.filter(t => t.status === 'cancelled').length;
    const assigned = filteredTasks.filter(t => t.assignment_status === 'assigned').length;
    const accepted = filteredTasks.filter(t => t.assignment_status === 'accepted').length;
    const declined = filteredTasks.filter(t => t.assignment_status === 'declined').length;
    const highPriority = filteredTasks.filter(t => t.priority === 'high').length;
    const mediumPriority = filteredTasks.filter(t => t.priority === 'medium').length;
    const lowPriority = filteredTasks.filter(t => t.priority === 'low').length;
    const overdue = filteredTasks.filter(t => 
      t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'completed'
    ).length;

    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

    // Category breakdown
    const categories = filteredTasks.reduce((acc, task) => {
      const cat = task.category || 'General';
      acc[cat] = (acc[cat] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      completed,
      inProgress,
      pending,
      onHold,
      cancelled,
      assigned,
      accepted,
      declined,
      highPriority,
      mediumPriority,
      lowPriority,
      overdue,
      completionRate,
      categories,
    };
  };

  const stats = getTaskStats();

  const StatCard = ({ title, value, color, subtitle, onPress }: any) => (
    <TouchableOpacity
      style={[styles.statCard, { borderLeftColor: color }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <Text style={styles.statTitle}>{title}</Text>
      <Text style={[styles.statValue, { color }]}>{value}</Text>
      {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
    </TouchableOpacity>
  );

  const handleCardPress = (filterType: string, title: string) => {
    if (filterType === 'export') {
      setShowExportModal(true);
    } else {
      navigation.navigate('AnalyticsDetail', {
        filterType,
        title,
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Analytics</Text>
        <View style={styles.timeRangeContainer}>
          {(['week', 'month', 'all'] as const).map((range) => (
            <TouchableOpacity
              key={range}
              style={[
                styles.timeRangeButton,
                timeRange === range && styles.activeTimeRange
              ]}
              onPress={() => setTimeRange(range)}
            >
              <Text
                style={[
                  styles.timeRangeText,
                  timeRange === range && styles.activeTimeRangeText
                ]}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Overview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="Total Tasks"
              value={stats.total}
              color="#3b82f6"
              onPress={() => handleCardPress('all', 'All Tasks')}
            />
            <StatCard
              title="Completion Rate"
              value={`${stats.completionRate}%`}
              color="#10b981"
            />
          </View>
        </View>

        {/* Task Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Task Status</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="Completed"
              value={stats.completed}
              color="#10b981"
              subtitle={`${stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0}%`}
              onPress={() => handleCardPress('completed', 'Completed Tasks')}
            />
            <StatCard
              title="In Progress"
              value={stats.inProgress}
              color="#3b82f6"
              subtitle={`${stats.total > 0 ? Math.round((stats.inProgress / stats.total) * 100) : 0}%`}
              onPress={() => handleCardPress('in-progress', 'In Progress Tasks')}
            />
            <StatCard
              title="Pending"
              value={stats.pending}
              color="#6b7280"
              subtitle={`${stats.total > 0 ? Math.round((stats.pending / stats.total) * 100) : 0}%`}
              onPress={() => handleCardPress('pending', 'Pending Tasks')}
            />
            <StatCard
              title="On Hold"
              value={stats.onHold}
              color="#8b5cf6"
              subtitle={`${stats.total > 0 ? Math.round((stats.onHold / stats.total) * 100) : 0}%`}
              onPress={() => handleCardPress('on-hold', 'On Hold Tasks')}
            />
            <StatCard
              title="Cancelled"
              value={stats.cancelled}
              color="#ef4444"
              subtitle={`${stats.total > 0 ? Math.round((stats.cancelled / stats.total) * 100) : 0}%`}
              onPress={() => handleCardPress('cancelled', 'Cancelled Tasks')}
            />
          </View>
        </View>

        {/* Priority Distribution */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Priority Distribution</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="High Priority"
              value={stats.highPriority}
              color="#ef4444"
              subtitle={`${stats.total > 0 ? Math.round((stats.highPriority / stats.total) * 100) : 0}%`}
              onPress={() => handleCardPress('high-priority', 'High Priority Tasks')}
            />
            <StatCard
              title="Medium Priority"
              value={stats.mediumPriority}
              color="#f59e0b"
              subtitle={`${stats.total > 0 ? Math.round((stats.mediumPriority / stats.total) * 100) : 0}%`}
            />
            <StatCard
              title="Low Priority"
              value={stats.lowPriority}
              color="#10b981"
              subtitle={`${stats.total > 0 ? Math.round((stats.lowPriority / stats.total) * 100) : 0}%`}
            />
          </View>
        </View>

        {/* Assignment Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Assignment Status</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="Assigned Tasks"
              value={stats.assigned}
              color="#3b82f6"
              subtitle="Tasks assigned to others"
              onPress={() => handleCardPress('assigned', 'Assigned Tasks')}
            />
            <StatCard
              title="Accepted"
              value={stats.accepted}
              color="#10b981"
              subtitle="Tasks accepted by assignees"
              onPress={() => handleCardPress('accepted', 'Accepted Tasks')}
            />
            <StatCard
              title="Declined"
              value={stats.declined}
              color="#ef4444"
              subtitle="Tasks declined by assignees"
              onPress={() => handleCardPress('declined', 'Declined Tasks')}
            />
          </View>
        </View>

        {/* Overdue Tasks */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overdue Tasks</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="Overdue"
              value={stats.overdue}
              color="#ef4444"
              subtitle="Tasks past due date"
              onPress={() => handleCardPress('overdue', 'Overdue Tasks')}
            />
          </View>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <View style={styles.statsGrid}>
            {Object.entries(stats.categories).map(([category, count]) => (
              <StatCard
                key={category}
                title={category}
                value={count}
                color="#8b5cf6"
                subtitle={`${stats.total > 0 ? Math.round((count / stats.total) * 100) : 0}%`}
                onPress={() => handleCardPress('category', `${category} Tasks`)}
              />
            ))}
          </View>
        </View>

        {/* Export Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Export & Share</Text>
          <View style={styles.exportButtons}>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={() => handleCardPress('export', 'Export Tasks')}
            >
              <Ionicons name="download-outline" size={20} color="#3B82F6" />
              <Text style={styles.exportButtonText}>Export Data</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={async () => {
                try {
                  const summary = await ExportService.generateTaskSummary(filteredTasks);
                  await ExportService.shareContent(summary, 'task-summary.txt');
                } catch (error) {
                  console.error('Error sharing summary:', error);
                }
              }}
            >
              <Ionicons name="share-outline" size={20} color="#3B82F6" />
              <Text style={styles.exportButtonText}>Share Summary</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      <ExportModal
        visible={showExportModal}
        onClose={() => setShowExportModal(false)}
        tasks={filteredTasks}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 4,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTimeRange: {
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  timeRangeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  activeTimeRangeText: {
    color: '#1f2937',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  statsGrid: {
    gap: 12,
  },
  statCard: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 12,
    color: '#9ca3af',
  },
  exportButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  exportButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    gap: 8,
  },
  exportButtonText: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '500',
  },
});
