export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
          subscription_tier: 'free' | 'pro'
          subscription_status: 'trial' | 'active' | 'past_due' | 'canceled' | 'unpaid'
          trial_ends_at?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          storage_used_bytes: number
        }
        Insert: {
          id: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
          subscription_tier?: 'free' | 'pro'
          subscription_status?: 'trial' | 'active' | 'past_due' | 'canceled' | 'unpaid'
          trial_ends_at?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          storage_used_bytes?: number
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
          subscription_tier?: 'free' | 'pro'
          subscription_status?: 'trial' | 'active' | 'past_due' | 'canceled' | 'unpaid'
          trial_ends_at?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          storage_used_bytes?: number
        }
      }
      tasks: {
        Row: {
          id: string
          user_id: string
          date: string
          title: string
          description: string
          priority: 'low' | 'medium' | 'high'
          status: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold'
          completed_at?: string | null
          due_date?: string | null
          category?: string | null
          tags?: string[] | null
          comments?: {
            id: string
            text: string
            timestamp: string
            edited_at?: string
          }[] | null
          links?: string[] | null
          attachments?: string[] | null
          voice_notes?: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          date: string
          title: string
          description: string
          priority: 'low' | 'medium' | 'high'
          status?: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold'
          completed_at?: string | null
          due_date?: string | null
          category?: string | null
          tags?: string[] | null
          comments?: {
            id: string
            text: string
            timestamp: string
            edited_at?: string
          }[] | null
          links?: string[] | null
          attachments?: string[] | null
          voice_notes?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          date?: string
          title?: string
          description?: string
          priority?: 'low' | 'medium' | 'high'
          status?: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold'
          completed_at?: string | null
          due_date?: string | null
          category?: string | null
          tags?: string[] | null
          comments?: {
            id: string
            text: string
            timestamp: string
            edited_at?: string
          }[] | null
          links?: string[] | null
          attachments?: string[] | null
          voice_notes?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      file_attachments: {
        Row: {
          id: string
          user_id: string
          task_id: string
          filename: string
          file_type: string
          file_size: number
          storage_path: string
          uploaded_at: string
        }
        Insert: {
          id?: string
          user_id: string
          task_id: string
          filename: string
          file_type: string
          file_size: number
          storage_path: string
          uploaded_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          task_id?: string
          filename?: string
          file_type?: string
          file_size?: number
          storage_path?: string
          uploaded_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      task_priority: 'low' | 'medium' | 'high'
      task_status: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
