import { lazy, Suspense } from 'react';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { LoadingSpinner } from '@/components/ui/loading-states';
import { cn } from '@/lib/utils';

// Lazy load components for better performance
export const LazyContributionCalendar = lazy(() => 
  import('@/components/calendar/ContributionCalendar').then(module => ({
    default: module.ContributionCalendar
  }))
);

export const LazyTaskModal = lazy(() => 
  import('@/components/calendar/TaskModal').then(module => ({
    default: module.TaskModal
  }))
);

export const LazyAnalyticsDashboard = lazy(() => 
  import('@/components/calendar/AnalyticsDashboard').then(module => ({
    default: module.AnalyticsDashboard
  }))
);

export const LazyAnalyticsModal = lazy(() => 
  import('@/components/calendar/AnalyticsModal').then(module => ({
    default: module.AnalyticsModal
  }))
);

export const LazyExportModal = lazy(() => 
  import('@/components/export/ExportModal').then(module => ({
    default: module.ExportModal
  }))
);


);

export const LazyProfileManager = lazy(() => 
  import('@/components/profile/ProfileManager').then(module => ({
    default: module.ProfileManager
  }))
);

// HOC for lazy loading with error boundary and loading state
interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
}

export const LazyWrapper: React.FC<LazyWrapperProps> = ({ 
  children, 
  fallback,
  errorFallback 
}) => {
  const defaultFallback = (
    <div className="flex items-center justify-center p-8">
      <LoadingSpinner size="lg" variant="pulse" />
    </div>
  );

  const defaultErrorFallback = (
    <div className="flex items-center justify-center p-8 text-muted-foreground">
      <span>Failed to load component</span>
    </div>
  );

  return (
    <ErrorBoundary fallback={errorFallback || defaultErrorFallback}>
      <Suspense fallback={fallback || defaultFallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
};

// Intersection Observer hook for lazy loading
import { useState, useEffect, useRef } from 'react';

interface UseIntersectionObserverOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

export const useIntersectionObserver = (
  options: UseIntersectionObserverOptions = {}
) => {
  const { threshold = 0.1, rootMargin = '0px', triggerOnce = true } = options;
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isVisible = entry.isIntersecting;
        setIsIntersecting(isVisible);

        if (isVisible && triggerOnce && !hasTriggered) {
          setHasTriggered(true);
          observer.unobserve(element);
        }
      },
      { threshold, rootMargin }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, triggerOnce, hasTriggered]);

  return { elementRef, isIntersecting, hasTriggered };
};

// Virtual list component for large datasets
import { useMemo } from 'react';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + overscan,
      items.length
    );

    return {
      startIndex: Math.max(0, startIndex - overscan),
      endIndex,
      items: items.slice(
        Math.max(0, startIndex - overscan),
        endIndex
      )
    };
  }, [scrollTop, itemHeight, containerHeight, items, overscan]);

  return (
    <div
      style={{ height: containerHeight, overflowY: 'auto' }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      {/* Spacer for items above visible area */}
      <div style={{ height: visibleItems.startIndex * itemHeight }} />
      
      {/* Visible items */}
      {visibleItems.items.map((item, index) => (
        <div key={visibleItems.startIndex + index} style={{ height: itemHeight }}>
          {renderItem(item, visibleItems.startIndex + index)}
        </div>
      ))}
      
      {/* Spacer for items below visible area */}
      <div 
        style={{ 
          height: (items.length - visibleItems.endIndex) * itemHeight 
        }} 
      />
    </div>
  );
}

// Image optimization component
interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: string;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  loading = 'lazy',
  placeholder
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
  };

  // Generate optimized Unsplash URLs if using Unsplash
  const getOptimizedUrl = (originalSrc: string, w?: number, h?: number) => {
    if (originalSrc.includes('unsplash.com')) {
      const baseUrl = originalSrc.split('?')[0];
      const params = new URLSearchParams();
      if (w) params.set('w', w.toString());
      if (h) params.set('h', h.toString());
      params.set('fit', 'crop');
      params.set('auto', 'format,compress');
      return `${baseUrl}?${params.toString()}`;
    }
    return originalSrc;
  };

  if (hasError && placeholder) {
    return (
      <div className={cn("bg-muted flex items-center justify-center", className)}>
        <span className="text-muted-foreground text-sm">Failed to load image</span>
      </div>
    );
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {!isLoaded && placeholder && (
        <div className="absolute inset-0 bg-muted animate-pulse" />
      )}
      
      <img
        src={getOptimizedUrl(src, width, height)}
        alt={alt}
        width={width}
        height={height}
        loading={loading}
        className={cn(
          "transition-opacity duration-300",
          isLoaded ? "opacity-100" : "opacity-0",
          className
        )}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  );
};

// Debounced search hook
export const useDebouncedValue = <T,>(value: T, delay: number = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Memory cache for expensive operations
class MemoryCache<T> {
  private cache = new Map<string, { value: T; timestamp: number }>();
  private maxAge: number;

  constructor(maxAge: number = 5 * 60 * 1000) { // 5 minutes default
    this.maxAge = maxAge;
  }

  set(key: string, value: T): void {
    this.cache.set(key, { value, timestamp: Date.now() });
  }

  get(key: string): T | undefined {
    const item = this.cache.get(key);
    if (!item) return undefined;

    if (Date.now() - item.timestamp > this.maxAge) {
      this.cache.delete(key);
      return undefined;
    }

    return item.value;
  }

  clear(): void {
    this.cache.clear();
  }
}

export const memoryCache = new MemoryCache();

// Performance monitoring hook
export const usePerformanceMonitor = (name: string) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (duration > 100) { // Log slow operations
        console.warn(`Performance: ${name} took ${duration.toFixed(2)}ms`);
      }
    };
  }, [name]);
};

// Bundle size analyzer (development only)
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    // This would integrate with webpack-bundle-analyzer in a real app
    console.log('Bundle analysis would appear here in development mode');
  }
};