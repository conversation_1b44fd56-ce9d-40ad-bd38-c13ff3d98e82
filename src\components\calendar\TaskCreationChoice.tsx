import React from 'react';
import { Mic, Edit3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

interface TaskCreationChoiceProps {
  isOpen: boolean;
  onClose: () => void;
  onTextTask: () => void;
  onVoiceTask: () => void;
}

export const TaskCreationChoice: React.FC<TaskCreationChoiceProps> = ({
  isOpen,
  onClose,
  onTextTask,
  onVoiceTask,
}) => {

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-semibold">
            Choose Action
          </DialogTitle>
          <DialogDescription className="text-center text-muted-foreground">
            Select how you would like to create your task
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Add Task Group */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Add Task
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {/* Voice Task - Primary Option */}
              <Button
                onClick={onVoiceTask}
                variant="outline"
                className="flex flex-col items-center gap-3 h-32 bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-150 border-blue-200 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <div className="p-3 bg-blue-600 rounded-full">
                  <Mic className="h-6 w-6 text-white" />
                </div>
                <div className="text-center">
                  <span className="text-sm font-medium text-blue-700">Voice Task</span>
                  <p className="text-xs text-blue-600 mt-1">Recommended</p>
                </div>
              </Button>

              {/* Text Task */}
              <Button
                onClick={onTextTask}
                variant="outline"
                className="flex flex-col items-center gap-3 h-32 bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-150 border-green-200 hover:border-green-300 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <div className="p-3 bg-green-600 rounded-full">
                  <Edit3 className="h-6 w-6 text-white" />
                </div>
                <div className="text-center">
                  <span className="text-sm font-medium text-green-700">Text Task</span>
                  <p className="text-xs text-green-600 mt-1">Traditional</p>
                </div>
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
