import React from 'react';
import { Calendar, Plus, Mouse<PERSON>ointer, <PERSON>, Check } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useScreenSize } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface WelcomePageProps {
  onLoadSamples: () => void;
  onStartTour: () => void;
  onSkip: () => void;
  isLoadingSamples: boolean;
  userName: string;
}

export const WelcomePage: React.FC<WelcomePageProps> = ({
  onLoadSamples,
  onStartTour,
  onSkip,
  isLoadingSamples,
  userName,
}) => {
  const { isMobile } = useScreenSize();

  return (
    <div className="min-h-screen p-4 sm:p-6 lg:p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <header className="text-center mb-8 sm:mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-full mb-6">
            <span className="text-4xl">📋</span>
          </div>
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            Welcome to TaskCalendar, {userName}! 🎉
          </h1>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto">
            Your personal task management system that helps you stay organized and productive
          </p>
        </header>

        {/* Key Actions */}
        <div className="grid gap-6 sm:gap-8 mb-8 sm:mb-12">
          <Card className="border-2 border-primary/20 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-xl">
                <MousePointer className="h-5 w-5 text-primary" />
                How to Get Started
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:gap-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <Calendar className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Click date tiles to view tasks</h3>
                    <p className="text-sm text-muted-foreground">
                      Navigate through your calendar and click on any date to see your tasks for that day
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <Plus className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Use the + button to add tasks anywhere</h3>
                    <p className="text-sm text-muted-foreground">
                      The floating action button is always available to quickly add new tasks
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <Clock className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Set priorities and due dates</h3>
                    <p className="text-sm text-muted-foreground">
                      Organize your tasks with priority levels and never miss an important deadline
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Track your progress</h3>
                    <p className="text-sm text-muted-foreground">
                      Mark tasks as complete and watch your productivity grow over time
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Calendar Placeholder */}
        <div className="mb-8 sm:mb-12">
          <Card className="border-dashed border-2 border-muted-foreground/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-muted-foreground">
                <Calendar className="h-5 w-5" />
                Your Task Calendar
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div id="calendar" className="grid grid-cols-7 gap-1 sm:gap-2 mb-4">
                {/* Calendar grid placeholder */}
                {Array.from({ length: 35 }, (_, i) => (
                  <div
                    key={i}
                    className={cn(
                      "aspect-square rounded-md border-2 border-dashed border-muted-foreground/20 flex items-center justify-center relative",
                      "hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer",
                      isMobile ? "text-xs" : "text-sm"
                    )}
                  >
                    <span className="text-muted-foreground/50">
                      {i + 1}
                    </span>
                    {/* Sample task indicators */}
                    {(i === 5 || i === 12 || i === 20) && (
                      <div className="absolute top-1 right-1 w-2 h-2 bg-primary/60 rounded-full" />
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-center">
                <Badge variant="outline" className="text-xs text-muted-foreground">
                  This is where your tasks will appear
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTAs */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <Button
            onClick={onLoadSamples}
            size="lg"
            className="w-full sm:w-auto bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
            disabled={isLoadingSamples}
          >
            {isLoadingSamples ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                Loading Samples...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Explore Sample Tasks
              </>
            )}
          </Button>
          
          <Button
            onClick={onStartTour}
            size="lg"
            variant="outline"
            className="w-full sm:w-auto border-2 border-primary/20 hover:border-primary/50 hover:bg-primary/5"
          >
            <MousePointer className="h-4 w-4 mr-2" />
            Take a Tour
          </Button>
        </div>

        {/* Skip option */}
        <div className="text-center">
          <Button
            onClick={onSkip}
            variant="ghost"
            size="sm"
            className="text-muted-foreground hover:text-primary"
          >
            Skip and start with empty calendar
          </Button>
        </div>

        {/* Features preview */}
        <div className="mt-12 grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <Card className="border-border">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-xs">📱</span>
                </div>
                <h3 className="font-semibold text-primary">Mobile Friendly</h3>
              </div>
              <p className="text-xs text-muted-foreground">
                Access your tasks anywhere, anytime on any device
              </p>
            </CardContent>
          </Card>

          <Card className="border-border">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-xs">🔄</span>
                </div>
                <h3 className="font-semibold text-primary">Real-time Sync</h3>
              </div>
              <p className="text-xs text-muted-foreground">
                Your tasks sync instantly across all your devices
              </p>
            </CardContent>
          </Card>

          <Card className="border-border sm:col-span-2 lg:col-span-1">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-xs">🎯</span>
                </div>
                <h3 className="font-semibold text-primary">Stay Organized</h3>
              </div>
              <p className="text-xs text-muted-foreground">
                Priorities, categories, and due dates keep you focused
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Floating + Button Preview - Now Functional */}
      <div className="fixed bottom-8 right-8 z-50">
        <div className="relative">
          <Button
            size="lg"
            className="h-16 w-16 rounded-full shadow-lg bg-primary hover:bg-primary/90 transition-all duration-200 hover:scale-105"
            onClick={() => {
              // Complete onboarding and go to main app with task creation
              onSkip();
            }}
          >
            <Plus className="h-7 w-7" />
          </Button>
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse">
            <span className="text-xs">👆</span>
          </div>
        </div>
        <div className="absolute bottom-20 right-0 bg-black/80 text-white text-xs px-2 py-1 rounded-md whitespace-nowrap">
          Click to create your first task!
        </div>
      </div>
    </div>
  );
};
