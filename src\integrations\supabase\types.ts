export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      file_attachments: {
        Row: {
          file_size: number
          file_type: string
          filename: string
          id: string
          storage_path: string
          task_id: string
          uploaded_at: string | null
          user_id: string
        }
        Insert: {
          file_size: number
          file_type: string
          filename: string
          id?: string
          storage_path: string
          task_id: string
          uploaded_at?: string | null
          user_id: string
        }
        Update: {
          file_size?: number
          file_type?: string
          filename?: string
          id?: string
          storage_path?: string
          task_id?: string
          uploaded_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "file_attachments_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      task_assignments: {
        Row: {
          id: string
          task_id: string
          assigned_to: string
          assigned_by: string
          status: string
          assigned_at: string | null
          completed_at: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          task_id: string
          assigned_to: string
          assigned_by: string
          status?: string
          assigned_at?: string | null
          completed_at?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          task_id?: string
          assigned_to?: string
          assigned_by?: string
          status?: string
          assigned_at?: string | null
          completed_at?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "task_assignments_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_assignments_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_assignments_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
 
    }notifications: {
        Row: {
          created_at: string | null
          data: Json | null
          id: string
          message: string
          read: boolean | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          id?: string
          message: string
          read?: boolean | null
          title: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          id?: string
          message?: string
          read?: boolean | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {

          avatar_url: string | null
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          notification_preferences: Json | null
          notification_settings: Json | null
          push_token: string | null
          timezone: string | null
          updated_at: string | null
          widget_enabled: boolean | null
          work_hours: Json | null
        }
        Insert: {

          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          notification_preferences?: Json | null
          notification_settings?: Json | null
          push_token?: string | null
          timezone?: string | null
          updated_at?: string | null
          widget_enabled?: boolean | null
          work_hours?: Json | null
        }
        Update: {

          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          notification_preferences?: Json | null
          notification_settings?: Json | null
          push_token?: string | null
          timezone?: string | null
          updated_at?: string | null
          widget_enabled?: boolean | null
          work_hours?: Json | null
        }
        Relationships: []
      }
      recurring_tasks: {
        Row: {
          category: string | null
          created_at: string | null
          description: string | null
          end_date: string | null
          frequency: number | null
          id: string
          next_occurrence: string | null
          pattern: Json
          priority: string | null
          start_date: string | null
          status: string | null
          title: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          frequency?: number | null
          id?: string
          next_occurrence?: string | null
          pattern: Json
          priority?: string | null
          start_date?: string | null
          status?: string | null
          title: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          frequency?: number | null
          id?: string
          next_occurrence?: string | null
          pattern?: Json
          priority?: string | null
          start_date?: string | null
          status?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recurring_tasks_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }


      tasks: {
        Row: {
          actual_hours: number | null

          attachments: string[] | null
          category: string | null
          comments: Json | null
          completed_at: string | null
          completion_percentage: number | null
          created_at: string | null
          date: string
          description: string
          due_date: string | null
          estimated_hours: number | null
          file_urls: string[] | null
          id: string
          links: string[] | null
          location: string | null
          priority: Database["public"]["Enums"]["task_priority"]
          recurring_pattern: Json | null
          recurring_task_id: string | null
          reminder_settings: Json | null
          status: Database["public"]["Enums"]["task_status"]
          tags: string[] | null
          title: string
          updated_at: string | null
          user_id: string
          voice_note_duration: number | null
          voice_note_url: string | null
          voice_notes: string[] | null
          weather_dependent: boolean | null
        }
        Insert: {
          actual_hours?: number | null

          attachments?: string[] | null
          category?: string | null
          comments?: Json | null
          completed_at?: string | null
          completion_percentage?: number | null
          created_at?: string | null
          date: string
          description: string
          due_date?: string | null
          estimated_hours?: number | null
          file_urls?: string[] | null
          id?: string
          links?: string[] | null
          location?: string | null
          priority?: Database["public"]["Enums"]["task_priority"]
          recurring_pattern?: Json | null
          recurring_task_id?: string | null
          reminder_settings?: Json | null
          status?: Database["public"]["Enums"]["task_status"]
          tags?: string[] | null
          title: string
          updated_at?: string | null
          user_id: string
          voice_note_duration?: number | null
          voice_note_url?: string | null
          voice_notes?: string[] | null
          weather_dependent?: boolean | null
        }
        Update: {
          actual_hours?: number | null

          attachments?: string[] | null
          category?: string | null
          comments?: Json | null
          completed_at?: string | null
          completion_percentage?: number | null
          created_at?: string | null
          date?: string
          description?: string
          due_date?: string | null
          estimated_hours?: number | null
          file_urls?: string[] | null
          id?: string
          links?: string[] | null
          location?: string | null
          priority?: Database["public"]["Enums"]["task_priority"]
          recurring_pattern?: Json | null
          recurring_task_id?: string | null
          reminder_settings?: Json | null
          status?: Database["public"]["Enums"]["task_status"]
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          user_id?: string
          voice_note_duration?: number | null
          voice_note_url?: string | null
          voice_notes?: string[] | null
          weather_dependent?: boolean | null
        }
        Relationships: [

          {
            foreignKeyName: "tasks_recurring_task_id_fkey"
            columns: ["recurring_task_id"]
            isOneToOne: false
            referencedRelation: "recurring_tasks"
            referencedColumns: ["id"]
          },
        ]
      }



    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      send_relationship_request: {
        Args: {
          requester_id: string
          addressee_id: string
          relationship_type: string
        }
        Returns: string
      }
      respond_to_relationship_request: {
        Args: {
          relationship_id: string
          response: string
        }
        Returns: boolean
      }
    }
    Enums: {
      task_priority: "low" | "medium" | "high"
      task_status:
        | "pending"
        | "completed"
        | "cancelled"
        | "on-hold"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      task_priority: ["low", "medium", "high"],
      task_status: [
        "pending",
        "completed",
        "cancelled",
        "on-hold",
      ],
    },
  },
} as const
