import React from 'react';
import { X } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-mobile';

interface PremiumModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
  className?: string;
}

export const PremiumModal: React.FC<PremiumModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = 'md',
  showCloseButton = true,
  className
}) => {
  const { isMobile } = useScreenSize();

  const getSizeClasses = () => {
    if (isMobile) {
      return "w-full h-full max-w-none max-h-none rounded-none";
    }

    switch (size) {
      case 'sm':
        return "max-w-md";
      case 'md':
        return "max-w-lg";
      case 'lg':
        return "max-w-2xl";
      case 'xl':
        return "max-w-4xl";
      case 'full':
        return "max-w-[95vw] max-h-[95vh]";
      default:
        return "max-w-lg";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className={cn(
          "modal-premium border-0 p-0 gap-0 mobile-optimized",
          getSizeClasses(),
          isMobile && "safe-area-padding",
          className
        )}
      >
        {/* Header */}
        <DialogHeader className="p-6 pb-4 border-b border-border/50">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-responsive-xl font-bold text-premium mb-1">
                {title}
              </DialogTitle>
              {description && (
                <DialogDescription className="text-responsive-base text-muted-foreground">
                  {description}
                </DialogDescription>
              )}
            </div>
            
            {showCloseButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="focus-premium rounded-full ml-4 -mt-1"
              >
                <X className="w-5 h-5" />
                <span className="sr-only">Close</span>
              </Button>
            )}
          </div>
        </DialogHeader>

        {/* Content */}
        <div className={cn(
          "flex-1 overflow-y-auto",
          isMobile ? "p-4" : "p-6"
        )}>
          {children}
        </div>
      </DialogContent>
    </Dialog>
  );
};

interface PremiumModalFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const PremiumModalFooter: React.FC<PremiumModalFooterProps> = ({ 
  children, 
  className 
}) => {
  const { isMobile } = useScreenSize();
  
  return (
    <div className={cn(
      "border-t border-border/50 bg-muted/30",
      isMobile ? "p-4" : "p-6",
      className
    )}>
      <div className={cn(
        "flex gap-3",
        isMobile ? "flex-col-reverse" : "flex-row justify-end"
      )}>
        {children}
      </div>
    </div>
  );
};

// Loading variant
interface PremiumLoadingModalProps {
  isOpen: boolean;
  title: string;
  description?: string;
}

export const PremiumLoadingModal: React.FC<PremiumLoadingModalProps> = ({
  isOpen,
  title,
  description
}) => {
  return (
    <PremiumModal
      isOpen={isOpen}
      onClose={() => {}} // Cannot close loading modal
      title={title}
      description={description}
      showCloseButton={false}
      size="sm"
    >
      <div className="flex flex-col items-center justify-center py-8">
        <div className="loading-shimmer w-16 h-16 rounded-full mb-4"></div>
        <div className="text-center space-y-2">
          <div className="loading-shimmer w-32 h-4 rounded mx-auto"></div>
          <div className="loading-shimmer w-24 h-3 rounded mx-auto"></div>
        </div>
      </div>
    </PremiumModal>
  );
};

// Confirmation variant
interface PremiumConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  variant?: 'default' | 'destructive';
}

export const PremiumConfirmModal: React.FC<PremiumConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  variant = "default"
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <PremiumModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      description={description}
      size="sm"
    >
      <div className="pt-4">
        <PremiumModalFooter>
          <Button variant="outline" onClick={onClose} className="focus-premium">
            {cancelLabel}
          </Button>
          <Button 
            onClick={handleConfirm}
            variant={variant === 'destructive' ? 'destructive' : 'default'}
            className={cn(
              "focus-premium",
              variant === 'default' && "btn-premium"
            )}
          >
            {confirmLabel}
          </Button>
        </PremiumModalFooter>
      </div>
    </PremiumModal>
  );
};