import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Check, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface QuickSignupProps {
  onComplete?: () => void;
  onSkip?: () => void;
  triggerReason?: string;
}

export const QuickSignup: React.FC<QuickSignupProps> = ({ 
  onComplete, 
  onSkip, 
  triggerReason = "You're ready to save your tasks!" 
}) => {
  const navigate = useNavigate();

  const features = [
    "Save unlimited tasks",
    "Sync across all devices", 
    "Set reminders & notifications",
    "Track your productivity",
    "Share tasks with team"
  ];

  const handleSignUp = () => {
    navigate('/auth');
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="max-w-md w-full mx-4">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="h-8 w-8 text-primary" />
          </div>
          <CardTitle className="text-2xl">
            Ready to get organized?
          </CardTitle>
          <p className="text-muted-foreground mt-2">
            {triggerReason}
          </p>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>

            <div className="space-y-3">
              <Button 
                className="w-full" 
                size="lg"
                onClick={handleSignUp}
              >
                Create Free Account
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              
              {onSkip && (
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={onSkip}
                >
                  Maybe Later
                </Button>
              )}
            </div>
          </div>
          
          <div className="text-center mt-4">
            <Badge variant="outline" className="text-xs">
              Free forever • No credit card required
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};