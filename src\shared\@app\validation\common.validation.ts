import { z } from 'zod';

export const PaginationParamsSchema = z.object({
  page: z.number().positive().optional(),
  limit: z.number().positive().max(100).optional(),
  offset: z.number().min(0).optional()
});

export const SortParamsSchema = z.object({
  field: z.string(),
  direction: z.enum(['asc', 'desc'])
});

export const SearchParamsSchema = z.object({
  query: z.string().optional(),
  filters: z.record(z.any()).optional(),
  sort: SortParamsSchema.optional(),
  pagination: PaginationParamsSchema.optional()
});

export const ApiResponseSchema = z.object({
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  success: z.boolean()
});

export const PaginatedResponseSchema = ApiResponseSchema.extend({
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  }).optional()
});

export const FileUploadResultSchema = z.object({
  id: z.string(),
  url: z.string().url(),
  path: z.string()
});

export function validatePaginationParams(data: unknown) {
  return PaginationParamsSchema.safeParse(data);
}

export function validateSortParams(data: unknown) {
  return SortParamsSchema.safeParse(data);
}

export function validateSearchParams(data: unknown) {
  return SearchParamsSchema.safeParse(data);
}

export function validateApiResponse(data: unknown) {
  return ApiResponseSchema.safeParse(data);
}

export function validateFileUploadResult(data: unknown) {
  return FileUploadResultSchema.safeParse(data);
}