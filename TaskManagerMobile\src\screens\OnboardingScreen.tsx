import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import OnboardingWelcomeScreen from './OnboardingWelcomeScreen';
import OnboardingTourScreen from './OnboardingTourScreen';
import { OnboardingService } from '../services/onboardingService';
import { supabase } from '../lib/supabase';
import { useUnifiedAuth } from '../hooks/useUnifiedAuth';

type RootStackParamList = {
  MainTabs: undefined;
  Onboarding: undefined;
};

type OnboardingScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

type OnboardingStep = 'welcome' | 'tour' | 'complete';

export const OnboardingScreen: React.FC = () => {
  const { userId } = useUnifiedAuth();
  const navigation = useNavigation<OnboardingScreenNavigationProp>();
  
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome');
  const [isLoadingSamples, setIsLoadingSamples] = useState(false);
  const [showTour, setShowTour] = useState(false);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);

  // Check if user has completed onboarding
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!userId) return;
      
      try {
        const hasCompleted = await OnboardingService.hasCompletedOnboarding(userId);
        
        if (hasCompleted) {
          navigation.replace('MainTabs');
          return;
        }
        
        // If signed in but no onboarding completed, show welcome
        await OnboardingService.updateProgress(userId, 'welcome');
      } catch (error) {
        console.error('Error checking onboarding status:', error);
      } finally {
        setIsCheckingOnboarding(false);
      }
    };

    checkOnboardingStatus();
  }, [userId, navigation]);

  const handleLoadSampleTasks = async () => {
    if (!userId) return;
    
    setIsLoadingSamples(true);
    try {
      const sampleTasks = OnboardingService.generateSampleTasks(userId);

      for (const task of sampleTasks) {
        await supabase.from('tasks').insert(task);
      }

      await OnboardingService.markSamplesLoaded(userId);
      await OnboardingService.markOnboardingComplete(userId);
      
      navigation.replace('MainTabs');
      
    } catch (error) {
      console.error('Error loading sample tasks:', error);
    } finally {
      setIsLoadingSamples(false);
    }
  };

  const handleStartTour = async () => {
    if (!userId) return;
    
    await OnboardingService.updateProgress(userId, 'tour');
    setShowTour(true);
  };

  const handleTourComplete = async () => {
    if (!userId) return;
    
    setShowTour(false);
    await OnboardingService.markTourCompleted(userId);
    await OnboardingService.markOnboardingComplete(userId);
    
    navigation.replace('MainTabs');
  };

  const handleSkipOnboarding = async () => {
    if (!userId) return;
    
    await OnboardingService.markOnboardingComplete(userId);
    navigation.replace('MainTabs');
  };

  if (isCheckingOnboarding) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  return (
    <>
      {currentStep === 'welcome' && (
        <OnboardingWelcomeScreen
          onLoadSamples={handleLoadSampleTasks}
          onStartTour={handleStartTour}
          onSkip={handleSkipOnboarding}
          isLoadingSamples={isLoadingSamples}
          userName="there"
        />
      )}
      
      {showTour && (
        <OnboardingTourScreen
          onComplete={handleTourComplete}
          onSkip={handleSkipOnboarding}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
});

export default OnboardingScreen;