import { supabase } from '../lib/supabase';
import { Task } from '../types/task';

interface ExportOptions {
  format: 'csv' | 'json' | 'txt';
  includeDetails: boolean;
  includeAttachments: boolean;
  includeVoiceNotes: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export class EnhancedExportService {
  static async generateReport(tasks: Task[], options: ExportOptions) {
    try {
      // Filter tasks by date range if provided
      let filteredTasks = tasks;
      if (options.dateRange) {
        filteredTasks = tasks.filter(task => {
          const taskDate = new Date(task.date);
          return taskDate >= options.dateRange!.start && taskDate <= options.dateRange!.end;
        });
      }

      // Get additional data
      const tasksWithDetails = await Promise.all(
        filteredTasks.map(async (task) => {
          let attachments = 0;
          let voiceNotes = 0;

          if (options.includeAttachments) {
            const { data: attachmentsData } = await supabase
              .from('task_attachments')
              .select('id')
              .eq('task_id', task.id);
            attachments = attachmentsData?.length || 0;
          }

          if (options.includeVoiceNotes) {
            const { data: voiceNotesData } = await supabase
              .from('voice_notes')
              .select('id')
              .eq('task_id', task.id);
            voiceNotes = voiceNotesData?.length || 0;
          }

          return {
            ...task,
            attachments,
            voiceNotes,
            status: task.status || 'pending',
            priority: task.priority || 'medium',
          };
        })
      );

      switch (options.format) {
        case 'csv':
          return this.generateCSVReport(tasksWithDetails);
        case 'json':
          return this.generateJSONReport(tasksWithDetails);
        case 'txt':
          return this.generateTaskSummary(tasksWithDetails);
        default:
          return this.generateTaskSummary(tasksWithDetails);
      }
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  private static generateCSVReport(tasks: any[]): string {
    const headers = [
      'ID',
      'Title',
      'Description',
      'Status',
      'Priority',
      'Category',
      'Date',
      'Due Date',
      'Completed At',
      'Attachments',
      'Voice Notes',
      'Created At',
      'Updated At'
    ];

    const rows = tasks.map(task => [
      task.id,
      task.title || '',
      task.description || '',
      task.status,
      task.priority,
      task.category || '',
      task.date || '',
      task.dueDate || '',
      task.completedAt || '',
      task.attachments.toString(),
      task.voiceNotes.toString(),
      task.created_at || '',
      task.updated_at || ''
    ]);

    return [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');
  }

  private static generateJSONReport(tasks: any[]): string {
    const total = tasks.length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const pending = tasks.filter(t => t.status === 'pending').length;
    const inProgress = tasks.filter(t => t.status === 'in-progress').length;
    const highPriority = tasks.filter(t => t.priority === 'high').length;
    const overdue = tasks.filter(t => 
      t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'completed'
    ).length;

    const categoryBreakdown = tasks.reduce((acc, task) => {
      const cat = task.category || 'General';
      acc[cat] = (acc[cat] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const report = {
      generatedAt: new Date().toISOString(),
      summary: {
        totalTasks: total,
        completed,
        pending,
        inProgress,
        highPriority,
        overdue,
        completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
      },
      breakdown: {
        byStatus: {
          completed,
          pending,
          inProgress,
          onHold: tasks.filter(t => t.status === 'on-hold').length,
          cancelled: tasks.filter(t => t.status === 'cancelled').length,
        },
        byPriority: {
          high: highPriority,
          medium: tasks.filter(t => t.priority === 'medium').length,
          low: tasks.filter(t => t.priority === 'low').length,
        },
        byCategory: categoryBreakdown,
      },
      tasks: tasks.map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        category: task.category,
        date: task.date,
        dueDate: task.dueDate,
        completedAt: task.completedAt,
        attachments: task.attachments,
        voiceNotes: task.voiceNotes,
        createdAt: task.created_at,
        updatedAt: task.updated_at,
      }))
    };

    return JSON.stringify(report, null, 2);
  }

  static generateTaskSummary(tasks: any[]): string {
    const total = tasks.length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const pending = tasks.filter(t => t.status === 'pending').length;
    const inProgress = tasks.filter(t => t.status === 'in-progress').length;
    const highPriority = tasks.filter(t => t.priority === 'high').length;
    const overdue = tasks.filter(t => 
      t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'completed'
    ).length;

    const categoryBreakdown = tasks.reduce((acc, task) => {
      const cat = task.category || 'General';
      acc[cat] = (acc[cat] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return `
TASK MANAGEMENT REPORT
======================
Generated: ${new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}

SUMMARY
-------
Total Tasks: ${total}
Completed: ${completed} (${total > 0 ? Math.round((completed/total)*100) : 0}%)
Pending: ${pending}
In Progress: ${inProgress}
High Priority: ${highPriority}
Overdue: ${overdue}

STATUS BREAKDOWN
----------------
Completed: ${completed}
Pending: ${pending}
In Progress: ${inProgress}
On Hold: ${tasks.filter(t => t.status === 'on-hold').length}
Cancelled: ${tasks.filter(t => t.status === 'cancelled').length}

PRIORITY BREAKDOWN
------------------
High: ${highPriority}
Medium: ${tasks.filter(t => t.priority === 'medium').length}
Low: ${tasks.filter(t => t.priority === 'low').length}

CATEGORY BREAKDOWN
------------------
${Object.entries(categoryBreakdown)
  .map(([category, count]) => `${category}: ${count}`)
  .join('\n')}

TASK DETAILS
------------
${tasks.map((task, index) => `
${index + 1}. ${task.title}
   Status: ${task.status}
   Priority: ${task.priority}
   Date: ${new Date(task.date).toLocaleDateString()}
   ${task.dueDate ? `Due: ${new Date(task.dueDate).toLocaleDateString()}` : ''}
   ${task.description ? `Description: ${task.description}` : ''}
   Attachments: ${task.attachments}
   Voice Notes: ${task.voiceNotes}
`).join('\n')}
    `.trim();
  }

  static async shareContent(content: string, fileName: string) {
    try {
      // In a real app, you would use a sharing library here
      // For now, we'll return the content as a data URL
      const dataUrl = `data:text/plain;charset=utf-8,${encodeURIComponent(content)}`;
      return dataUrl;
    } catch (error) {
      console.error('Error sharing content:', error);
      throw error;
    }
  }
}