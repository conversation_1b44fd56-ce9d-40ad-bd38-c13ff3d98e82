import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  Switch,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { unifiedNotificationService, NotificationSettings } from '../services/notificationService';

interface Props {
  userId: string;
  onBack: () => void;
}

export default function NotificationSettingsScreen({ userId, onBack }: Props) {
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, [userId]);

  const loadSettings = async () => {
    try {
      await unifiedNotificationService.initialize(userId);
      const currentSettings = unifiedNotificationService.getSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error('Failed to load notification settings:', error);
      Alert.alert('Error', 'Failed to load notification settings.');
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<NotificationSettings>) => {
    if (!settings) return;

    setSaving(true);
    try {
      await unifiedNotificationService.updateSettings(userId, newSettings);
      setSettings({ ...settings, ...newSettings });
    } catch (error) {
      console.error('Failed to update settings:', error);
      Alert.alert('Error', 'Failed to update notification settings.');
    } finally {
      setSaving(false);
    }
  };

  const handlePreferenceChange = (key: keyof NotificationSettings['preferences'], value: boolean) => {
    if (!settings) return;
    
    updateSettings({
      preferences: {
        ...settings.preferences,
        [key]: value,
      },
    });
  };

  const handleSettingChange = (key: keyof NotificationSettings, value: any) => {
    if (!settings) return;
    updateSettings({ [key]: value });
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#1f2937" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notification Settings</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!settings) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#1f2937" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notification Settings</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error loading settings</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#1f2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notification Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Master Control */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Master Control</Text>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Enable Notifications</Text>
              <Text style={styles.settingDescription}>Turn all notifications on or off</Text>
            </View>
            <Switch
              value={settings.enabled}
              onValueChange={(value) => handleSettingChange('enabled', value)}
              disabled={saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>
        </View>

        {/* Notification Channels */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Channels</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <View style={styles.settingLabelRow}>
                <Ionicons name="notifications-outline" size={16} color="#6b7280" />
                <Text style={styles.settingLabel}>In-App Notifications</Text>
              </View>
              <Text style={styles.settingDescription}>Notifications shown within the app</Text>
            </View>
            <Switch
              value={settings.preferences.inApp}
              onValueChange={(value) => handlePreferenceChange('inApp', value)}
              disabled={!settings.enabled || saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <View style={styles.settingLabelRow}>
                <Ionicons name="phone-portrait-outline" size={16} color="#6b7280" />
                <Text style={styles.settingLabel}>Push Notifications</Text>
              </View>
              <Text style={styles.settingDescription}>Mobile push notifications</Text>
            </View>
            <Switch
              value={settings.preferences.push}
              onValueChange={(value) => handlePreferenceChange('push', value)}
              disabled={!settings.enabled || saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <View style={styles.settingLabelRow}>
                <Ionicons name="mail-outline" size={16} color="#6b7280" />
                <Text style={styles.settingLabel}>Email Notifications</Text>
              </View>
              <Text style={styles.settingDescription}>Notifications sent to your email</Text>
            </View>
            <Switch
              value={settings.preferences.email}
              onValueChange={(value) => handlePreferenceChange('email', value)}
              disabled={!settings.enabled || saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>
        </View>

        {/* Task Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Task Notifications</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Task Reminders</Text>
              <Text style={styles.settingDescription}>Get reminded before tasks are due</Text>
            </View>
            <Switch
              value={settings.taskReminders}
              onValueChange={(value) => handleSettingChange('taskReminders', value)}
              disabled={!settings.enabled || saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Overdue Alerts</Text>
              <Text style={styles.settingDescription}>Get alerted when tasks become overdue</Text>
            </View>
            <Switch
              value={settings.overdueAlerts}
              onValueChange={(value) => handleSettingChange('overdueAlerts', value)}
              disabled={!settings.enabled || saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Assignment Notifications</Text>
              <Text style={styles.settingDescription}>Get notified about task assignments</Text>
            </View>
            <Switch
              value={settings.assignmentNotifications}
              onValueChange={(value) => handleSettingChange('assignmentNotifications', value)}
              disabled={!settings.enabled || saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Completion Notifications</Text>
              <Text style={styles.settingDescription}>Get notified when tasks are completed</Text>
            </View>
            <Switch
              value={settings.completionNotifications}
              onValueChange={(value) => handleSettingChange('completionNotifications', value)}
              disabled={!settings.enabled || saving}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>
        </View>

        {saving && (
          <View style={styles.savingIndicator}>
            <Text style={styles.savingText}>Saving changes...</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginLeft: 8,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  savingIndicator: {
    alignItems: 'center',
    padding: 16,
  },
  savingText: {
    fontSize: 14,
    color: '#6b7280',
  },
});