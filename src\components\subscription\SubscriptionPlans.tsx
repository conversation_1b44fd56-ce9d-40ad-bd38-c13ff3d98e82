import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Check, Crown, Zap, Clock } from 'lucide-react';
import { SUBSCRIPTION_PLANS, formatPrice } from '@/config/subscription';
import { useSubscriptionStore } from '@/store/subscriptionStore';
import { SubscriptionPlan } from '@/types/subscription';

interface SubscriptionPlansProps {
  onPlanSelect: (planId: string, billingCycle: 'monthly' | 'yearly') => void;
  currentPlanId?: string;
  isLoading?: boolean;
}

export const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({
  onPlanSelect,
  currentPlanId = 'free',
  isLoading = false,
}) => {
  const [isYearly, setIsYearly] = useState(false);
  const { isTrialActive, getTrialDaysRemaining } = useSubscriptionStore();

  const trialActive = isTrialActive();
  const trialDaysRemaining = getTrialDaysRemaining();

  const getPlanCards = () => {
    return Object.values(SUBSCRIPTION_PLANS).map((plan) => (
      <PlanCard
        key={plan.id}
        plan={plan}
        isYearly={isYearly}
        isCurrent={currentPlanId === plan.id}
        onSelect={() => onPlanSelect(plan.id, isYearly ? 'yearly' : 'monthly')}
        isLoading={isLoading}
        trialActive={trialActive}
        trialDaysRemaining={trialDaysRemaining}
      />
    ));
  };

  const getYearlySavings = () => {
    const proMonthly = SUBSCRIPTION_PLANS.pro.price.monthly * 12;
    const proYearly = SUBSCRIPTION_PLANS.pro.price.yearly;
    const savings = proMonthly - proYearly;
    return formatPrice(savings);
  };

  return (
    <div className="space-y-6">
      {/* Trial Banner */}
      {trialActive && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="font-medium text-yellow-800">Pro Trial Active</p>
                  <p className="text-sm text-yellow-600">
                    {trialDaysRemaining} days remaining
                  </p>
                </div>
              </div>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                Trial
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing Toggle */}
      <div className="flex items-center justify-center gap-4">
        <Label htmlFor="billing-toggle" className={!isYearly ? 'font-medium' : ''}>
          Monthly
        </Label>
        <Switch
          id="billing-toggle"
          checked={isYearly}
          onCheckedChange={setIsYearly}
        />
        <Label htmlFor="billing-toggle" className={isYearly ? 'font-medium' : ''}>
          Yearly
        </Label>
        {isYearly && (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Save {getYearlySavings()}
          </Badge>
        )}
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {getPlanCards()}
      </div>

      {/* Features Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Feature</th>
                  <th className="text-center py-2">Free</th>
                  <th className="text-center py-2">Pro</th>
                </tr>
              </thead>
              <tbody>
                <FeatureRow
                  feature="Task Management"
                  free={true}
                  pro={true}
                />
                <FeatureRow
                  feature="File Attachments"
                  free="5MB per file"
                  pro="25MB per file"
                />
                <FeatureRow
                  feature="Total Storage"
                  free="50MB"
                  pro="500MB"
                />
                <FeatureRow
                  feature="Voice Notes"
                  free={false}
                  pro={true}
                />
                <FeatureRow
                  feature="Comments per Task"
                  free="1"
                  pro="Unlimited"
                />
                <FeatureRow
                  feature="File Retention"
                  free="30 days"
                  pro="1 year"
                />
                <FeatureRow
                  feature="Notifications"
                  free="Email only"
                  pro="Email + SMS"
                />
                <FeatureRow
                  feature="Priority Support"
                  free={false}
                  pro={true}
                />
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface PlanCardProps {
  plan: SubscriptionPlan;
  isYearly: boolean;
  isCurrent: boolean;
  onSelect: () => void;
  isLoading: boolean;
  trialActive: boolean;
  trialDaysRemaining: number;
}

const PlanCard: React.FC<PlanCardProps> = ({
  plan,
  isYearly,
  isCurrent,
  onSelect,
  isLoading,
  trialActive,
  trialDaysRemaining,
}) => {
  const price = isYearly ? plan.price.yearly : plan.price.monthly;
  const monthlyPrice = isYearly ? plan.price.yearly / 12 : plan.price.monthly;
  const isFree = plan.id === 'free';
  const isPro = plan.id === 'pro';

  const getButtonText = () => {
    if (isCurrent && !trialActive) return 'Current Plan';
    if (isCurrent && trialActive) return `${trialDaysRemaining} days left`;
    if (isFree) return 'Downgrade to Free';
    if (isPro && trialActive) return 'Continue with Pro';
    return `Get ${plan.name}`;
  };

  const getButtonVariant = () => {
    if (isCurrent) return 'outline';
    if (isPro) return 'default';
    return 'outline';
  };

  return (
    <Card className={`relative ${plan.popular ? 'border-blue-200 shadow-lg' : ''}`}>
      {plan.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-blue-500 text-white">
            <Crown className="h-3 w-3 mr-1" />
            Most Popular
          </Badge>
        </div>
      )}
      
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          {isPro && <Zap className="h-5 w-5 text-yellow-500" />}
          {plan.name}
        </CardTitle>
        <div className="space-y-1">
          <div className="text-3xl font-bold">
            {isFree ? 'Free' : formatPrice(monthlyPrice)}
            {!isFree && <span className="text-sm font-normal text-muted-foreground">/month</span>}
          </div>
          {!isFree && isYearly && (
            <div className="text-sm text-muted-foreground">
              Billed {formatPrice(price)} yearly
            </div>
          )}
        </div>
        <p className="text-sm text-muted-foreground">{plan.description}</p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <ul className="space-y-2">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex items-start gap-2">
              <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </li>
          ))}
        </ul>
        
        <Button
          onClick={onSelect}
          disabled={isLoading || (isCurrent && !trialActive)}
          className="w-full"
          variant={getButtonVariant()}
        >
          {getButtonText()}
        </Button>
      </CardContent>
    </Card>
  );
};

interface FeatureRowProps {
  feature: string;
  free: boolean | string;
  pro: boolean | string;
}

const FeatureRow: React.FC<FeatureRowProps> = ({ feature, free, pro }) => {
  const renderValue = (value: boolean | string) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className="h-4 w-4 text-green-500 mx-auto" />
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    }
    return <span className="text-sm">{value}</span>;
  };

  return (
    <tr className="border-b">
      <td className="py-2">{feature}</td>
      <td className="py-2 text-center">{renderValue(free)}</td>
      <td className="py-2 text-center">{renderValue(pro)}</td>
    </tr>
  );
};
