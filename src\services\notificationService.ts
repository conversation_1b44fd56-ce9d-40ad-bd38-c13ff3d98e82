import { Task } from '@/types/task';
import { soundManager } from '@/lib/soundUtils';
import { supabase } from '../lib/supabase';
// Removed relationship-related imports

interface NotificationPreferences {
  email: boolean;
  push: boolean;
  inApp: boolean;
  sms: boolean;
}



// Removed RelationshipNotification interface

interface NotificationSettings {
  enabled: boolean;
  taskReminders: boolean;
  overdueAlerts: boolean;
  dailyDigest: boolean;
  soundEnabled: boolean;
  reminderTime: number; // minutes before due time
}

export class NotificationService {
  private static instance: NotificationService;
  private settings: NotificationSettings;
  private scheduledNotifications: Map<string, number> = new Map();
  private checkInterval: number | null = null;

  private constructor() {
    this.settings = this.loadSettings();
    this.initialize();
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private loadSettings(): NotificationSettings {
    try {
      const savedSettings = localStorage.getItem('app-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        return {
          enabled: parsed.notificationsEnabled !== false,
          taskReminders: parsed.taskReminders !== false,
          overdueAlerts: parsed.overdueAlerts !== false,
          dailyDigest: parsed.dailyDigest || false,
          soundEnabled: parsed.soundEnabled !== false,
          reminderTime: parsed.reminderTime || 15,
        };
      }
    } catch (error) {
      console.warn('Failed to load notification settings:', error);
    }
    
    return {
      enabled: true,
      taskReminders: true,
      overdueAlerts: true,
      dailyDigest: false,
      soundEnabled: true,
      reminderTime: 15,
    };
  }

  private initialize() {
    // Don't automatically request notification permission
    // This should only be done on user interaction
    
    // Start periodic check for overdue tasks
    this.startPeriodicChecks();
  }

  // Request notification permission (must be called from user interaction)
  async requestNotificationPermission(): Promise<boolean> {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      try {
        const permission = await Notification.requestPermission();
        return permission === 'granted';
      } catch (error) {
        console.warn('Failed to request notification permission:', error);
        return false;
      }
    }
    return false;
  }

  updateSettings(newSettings: Partial<NotificationSettings>) {
    this.settings = { ...this.settings, ...newSettings };
    
    if (!this.settings.enabled) {
      this.clearAllScheduledNotifications();
      this.stopPeriodicChecks();
    } else {
      this.startPeriodicChecks();
    }
  }

  // Schedule notifications for a task
  scheduleTaskNotifications(task: Task) {
    if (!this.settings.enabled || !this.settings.taskReminders) return;

    this.clearTaskNotifications(task.id);

    if (task.dueDate && task.status !== 'completed') {
      const dueTime = new Date(task.dueDate).getTime();
      const reminderTime = dueTime - (this.settings.reminderTime * 60 * 1000);
      const now = Date.now();

      // Schedule reminder notification
      if (reminderTime > now) {
        const timeoutId = window.setTimeout(() => {
          this.showTaskReminder(task);
        }, reminderTime - now);

        this.scheduledNotifications.set(`${task.id}-reminder`, timeoutId);
      }

      // Schedule due notification
      if (dueTime > now) {
        const timeoutId = window.setTimeout(() => {
          this.showTaskDue(task);
        }, dueTime - now);

        this.scheduledNotifications.set(`${task.id}-due`, timeoutId);
      }
    }
  }

  // Clear notifications for a specific task
  clearTaskNotifications(taskId: string) {
    const keys = Array.from(this.scheduledNotifications.keys())
      .filter(key => key.startsWith(taskId));
    
    keys.forEach(key => {
      const timeoutId = this.scheduledNotifications.get(key);
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.scheduledNotifications.delete(key);
      }
    });
  }

  // Clear all scheduled notifications
  clearAllScheduledNotifications() {
    this.scheduledNotifications.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    this.scheduledNotifications.clear();
  }

  // Show task reminder notification
  private showTaskReminder(task: Task) {
    const title = 'Task Reminder';
    const body = `"${task.title}" is due in ${this.settings.reminderTime} minutes`;
    
    this.showNotification(title, body, 'reminder');
  }

  // Show task due notification
  private showTaskDue(task: Task) {
    const title = 'Task Due Now';
    const body = `"${task.title}" is due now`;
    
    this.showNotification(title, body, 'alert');
  }

  // Show overdue task notification
  private showOverdueTask(task: Task) {
    const title = 'Overdue Task';
    const dueDate = new Date(task.dueDate!);
    const hoursOverdue = Math.floor((Date.now() - dueDate.getTime()) / (1000 * 60 * 60));
    const body = `"${task.title}" is ${hoursOverdue} hours overdue`;
    
    this.showNotification(title, body, 'alert');
  }

  // Generic notification display
  private showNotification(title: string, body: string, soundType: 'notification' | 'reminder' | 'alert' = 'notification') {
    if (!this.settings.enabled) return;

    // Show browser notification
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(title, {
        body,
        icon: '/favicon.svg',
        badge: '/favicon-32x32.svg',
        requireInteraction: true,
        silent: !this.settings.soundEnabled,
      });

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      // Play sound if enabled
      if (this.settings.soundEnabled) {
        soundManager.playSound(soundType);
      }
    }
  }

  // Start periodic checks for overdue tasks
  private startPeriodicChecks() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    // Check every 15 minutes
    this.checkInterval = window.setInterval(() => {
      this.checkOverdueTasks();
    }, 15 * 60 * 1000);
  }

  // Stop periodic checks
  private stopPeriodicChecks() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  // Check for overdue tasks
  private checkOverdueTasks() {
    if (!this.settings.enabled || !this.settings.overdueAlerts) return;

    // This would need to be called with current tasks from the app
    // For now, we'll create a method that can be called externally
  }

  // Method to be called from the app with current tasks
  checkTasksForOverdue(tasks: Task[]) {
    if (!this.settings.enabled || !this.settings.overdueAlerts) return;

    const now = Date.now();
    const overdueThreshold = 60 * 60 * 1000; // 1 hour

    tasks.forEach(task => {
      if (task.dueDate && task.status !== 'completed') {
        const dueTime = new Date(task.dueDate).getTime();
        const overdue = now - dueTime;

        // Show notification if task is overdue by more than threshold
        if (overdue > overdueThreshold) {
          // Only show once per day for each overdue task
          const lastShown = localStorage.getItem(`overdue-notif-${task.id}`);
          const today = new Date().toDateString();
          
          if (lastShown !== today) {
            this.showOverdueTask(task);
            localStorage.setItem(`overdue-notif-${task.id}`, today);
          }
        }
      }
    });
  }

  // Show daily digest
  showDailyDigest(tasks: Task[]) {
    if (!this.settings.enabled || !this.settings.dailyDigest) return;

    const today = new Date().toDateString();
    const todayTasks = tasks.filter(task => {
      const taskDate = new Date(task.date).toDateString();
      return taskDate === today;
    });

    const completedCount = todayTasks.filter(task => task.status === 'completed').length;
    const pendingCount = todayTasks.filter(task => task.status === 'pending').length;

    const title = 'Daily Task Summary';
    const body = `Today: ${completedCount} completed, ${pendingCount} pending`;

    this.showNotification(title, body, 'notification');
  }

  // Request notification permission
  static async requestPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission;
    }
    return 'denied';
  }

  // Get current notification permission
  static getPermission(): NotificationPermission {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      return Notification.permission;
    }
    return 'default';
  }

// Removed relationship notification methods
  
  // Real-time subscriptions
  subscribeToNotifications(user_id: string, callback: (notification: any) => void) {
    const subscription = supabase
      .channel(`notifications:${user_id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user_id}`
        },
        (payload) => {
          callback(payload.new);
          
          // Show browser notification for real-time updates
          if (this.settings.enabled) {
            this.showNotification(
              payload.new.title,
              payload.new.message,
              'notification'
            );
          }
        }
      )
      .subscribe();
    
    return () => {
      subscription.unsubscribe();
    };
  }
  

}

// Export singleton instance
export const notificationService = NotificationService.getInstance();
