import { SupabaseClient } from '@supabase/supabase-js'
import { Database } from '@/integrations/supabase/types'

export interface ProfileData {
  id: string
  email?: string
  full_name?: string | null
  avatar_url?: string | null
}

export class ProfileService {
  /**
   * Synchronize user profile with Supabase
   */
  // Method that works directly with Supabase user data
  static async syncProfile(
    supabaseUser: {
      id: string
      email: string
      user_metadata?: {
        full_name?: string
        avatar_url?: string
      }
    },
    supabaseClient: SupabaseClient<Database>
  ): Promise<ProfileData | null> {
    try {
      const email = supabaseUser.email
      const fullName = supabaseUser.user_metadata?.full_name || null
      const avatarUrl = supabaseUser.user_metadata?.avatar_url || null
  
      // Call the database upsert directly
      const { error: upsertError } = await supabaseClient
        .from('profiles')
        .upsert({
          id: supabaseUser.id,
          email: email || '',
          full_name: fullName,
          avatar_url: avatarUrl
        })
  
      if (upsertError) {
        console.error('Error upserting profile:', upsertError)
        throw upsertError
      }
  
      // Fetch the updated profile
      const { data: profile, error: fetchError } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', supabaseUser.id)
        .single()
  
      if (fetchError) {
        console.error('Error fetching profile:', fetchError)
        throw fetchError
      }
  
      return profile
    } catch (error) {
      console.error('Error syncing profile:', error)
      throw error
    }
  }

  /**
   * Get user profile from Supabase
   */
  static async getProfile(
    userId: string,
    supabaseClient: SupabaseClient<Database>
  ): Promise<ProfileData | null> {
    try {
      const { data: profile, error } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // Profile doesn't exist - this is expected for new users
          return null
        }
        throw error
      }

      return profile
    } catch (error) {
      console.error('Error getting profile:', error)
      throw error
    }
  }
}
