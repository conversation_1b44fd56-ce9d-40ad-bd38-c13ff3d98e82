import { Task, CreateTaskInput, UpdateTaskInput, TaskFilter, TaskSort } from '../types';

export abstract class BaseTaskService {
  abstract createTask(task: CreateTaskInput): Promise<Task>;
  abstract updateTask(id: string, updates: UpdateTaskInput): Promise<Task>;
  abstract deleteTask(id: string): Promise<void>;
  abstract getTask(id: string): Promise<Task | null>;
  abstract getTasks(filter?: TaskFilter, sort?: TaskSort): Promise<Task[]>;
  abstract getTasksByDate(startDate: string, endDate: string): Promise<{ [date: string]: Task[] }>;

  // Shared business logic
  validateTaskData(task: Partial<Task>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!task.title?.trim()) {
      errors.push('Title is required');
    }

    if (task.title && task.title.length > 255) {
      errors.push('Title must be less than 255 characters');
    }

    if (task.description && task.description.length > 2000) {
      errors.push('Description must be less than 2000 characters');
    }

    if (task.priority && !['low', 'medium', 'high'].includes(task.priority)) {
      errors.push('Invalid priority level');
    }

    if (task.status && !['pending', 'completed', 'cancelled', 'on-hold'].includes(task.status)) {
      errors.push('Invalid status');
    }

    if (task.due_date && new Date(task.due_date) < new Date()) {
      errors.push('Due date cannot be in the past');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  calculateTaskProgress(task: Task): number {
    if (task.status === 'completed') return 100;
    if (task.status === 'cancelled') return 0;
    if (task.status === 'pending') return 0;

    if (task.status === 'on-hold') return task.progress_percentage || 25;
    return 0;
  }

  isTaskOverdue(task: Task): boolean {
    if (!task.due_date || task.status === 'completed') return false;
    return new Date(task.due_date) < new Date();
  }

  filterTasks(tasks: Task[], filter: TaskFilter): Task[] {
    return tasks.filter(task => {
      // Status filter
      if (filter.status && filter.status.length > 0) {
        if (!filter.status.includes(task.status || 'pending')) return false;
      }

      // Priority filter
      if (filter.priority && filter.priority.length > 0) {
        if (!filter.priority.includes(task.priority)) return false;
      }

      // Search filter
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        const searchable = `${task.title} ${task.description} ${task.category || ''}`.toLowerCase();
        if (!searchable.includes(searchLower)) return false;
      }

      // Date range filter
      if (filter.date_range) {
        const taskDate = new Date(task.date);
        const startDate = new Date(filter.date_range.start);
        const endDate = new Date(filter.date_range.end);
        if (taskDate < startDate || taskDate > endDate) return false;
      }

      // Overdue filter
      if (filter.overdue_only && !this.isTaskOverdue(task)) return false;



      return true;
    });
  }

  sortTasks(tasks: Task[], sort: TaskSort): Task[] {
    return [...tasks].sort((a, b) => {
      const aVal = this.getSortValue(a, sort.field);
      const bVal = this.getSortValue(b, sort.field);

      if (aVal === bVal) return 0;
      
      const comparison = aVal < bVal ? -1 : 1;
      return sort.direction === 'asc' ? comparison : -comparison;
    });
  }

  private getSortValue(task: Task, field: string): any {
    switch (field) {
      case 'created_at':
        return new Date(task.created_at);
      case 'updated_at':
        return new Date(task.updated_at || task.created_at);
      case 'due_date':
        return task.due_date ? new Date(task.due_date) : new Date('9999-12-31');
      case 'priority':
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        return priorityOrder[task.priority];
      case 'title':
        return task.title.toLowerCase();
      case 'completion_percentage':
        return this.calculateTaskProgress(task);
      default:
        return 0;
    }
  }
}