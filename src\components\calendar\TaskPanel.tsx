import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { Calendar, Clock, AlertCircle, CheckCircle2, FileText, Image, Video, Music, Archive, File, Edit, Trash2, MessageCircle, Plus, Send, ChevronDown, X, RefreshCw } from 'lucide-react';
import { Task, FileReference } from '@/types/task';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TaskModal } from './TaskModal';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { FileAttachmentService } from '@/services/taskService';

interface TaskPanelProps {
  selectedDate: Date | null;
  tasks: Task[];
  loading?: boolean;
  onTaskUpdate?: (taskId: string, updates: Partial<Task>) => void;
  onTaskStatusUpdate?: (taskId: string, status: Task['status']) => void;
  onTaskClick?: (task: Task) => void;
  onDeleteTask?: (taskId: string) => void;
  onAddComment?: (taskId: string, comment: string) => void;
  onUpdateComment?: (taskId: string, commentIndex: number, newText: string) => void;
  onDeleteComment?: (taskId: string, commentIndex: number) => void;
  onRefresh?: () => void;
  focusedTaskId?: string | null;
  searchQuery?: string;
  isCalendarCollapsed?: boolean;
}

const priorityColors = {
  high: 'bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700',
  medium: 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700',
  low: 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700',
};

const statusColors = {
  pending: 'bg-gray-100 text-gray-700 border-gray-300 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700',

  completed: 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700',
  'on-hold': 'bg-orange-100 text-orange-700 border-orange-300 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700',
  cancelled: 'bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700',
};

// Helper function to get file icon based on file type
const getFileIcon = (fileName: string, fileType?: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  const type = fileType?.toLowerCase() || '';
  
  if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
    return Image;
  }
  if (type.includes('video') || ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension)) {
    return Video;
  }
  if (type.includes('audio') || ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(extension)) {
    return Music;
  }
  if (type.includes('text') || type.includes('document') || ['txt', 'doc', 'docx', 'pdf', 'rtf'].includes(extension)) {
    return FileText;
  }
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return Archive;
  }
  return File;
};

export const TaskPanel: React.FC<TaskPanelProps> = ({
  selectedDate,
  tasks,
  loading = false,
  onTaskUpdate,
  onTaskStatusUpdate,
  onTaskClick,
  onDeleteTask,
  onAddComment,
  onUpdateComment,
  onDeleteComment,
  onRefresh,
  focusedTaskId,
  searchQuery = '',
  isCalendarCollapsed = false,
}) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [attachmentMetadata, setAttachmentMetadata] = useState<Record<string, Array<{filename: string, file_type: string}>>>({});
  
  // Comment management state
  const [showAddComment, setShowAddComment] = useState<Record<string, boolean>>({});
  const [newComment, setNewComment] = useState<Record<string, string>>({});
  const [editingComment, setEditingComment] = useState<Record<string, number | null>>({});
  const [editingCommentText, setEditingCommentText] = useState<Record<string, string>>({});
  
  // Collapsible card state
  const [expandedCards, setExpandedCards] = useState<Record<string, boolean>>({});
  
  // Preserve expanded state when tasks update
  const [previousTaskIds, setPreviousTaskIds] = useState<string[]>([]);
  
  useEffect(() => {
    const currentTaskIds = tasks.map(task => task.id).filter(Boolean);
    
    // Only update if task IDs actually changed (not just task content)
    if (JSON.stringify(currentTaskIds) !== JSON.stringify(previousTaskIds)) {
      // Preserve expanded state for tasks that still exist
      setExpandedCards(prev => {
        const preserved: Record<string, boolean> = {};
        Object.keys(prev).forEach(taskId => {
          if (currentTaskIds.includes(taskId)) {
            preserved[taskId] = prev[taskId];
          }
        });
        return preserved;
      });
      setPreviousTaskIds(currentTaskIds);
    }
  }, [tasks, previousTaskIds]);
  
  // Toggle card expansion (accordion behavior - only one card expanded at a time)
  const toggleCardExpansion = (taskId: string) => {
    console.log('toggleCardExpansion called for task:', taskId);
    const task = tasks.find(t => t.id === taskId);
    console.log('Task details:', task?.title, task?.date);
    
    setExpandedCards(prev => {
      const isCurrentlyExpanded = prev[taskId];
      console.log('Current expansion state:', isCurrentlyExpanded);
      console.log('Previous expanded cards:', prev);
      
      // If clicking on an already expanded card, collapse it
      if (isCurrentlyExpanded) {
        console.log('Collapsing all cards');
        return {}; // Collapse all cards
      }
      
      // Otherwise, expand only this card and collapse all others
      console.log('Expanding task:', taskId);
      const newState = { [taskId]: true };
      console.log('New expanded cards state:', newState);
      return newState;
    });
  };

  // Handle attachment removal
  const handleRemoveAttachment = async (taskId: string, fileName: string, attachmentIndex: number) => {
    if (!user || !onTaskUpdate) return;

    try {
      // Delete the attachment from storage and database
      await FileAttachmentService.deleteTaskAttachment(taskId, fileName, user.id);
      
      // Get the current task
      const task = tasks.find(t => t.id === taskId);
      if (!task || !task.attachments) return;
      
      // Remove the attachment from the task's attachments array
      const updatedAttachments = task.attachments.filter((_, index) => index !== attachmentIndex);
      
      // Update the task
      await onTaskUpdate(taskId, { attachments: updatedAttachments });
      
      // Update local attachment metadata
      setAttachmentMetadata(prev => {
        const updated = { ...prev };
        if (updated[taskId]) {
          updated[taskId] = updated[taskId].filter((_, index) => index !== attachmentIndex);
          if (updated[taskId].length === 0) {
            delete updated[taskId];
          }
        }
        return updated;
      });
      
      toast({
        title: 'Attachment Removed',
        description: `${fileName} has been removed successfully.`,
      });
    } catch (error) {
      console.error('Error removing attachment:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove attachment. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  // Helper function to extract filename from URL
  const extractFilenameFromUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || 'Unknown File';
      // Remove any query parameters or tokens
      return filename.split('?')[0] || 'Unknown File';
    } catch {
      // Fallback for invalid URLs
      const parts = url.split('/');
      const filename = parts[parts.length - 1] || 'Unknown File';
      return filename.split('?')[0] || 'Unknown File';
    }
  };

  // Memoize task IDs to prevent unnecessary re-fetching of attachment metadata
  const taskIds = useMemo(() => 
    tasks.map(task => task.id).filter(Boolean).sort(),
    [tasks]
  );

  // Memoized function to fetch attachment metadata
  const fetchAttachmentMetadata = useCallback(async () => {
    if (!user || !taskIds.length) return;

    try {
      const { data: attachmentData } = await supabase
        .from('file_attachments')
        .select('task_id, filename, file_type')
        .in('task_id', taskIds)
        .eq('user_id', user.id);

      if (attachmentData) {
        const metadata: Record<string, Array<{filename: string, file_type: string}>> = {};
        attachmentData.forEach(att => {
          if (!metadata[att.task_id]) {
            metadata[att.task_id] = [];
          }
          metadata[att.task_id].push({
            filename: att.filename,
            file_type: att.file_type
          });
        });
        
        // Preserve existing metadata for tasks that still exist and merge with new data
        setAttachmentMetadata(prev => {
          const updated = { ...prev };
          let hasChanges = false;
          
          // Remove metadata for tasks that no longer exist
          Object.keys(updated).forEach(taskId => {
            if (!taskIds.includes(taskId)) {
              delete updated[taskId];
              hasChanges = true;
            }
          });
          
          // Add/update metadata for current tasks
          Object.keys(metadata).forEach(taskId => {
            const newMetadata = metadata[taskId];
            const existingMetadata = updated[taskId];
            
            // Check if metadata actually changed
            if (!existingMetadata || 
                JSON.stringify(existingMetadata) !== JSON.stringify(newMetadata)) {
              updated[taskId] = newMetadata;
              hasChanges = true;
            }
          });
          
          // Only update state if there are actual changes
          return hasChanges ? updated : prev;
        });
      }
    } catch (error) {
      console.error('Error fetching attachment metadata:', error);
    }
  }, [user, taskIds]);

  // Fetch attachment metadata for all tasks
  useEffect(() => {
    fetchAttachmentMetadata();
  }, [fetchAttachmentMetadata]);

  // Handle focused task scrolling
  useEffect(() => {
    if (focusedTaskId) {
      // Scroll to the focused task after a short delay to ensure rendering is complete
      const timer = setTimeout(() => {
        const taskElement = document.querySelector(`[data-task-id="${focusedTaskId}"]`);
        if (taskElement) {
          taskElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [focusedTaskId]);

  // Helper function to handle file opening
  const handleFileClick = async (attachment: string | FileReference) => {
    console.log('File click handler called with:', attachment);
    
    if (typeof attachment === 'string') {
      console.log('Opening attachment URL:', attachment);
      // Handle string URLs - open directly since we now have proper public URLs
      window.open(attachment, '_blank');
    } else {
      console.log('Processing FileReference object:', attachment);
      // Handle FileReference objects
      if (attachment.key) {
        console.log('Opening file with key:', attachment.key);
        window.open(attachment.key, '_blank');
      } else {
        console.error('FileReference object missing key:', attachment);
        toast({
          title: 'Invalid File',
          description: 'The file reference is invalid.',
          variant: 'destructive',
        });
      }
    }
  };

  // Helper function to safely parse dates
  const safeParseDate = (dateString: string | undefined | null): Date | null => {
    if (!dateString) {
      return null;
    }
    
    // Convert to string if not already
    const dateStr = String(dateString).trim();
    
    if (!dateStr) {
      return null;
    }
    
    try {
      // Try multiple date parsing strategies
      let date: Date;
      
      // Strategy 1: Direct parsing
      date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        return date;
      }
      
      // Strategy 2: ISO date format (YYYY-MM-DD)
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        date = new Date(dateStr + 'T00:00:00.000Z');
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
      
      // Strategy 3: Try parsing as timestamp
      const timestamp = parseInt(dateStr);
      if (!isNaN(timestamp) && timestamp > 0) {
        date = new Date(timestamp);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
      
      // Strategy 4: Try common date formats
      const commonFormats = [
        dateStr.replace(/[^\d]/g, ''), // Remove non-digits
        dateStr.replace(/T.*$/, ''), // Remove time part
        dateStr.split('T')[0], // Split on T and take first part
      ];
      
      for (const format of commonFormats) {
        if (format && format.length >= 8) {
          date = new Date(format);
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
      }
      
      console.error('All date parsing strategies failed for:', dateStr);
      return null;
      
    } catch (error) {
      console.error('Error parsing date:', dateStr, error);
      return null;
    }
  };

  // Helper function to safely format dates
  const safeFormatDate = (dateString: string | undefined | null, formatStr: string): string => {
    if (!dateString) {
      return 'Invalid Date';
    }
    
    try {
      const parsedDate = safeParseDate(dateString);
      if (parsedDate) {
        return format(parsedDate, formatStr);
      }
      
      console.error('Failed to parse date for formatting:', dateString);
      return 'Invalid Date';
      
    } catch (error) {
      console.error('Error formatting date:', dateString, error);
      return 'Invalid Date';
    }
  };

  // Debug: Log all tasks and localStorage content
  useEffect(() => {
    console.log('=== TASK DEBUGGING ===');
    console.log('All tasks count:', tasks?.length || 0);
    console.log('Selected date:', selectedDate);
    
    // Look for Independence Day task specifically
    const independenceTask = tasks?.find(t => t.title?.toLowerCase().includes('independence'));
    if (independenceTask) {
      console.log('🎆 FOUND INDEPENDENCE DAY TASK:');
      console.log('Task object:', JSON.stringify(independenceTask, null, 2));
      console.log('Task date field:', independenceTask.date);
      console.log('Task date type:', typeof independenceTask.date);
      console.log('Raw date string:', JSON.stringify(independenceTask.date));
    } else {
      console.log('❌ Independence Day task not found in tasks array');
    }
    
    console.log('localStorage scheduled-tasks:', localStorage.getItem('scheduled-tasks'));
    console.log('======================');
  }, [tasks, selectedDate]);

  // Filter and sort tasks for the selected date
  const filteredAndSortedTasks = useMemo(() => {
    if (!Array.isArray(tasks)) return [];
    
    let filtered = [...tasks];

    // Filter by selected date if provided
    if (selectedDate) {
      const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');
      filtered = filtered.filter(task => {
        // More defensive filtering
        if (!task || typeof task !== 'object') {
          console.warn('Invalid task object:', task);
          return false;
        }
        
        if (!task.date) {
          console.warn('Task missing date field:', task.id, task.title);
          return false;
        }
        
        const taskDate = safeParseDate(task.date);
        if (!taskDate) {
          console.warn('Failed to parse task date:', task.id, task.title, task.date);
          return false;
        }
        const taskDateStr = format(taskDate, 'yyyy-MM-dd');
        return taskDateStr === selectedDateStr;
      });
    }

    // Filter by search query if provided
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(task => {
        if (!task || typeof task !== 'object') return false;
        
        // Search in title
        if (task.title && task.title.toLowerCase().includes(query)) {
          return true;
        }
        
        // Search in description
        if (task.description && task.description.toLowerCase().includes(query)) {
          return true;
        }
        
        // Search in category
        if ((task as any).category && (task as any).category.toLowerCase().includes(query)) {
          return true;
        }
        
        // Search in status
        if (task.status && task.status.toLowerCase().includes(query)) {
          return true;
        }
        
        // Search in priority
        if (task.priority && task.priority.toLowerCase().includes(query)) {
          return true;
        }
        
        // Search in comments
        if (task.comments && Array.isArray(task.comments)) {
          return task.comments.some(comment =>
            comment.text && comment.text.toLowerCase().includes(query)
          );
        }
        
        return false;
      });
    }

    // Sort by date in descending order (newest first)
    return filtered.sort((a, b) => {
      const dateA = safeParseDate(a.date);
      const dateB = safeParseDate(b.date);
      
      // Handle invalid dates by putting them at the end
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      
      return dateB.getTime() - dateA.getTime();
    });
  }, [tasks, selectedDate, searchQuery]);

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Header when calendar is collapsed */}
      {isCalendarCollapsed && selectedDate && (
        <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
               <div className="flex items-center gap-3">
                  <h2 className="text-xl font-bold text-left text-gray-900 dark:text-white">
                    Tasks for {format(selectedDate, 'MMMM d, yyyy')}
                  </h2>
                  <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium rounded-full">
                    {filteredAndSortedTasks.length} {filteredAndSortedTasks.length === 1 ? 'task' : 'tasks'}
                  </span>
                </div>
             </div>
          </div>
        </div>
      )}
      
      {/* Task List */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-visible">
        {loading ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
            <h3 className="text-lg font-semibold mb-2">Loading tasks...</h3>
            <p className="text-muted-foreground">Please wait while we fetch your tasks.</p>
          </div>
        ) : !selectedDate ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-lg flex items-center justify-center text-2xl">
              📅
            </div>
            <h3 className="text-lg font-semibold mb-2">Ready to get organized?</h3>
            <p className="text-muted-foreground mb-6">Select a date to view tasks</p>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-center justify-center gap-2">
                📅 <span>Click date tiles to view tasks</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                ➕ <span>Use + button to add tasks</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                ⌨️ <span>Ctrl+N for quick access</span>
              </div>
            </div>
          </div>
        ) : filteredAndSortedTasks.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No tasks found</h3>
            <p className="text-muted-foreground">
              {searchQuery.trim()
                ? `No tasks found matching "${searchQuery}". Try a different search term or clear the search.`
                : 'No tasks found for the selected date. Create a new task to get started.'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3 p-2">
            {filteredAndSortedTasks.map((task, taskIndex) => {
              // Enhanced validation for task object
              if (!task || !task.id || typeof task !== 'object') {
                console.error('Invalid task object in rendering:', task);
                return null;
              }
              
              try {
                // Add debugging for the specific task
                if (task.title && task.title.toLowerCase().includes('advanced')) {
                  console.log('Rendering advanced task:', task);
                  console.log('Expanded state for task:', expandedCards[task.id]);
                  console.log('Task date:', task.date);
                  console.log('Selected date:', selectedDate);
                }
                
                // Add specific debugging for Independence Day task
                if (task.title && task.title.toLowerCase().includes('independence')) {
                  console.log('=== INDEPENDENCE DAY TASK DEBUG ===');
                  console.log('Full task object:', JSON.stringify(task, null, 2));
                  console.log('Task date value:', task.date);
                  console.log('Task date type:', typeof task.date);
                  console.log('Task ID:', task.id);
                  console.log('===================================');
                }
                
                // Additional debugging for task expansion issues
                if (expandedCards[task.id]) {
                  console.log('Task is expanded:', task.id, task.title);
                }
                
                // Special handling for problematic Independence Day task
                if (task.id === '527d97c3-050b-4015-b7c1-f018406b00dd' || 
                    (task.title && task.title.toLowerCase().includes('independence'))) {
                  console.log('🎆 Processing Independence Day task with special handling');
                  console.log('Task data:', JSON.stringify(task, null, 2));
                }
                
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                let taskDate = safeParseDate(task.date);
                if (!taskDate) {
                  console.error('Task with invalid date cannot be rendered properly:', task.id, task.title, task.date);
                  // Default to today's date for rendering purposes
                  taskDate = new Date();
                }
                
                // Ensure we have a valid date object before calling setHours
                if (!taskDate || isNaN(taskDate.getTime())) {
                  console.warn('Invalid date object, defaulting to current date for task:', task.title);
                  taskDate = new Date();
                }
                
                taskDate.setHours(0, 0, 0, 0);
                const isPastTask = taskDate < today;

                return (
                  <Card
                    key={task.id}
                    data-task-id={task.id}
                    className={cn(
                      "transition-all duration-200 hover:shadow-md border-l-4",
                      isPastTask ? "border-l-amber-400 bg-amber-50/30 dark:bg-amber-900/10" : "border-l-primary",
                      task.priority === 'high' && "border-l-red-500",
                      task.priority === 'medium' && "border-l-yellow-500",
                      task.priority === 'low' && "border-l-green-500",
                      focusedTaskId === task.id && "ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 dark:bg-blue-900/20 shadow-lg"
                    )}
                  >
                    <CardContent className="p-2">
                      <div className="space-y-0.5">
                        {/* Title Row with Date and Badges - Clickable */}
                        <div 
                          className="flex items-center justify-between gap-2 cursor-pointer bg-gray-100 dark:bg-gray-700/70 rounded p-1 -m-1 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleCardExpansion(task.id);
                          }}
                        >
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <div className="flex-shrink-0 w-6 h-6 bg-primary/10 text-primary rounded-full flex items-center justify-center text-xs font-medium">
                              {taskIndex + 1}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <h4 className="font-semibold text-sm truncate">
                                  {task.title || 'Untitled Task'}
                                </h4>
                                <span className="text-xs text-muted-foreground flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {safeFormatDate(task.date, 'MMM d, yyyy')}
                                </span>
                              </div>
                            </div>
                          </div>
                          
                          {/* Badges Row - Right Side */}
                          <div className="flex items-center gap-1 flex-shrink-0 overflow-hidden">
                            {/* Status Select */}
                            <Select
                              value={task.status || 'pending'}
                              onValueChange={(value) => onTaskStatusUpdate?.(task.id, value as Task['status'])}
                            >
                              <SelectTrigger className={cn(
                                 "h-6 text-xs border-0 px-2 py-0 w-auto min-w-0 focus:ring-0 focus:ring-offset-0",
                                 statusColors[task.status || 'pending']
                               )}>
                                <SelectValue className="capitalize" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="pending">Pending</SelectItem>
                
                                <SelectItem value="completed">Completed</SelectItem>
                                <SelectItem value="on-hold">On Hold</SelectItem>
                                <SelectItem value="cancelled">Cancelled</SelectItem>
                              </SelectContent>
                            </Select>

                            {/* Priority Select */}
                            <Select
                              value={task.priority}
                              onValueChange={(value) => onTaskUpdate?.(task.id, { priority: value as Task['priority'] })}
                            >
                              <SelectTrigger className={cn(
                                 "h-6 text-xs border-0 px-2 py-0 w-auto min-w-0 focus:ring-0 focus:ring-offset-0",
                                 priorityColors[task.priority]
                               )}>
                                <SelectValue className="capitalize" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="low">Low</SelectItem>
                                <SelectItem value="medium">Medium</SelectItem>
                                <SelectItem value="high">High</SelectItem>
                              </SelectContent>
                            </Select>

                            {/* Category Select */}
                            <Select
                              value={(task as any).category || 'General'}
                              onValueChange={(value) => onTaskUpdate?.(task.id, { category: value })}
                            >
                              <SelectTrigger className="h-6 text-xs border-0 px-2 py-0 w-auto min-w-0 focus:ring-0 focus:ring-offset-0 bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300">
                                <SelectValue className="capitalize" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="General">General</SelectItem>
                                <SelectItem value="Work">Work</SelectItem>
                                <SelectItem value="Personal">Personal</SelectItem>
                                <SelectItem value="Health">Health</SelectItem>
                                <SelectItem value="Finance">Finance</SelectItem>
                                <SelectItem value="Education">Education</SelectItem>
                                <SelectItem value="Shopping">Shopping</SelectItem>
                                <SelectItem value="Travel">Travel</SelectItem>
                              </SelectContent>
                            </Select>
                            
                            {/* Chevron Icon */}
                            <ChevronDown 
                              className={cn(
                                "h-4 w-4 text-gray-400 transition-transform duration-200 ml-2",
                                expandedCards[task.id] && "rotate-180"
                              )}
                            />
                          </div>
                        </div>

                        {/* Description */}
                        {task.description && (
                          <p className="text-xs text-muted-foreground leading-tight pl-8 mt-0.5 text-left">
                            {task.description}
                          </p>
                        )}

                        {/* Collapsible Content */}
                        {expandedCards[task.id] && (
                          <div className="space-y-2 mt-2 pl-8 border-l-2 border-gray-100 dark:border-gray-700">
                            {/* Task Details */}
                            <div className="space-y-1">
                              {/* Completion Timestamp */}
                              {task.completedAt && (() => {
                                const completedDate = safeParseDate(task.completedAt);
                                return completedDate ? (
                                  <div className="flex items-center gap-2 text-xs text-green-600 dark:text-green-400">
                                    <CheckCircle2 className="h-3 w-3" />
                                    <span>Completed: {format(completedDate, 'MMM d, yyyy h:mm a')}</span>
                                  </div>
                                ) : null;
                              })()}
                              {task.dueDate && (() => {
                                const dueDate = safeParseDate(task.dueDate);
                                return dueDate ? (
                                  <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                                    <AlertCircle className="h-3 w-3" />
                                    <span>Due: {format(dueDate, 'MMM d, yyyy h:mm a')}</span>
                                  </div>
                                ) : null;
                              })()}

                            </div>
                            
                            {/* Attachments */}
                            {task.attachments && task.attachments.length > 0 && (
                              <div className="mt-1">
                                <div className="flex flex-wrap gap-1">
                                  {task.attachments.map((attachment, index) => {
                                    // Get filename from metadata or fallback to URL extraction
                                    const taskMetadata = attachmentMetadata[task.id || ''] || [];
                                    const attachmentMeta = taskMetadata[index];
                                    
                                    // Always show attachments, even if metadata is temporarily unavailable
                                    const fileName = attachmentMeta 
                                      ? attachmentMeta.filename
                                      : typeof attachment === 'string' 
                                        ? extractFilenameFromUrl(attachment)
                                        : attachment.name || `Attachment ${index + 1}`;
                                        
                                    const fileType = attachmentMeta
                                      ? attachmentMeta.file_type
                                      : typeof attachment === 'string' 
                                        ? undefined 
                                        : attachment.type;
                                        
                                    const FileIcon = getFileIcon(fileName, fileType);
                                    
                                    return (
                                      <div key={index} className="relative inline-flex items-center group">
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleFileClick(attachment);
                                          }}
                                          className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 rounded border border-blue-200 transition-colors dark:bg-blue-900/20 dark:hover:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700 pr-6"
                                          title={`Open ${fileName}`}
                                        >
                                          <FileIcon className="h-3 w-3" />
                                          <span className="truncate max-w-[100px]">{fileName}</span>
                                        </button>
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleRemoveAttachment(task.id, fileName, index);
                                          }}
                                          className="absolute right-0.5 top-0.5 bottom-0.5 w-3.5 h-3.5 flex items-center justify-center bg-red-500 hover:bg-red-600 text-white rounded-sm opacity-0 group-hover:opacity-100 transition-opacity duration-150"
                                          title={`Remove ${fileName}`}
                                        >
                                          <X className="h-2 w-2" />
                                        </button>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}

                            {/* Comments Section */}
                            <div className="mt-2">
                              <div className="flex items-center gap-1 mb-1">
                                <MessageCircle className="h-3 w-3 text-gray-500" />
                                <span className="text-xs text-gray-500 font-medium">Comments ({task.comments?.length || 0})</span>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setShowAddComment(prev => ({ ...prev, [task.id]: true }));
                                  }}
                                  className="ml-1 p-1 text-gray-400 hover:text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded transition-colors"
                                  title="Add comment"
                                >
                                  <Plus className="h-3 w-3" />
                                </button>
                              </div>
                              
                              {/* Add Comment Input */}
                              {showAddComment[task.id] && (
                                <div className="flex gap-1 mb-2">
                                  <input
                                    type="text"
                                    value={newComment[task.id] || ''}
                                    onChange={(e) => setNewComment(prev => ({ ...prev, [task.id]: e.target.value }))}
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        e.stopPropagation();
                                        if (newComment[task.id]?.trim() && onAddComment) {
                                          onAddComment(task.id, newComment[task.id].trim());
                                          setNewComment(prev => ({ ...prev, [task.id]: '' }));
                                          setShowAddComment(prev => ({ ...prev, [task.id]: false }));
                                        }
                                      } else if (e.key === 'Escape') {
                                        e.stopPropagation();
                                        setShowAddComment(prev => ({ ...prev, [task.id]: false }));
                                        setNewComment(prev => ({ ...prev, [task.id]: '' }));
                                      }
                                    }}
                                    className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                    placeholder="Add a comment..."
                                    autoFocus
                                  />
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (newComment[task.id]?.trim() && onAddComment) {
                                        onAddComment(task.id, newComment[task.id].trim());
                                        setNewComment(prev => ({ ...prev, [task.id]: '' }));
                                        setShowAddComment(prev => ({ ...prev, [task.id]: false }));
                                      }
                                    }}
                                    className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                                    title="Add comment"
                                  >
                                    <Send className="h-3 w-3" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setShowAddComment(prev => ({ ...prev, [task.id]: false }));
                                      setNewComment(prev => ({ ...prev, [task.id]: '' }));
                                    }}
                                    className="px-2 py-1 text-xs bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                                    title="Cancel"
                                  >
                                    ×
                                  </button>
                                </div>
                              )}
                              
                              {/* Comments List */}
                              {task.comments && task.comments.length > 0 && (
                                <div className="space-y-1">
                                  {task.comments.slice().reverse().map((comment, index) => {
                                    const originalIndex = task.comments.length - 1 - index;
                                    return (
                                      <div key={comment.id} className="group relative">
                                        {editingComment[task.id] === originalIndex ? (
                                          <div className="flex gap-1">
                                            <input
                                              type="text"
                                              value={editingCommentText[task.id] || ''}
                                              onChange={(e) => setEditingCommentText(prev => ({ ...prev, [task.id]: e.target.value }))}
                                              onKeyDown={(e) => {
                                                if (e.key === 'Enter') {
                                                  e.stopPropagation();
                                                  if (editingCommentText[task.id]?.trim() && onUpdateComment) {
                                                    onUpdateComment(task.id, originalIndex, editingCommentText[task.id].trim());
                                                    setEditingComment(prev => ({ ...prev, [task.id]: null }));
                                                    setEditingCommentText(prev => ({ ...prev, [task.id]: '' }));
                                                  }
                                                } else if (e.key === 'Escape') {
                                                  e.stopPropagation();
                                                  setEditingComment(prev => ({ ...prev, [task.id]: null }));
                                                  setEditingCommentText(prev => ({ ...prev, [task.id]: '' }));
                                                }
                                              }}
                                              className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                              placeholder="Edit comment..."
                                              autoFocus
                                            />
                                            <button
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                if (editingCommentText[task.id]?.trim() && onUpdateComment) {
                                                  onUpdateComment(task.id, originalIndex, editingCommentText[task.id].trim());
                                                  setEditingComment(prev => ({ ...prev, [task.id]: null }));
                                                  setEditingCommentText(prev => ({ ...prev, [task.id]: '' }));
                                                }
                                              }}
                                              className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                                              title="Save comment"
                                            >
                                              <Send className="h-3 w-3" />
                                            </button>
                                          </div>
                                        ) : (
                                          <div className="flex items-start justify-between bg-gray-50 dark:bg-gray-800/50 rounded px-2 py-1 text-xs group">
                                            <div className="flex-1 min-w-0">
                                              <p className="text-gray-700 dark:text-gray-300 break-words text-left">{comment.text}</p>
                                              <p className="text-gray-400 text-xs mt-0.5 text-left">
                                                {safeFormatDate(comment.timestamp, 'MMM d, h:mm a')}
                                              </p>
                                            </div>
                                            <div className="flex gap-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                                              <button
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  setEditingComment(prev => ({ ...prev, [task.id]: originalIndex }));
                                                  setEditingCommentText(prev => ({ ...prev, [task.id]: comment.text }));
                                                }}
                                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                                title="Edit comment"
                                              >
                                                <Edit className="h-3 w-3" />
                                              </button>
                                              <button
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  if (window.confirm('Are you sure you want to delete this comment?') && onDeleteComment) {
                                                    onDeleteComment(task.id, originalIndex);
                                                  }
                                                }}
                                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                                title="Delete comment"
                                              >
                                                <Trash2 className="h-3 w-3" />
                                              </button>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Action Buttons - Edit and Delete */}
                        <div className="flex justify-end gap-1 mt-2 pr-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onTaskClick?.(task);
                            }}
                            className="inline-flex items-center justify-center w-7 h-7 text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-full border border-blue-200 transition-all duration-200 hover:scale-105 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700"
                            title="Edit task"
                          >
                            <Edit className="h-3 w-3" />
                          </button>
                          {onDeleteTask && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                if (window.confirm('Are you sure you want to delete this task?')) {
                                  onDeleteTask(task.id);
                                }
                              }}
                              className="inline-flex items-center justify-center w-7 h-7 text-xs bg-red-50 hover:bg-red-100 text-red-600 rounded-full border border-red-200 transition-all duration-200 hover:scale-105 dark:bg-red-900/20 dark:hover:bg-red-900/30 dark:text-red-400 dark:border-red-700"
                              title="Delete task"
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              } catch (error) {
                console.error('Error rendering task:', task.title, '\nTask ID:', task.id, '\nError:', error);
                console.error('Task object causing error:', JSON.stringify(task, null, 2));
                
                // Special handling for Independence Day task
                if (task.title && task.title.toLowerCase().includes('independence')) {
                  console.error('=== INDEPENDENCE DAY TASK ERROR DETAILS ===');
                  console.error('Task date field:', task.date);
                  console.error('Task date type:', typeof task.date);
                  console.error('Task date length:', task.date?.length);
                  console.error('Task date characters:', task.date ? Array.from(task.date).map(c => `${c} (${c.charCodeAt(0)})`) : 'N/A');
                  console.error('============================================');
                }
                
                return (
                  <Card key={task.id} className="border-red-200 bg-red-50 dark:bg-red-900/10">
                    <CardContent className="p-3">
                      <div className="text-red-600 dark:text-red-400">
                        <p className="font-medium">Error rendering task: {task.title || 'Unknown Task'}</p>
                        <p className="text-sm">Task ID: {task.id}</p>
                        <p className="text-sm">Error: {error instanceof Error ? error.message : String(error)}</p>
                        <p className="text-sm">Date value: {JSON.stringify(task.date)}</p>
                      </div>
                    </CardContent>
                  </Card>
                );
              }
            })}
          </div>
        )}
      </div>
    </div>
  );
};