export interface FileReference {
  key: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  lastModified?: number;
}

export type TaskInsert = Omit<Task, 'id' | 'createdAt' | 'updatedAt'>;
export type TaskUpdate = Partial<Omit<Task, 'id' | 'createdAt' | 'updatedAt'>>;

export interface Task {
  id: string;
  date: string; // YYYY-MM-DD format
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status?: 'pending' | 'completed' | 'cancelled' | 'on-hold';
  completedAt?: string;
  dueDate?: string; // YYYY-MM-DD format, optional due date
  category?: string; // Task category (General, Personal, Official, Home Tasks)
  tags?: string[]; // Task tags within category
  comments?: Array<{
    id: string;
    text: string;
    timestamp: string;
  }>; // User comments with timestamps
  links?: string[];
  location?: string; // Task location with GPS coordinates or address
  attachments?: Array<string | FileReference>;
  voiceNotes?: string[]; // URLs to voice recordings (blob URLs or cloud storage URLs)
  recurringPattern?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurringEndDate?: string; // YYYY-MM-DD format for recurring tasks
  createdAt: string;
  updatedAt: string;
}

export interface TasksByDate {
  [date: string]: Task[];
}