import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface DynamicBottomLayoutProps {
  children: ReactNode;
  className?: string;
  leftOffset?: string;
  centered?: boolean;
  topOffset?: string;
  isCalendarCollapsed?: boolean;
}

export const DynamicBottomLayout = ({ children, className = '', leftOffset = 'left-0', centered = false, topOffset = 'top-[calc(42vh+10rem)]', isCalendarCollapsed = false }: DynamicBottomLayoutProps) => (
  <div className={cn(
    `fixed ${isCalendarCollapsed ? 'top-16' : topOffset} bottom-0 overflow-x-hidden overflow-y-auto scrollbar-visible bg-background transition-all duration-300 ease-in-out`,
    centered ? 'left-1/2 transform -translate-x-1/2 w-full max-w-6xl' : `right-0 ${leftOffset}`,
    className
  )}>
    {children}
  </div>
);