{"name": "professional-task-management-saas", "private": false, "version": "1.0.0", "type": "module", "description": "A modern, privacy-first task management SaaS with advanced analytics, voice integration, and professional-grade features", "keywords": ["task-management", "productivity", "calendar", "analytics", "voice-integration", "saas", "react", "typescript", "privacy-first", "mobile-app"], "author": {"name": "Your Name", "email": "<EMAIL>", "url": "https://yourdomain.com"}, "license": "MIT", "homepage": "https://github.com/yourusername/scheduled-task-view#readme", "repository": {"type": "git", "url": "git+https://github.com/yourusername/scheduled-task-view.git"}, "bugs": {"url": "https://github.com/yourusername/scheduled-task-view/issues"}, "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/app": "^7.0.1", "@capacitor/app-launcher": "^7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.2", "@capacitor/geolocation": "^7.1.4", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.4.2", "@capacitor/local-notifications": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.56.2", "@types/redis": "^4.0.10", "@types/winston": "^2.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.6", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^15.6.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "stripe": "^18.3.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tsyringe": "^4.10.0", "vaul": "^0.9.3", "vite-plugin-pwa": "^1.0.1", "winston": "^3.17.0", "zod": "^3.23.8", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.40.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jsdom": "^23.0.1", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-bundle-analyzer": "^0.7.0", "vitest": "^1.0.4"}}