import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';

export const ConnectionTest: React.FC = () => {
  const [status, setStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

  useEffect(() => {
    // Simulate connection test
    const timer = setTimeout(() => {
      setStatus('connected');
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const getStatusColor = () => {
    switch (status) {
      case 'checking': return '#f59e0b';
      case 'connected': return '#10b981';
      case 'disconnected': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'checking': return '🔄 Checking connection...';
      case 'connected': return '✅ Connected';
      case 'disconnected': return '❌ Disconnected';
      default: return '⚪ Unknown';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.statusText, { color: getStatusColor() }]}>
        {getStatusText()}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    backgroundColor: '#f8fafc',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
