import React, { useState, useEffect } from 'react';
import { <PERSON>gerprint, Shield, X, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { BiometricService } from '@/services/biometricService';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface BiometricSetupProps {
  onClose?: () => void;
  showAsCard?: boolean;
}

export const BiometricSetup: React.FC<BiometricSetupProps> = ({
  onClose,
  showAsCard = false
}) => {
  const { user } = useAuth();
  const userId = user?.id;
  const { toast } = useToast();
  const [isAvailable, setIsAvailable] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [availableTypes, setAvailableTypes] = useState<string[]>([]);

  useEffect(() => {
    checkBiometricAvailability();
  }, [userId]);

  const checkBiometricAvailability = async () => {
    try {
      const available = await BiometricService.isAvailable();
      setIsAvailable(available);
      
      if (available && userId) {
        setIsEnabled(BiometricService.isBiometricEnabled(userId));
        const types = await BiometricService.getAvailableBiometricTypes();
        setAvailableTypes(types);
      }
    } catch (error) {
      console.error('Error checking biometric availability:', error);
    }
  };

  const handleToggleBiometric = async () => {
    if (!userId) return;
    
    setIsLoading(true);
    try {
      if (isEnabled) {
        // Disable biometric
        await BiometricService.disableBiometric(userId);
        setIsEnabled(false);
        toast({
          title: "Biometric Disabled",
          description: "Biometric authentication has been disabled.",
        });
      } else {
        // Enable biometric
        await BiometricService.enableBiometric(userId);
        setIsEnabled(true);
        toast({
          title: "Biometric Enabled",
          description: "Biometric authentication has been enabled successfully.",
        });
      }
    } catch (error: any) {
      toast({
        title: "Biometric Setup Failed",
        description: error.message || "Failed to setup biometric authentication.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestBiometric = async () => {
    if (!userId) return;
    
    setIsLoading(true);
    try {
      const success = await BiometricService.authenticate({
        title: 'Test Biometric Authentication',
        subtitle: 'Verify your identity',
        description: 'Use your biometric to test the authentication.',
      });
      
      if (success) {
        toast({
          title: "Authentication Successful",
          description: "Biometric authentication is working correctly.",
        });
      } else {
        toast({
          title: "Authentication Failed",
          description: "Biometric authentication was not successful.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Authentication Error",
        description: error.message || "Failed to authenticate.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const content = (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Fingerprint className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="font-medium">Biometric Authentication</h3>
            <p className="text-sm text-muted-foreground">
              {isAvailable 
                ? `Use ${availableTypes.join(' or ')} for quick access`
                : 'Not available on this device'
              }
            </p>
          </div>
        </div>
        
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {!isAvailable ? (
        <div className="bg-muted/50 rounded-lg p-4 mt-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Shield className="h-4 w-4" />
            <span>Biometric authentication is not supported on this device or browser.</span>
          </div>
        </div>
      ) : (
        <div className="space-y-4 mt-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium">Enable Biometric Login</p>
              <p className="text-xs text-muted-foreground">
                Sign in quickly using your biometric data
              </p>
            </div>
            <Switch
              checked={isEnabled}
              onCheckedChange={handleToggleBiometric}
              disabled={isLoading}
            />
          </div>

          {isEnabled && (
            <>
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-300">
                  <CheckCircle className="h-4 w-4" />
                  <span>Biometric authentication is enabled</span>
                </div>
              </div>

              <Button
                variant="outline"
                onClick={handleTestBiometric}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin h-4 w-4 mr-2 border-2 border-current border-t-transparent rounded-full" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Fingerprint className="h-4 w-4 mr-2" />
                    Test Biometric Authentication
                  </>
                )}
              </Button>
            </>
          )}

          <div className="text-xs text-muted-foreground bg-muted/30 rounded-lg p-3">
            <p className="font-medium mb-1">Privacy & Security</p>
            <p>
              Your biometric data never leaves your device and is used only for local authentication.
              You can disable this feature at any time.
            </p>
          </div>
        </div>
      )}
    </>
  );

  if (showAsCard) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Settings
          </CardTitle>
          <CardDescription>
            Manage your biometric authentication preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {content}
    </div>
  );
};