import { useReducer, useEffect } from 'react';

export interface SettingsState {
  // Appearance
  theme: 'light' | 'dark' | 'system';
  compactView: boolean;
  fontSize: number;
  colorScheme: 'default' | 'blue' | 'green' | 'purple';
  animationsEnabled: boolean;
  highContrast: boolean;
  
  // Notifications
  notificationsEnabled: boolean;
  taskReminders: boolean;
  overdueAlerts: boolean;
  dailyDigest: boolean;
  soundEnabled: boolean;
  reminderTime: number;
  
  // General
  autoSave: boolean;
  weekStartsOn: 'sunday' | 'monday' | 'saturday';
  language: string;
}

type SettingsAction = 
  | { type: 'SET_THEME'; payload: SettingsState['theme'] }
  | { type: 'SET_COMPACT_VIEW'; payload: boolean }
  | { type: 'SET_FONT_SIZE'; payload: number }
  | { type: 'SET_COLOR_SCHEME'; payload: SettingsState['colorScheme'] }
  | { type: 'SET_ANIMATIONS'; payload: boolean }
  | { type: 'SET_HIGH_CONTRAST'; payload: boolean }
  | { type: 'SET_NOTIFICATIONS'; payload: boolean }
  | { type: 'SET_TASK_REMINDERS'; payload: boolean }
  | { type: 'SET_OVERDUE_ALERTS'; payload: boolean }
  | { type: 'SET_DAILY_DIGEST'; payload: boolean }
  | { type: 'SET_SOUND'; payload: boolean }
  | { type: 'SET_REMINDER_TIME'; payload: number }
  | { type: 'SET_AUTO_SAVE'; payload: boolean }
  | { type: 'SET_WEEK_STARTS_ON'; payload: SettingsState['weekStartsOn'] }
  | { type: 'SET_LANGUAGE'; payload: string }
  | { type: 'LOAD_SETTINGS'; payload: Partial<SettingsState> }
  | { type: 'RESET_SETTINGS' };

const defaultSettings: SettingsState = {
  theme: 'system',
  compactView: false,
  fontSize: 16,
  colorScheme: 'default',
  animationsEnabled: true,
  highContrast: false,
  notificationsEnabled: true,
  taskReminders: true,
  overdueAlerts: true,
  dailyDigest: false,
  soundEnabled: true,
  reminderTime: 15,
  autoSave: true,
  weekStartsOn: 'sunday',
  language: 'en',
};

function settingsReducer(state: SettingsState, action: SettingsAction): SettingsState {
  switch (action.type) {
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    case 'SET_COMPACT_VIEW':
      return { ...state, compactView: action.payload };
    case 'SET_FONT_SIZE':
      return { ...state, fontSize: action.payload };
    case 'SET_COLOR_SCHEME':
      return { ...state, colorScheme: action.payload };
    case 'SET_ANIMATIONS':
      return { ...state, animationsEnabled: action.payload };
    case 'SET_HIGH_CONTRAST':
      return { ...state, highContrast: action.payload };
    case 'SET_NOTIFICATIONS':
      return { ...state, notificationsEnabled: action.payload };
    case 'SET_TASK_REMINDERS':
      return { ...state, taskReminders: action.payload };
    case 'SET_OVERDUE_ALERTS':
      return { ...state, overdueAlerts: action.payload };
    case 'SET_DAILY_DIGEST':
      return { ...state, dailyDigest: action.payload };
    case 'SET_SOUND':
      return { ...state, soundEnabled: action.payload };
    case 'SET_REMINDER_TIME':
      return { ...state, reminderTime: action.payload };
    case 'SET_AUTO_SAVE':
      return { ...state, autoSave: action.payload };
    case 'SET_WEEK_STARTS_ON':
      return { ...state, weekStartsOn: action.payload };
    case 'SET_LANGUAGE':
      return { ...state, language: action.payload };
    case 'LOAD_SETTINGS':
      return { ...state, ...action.payload };
    case 'RESET_SETTINGS':
      return defaultSettings;
    default:
      return state;
  }
}

export function useSettingsState() {
  const [state, dispatch] = useReducer(settingsReducer, defaultSettings);

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('app-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        dispatch({ type: 'LOAD_SETTINGS', payload: parsed });
      } catch (error) {
        console.warn('Failed to load settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('app-settings', JSON.stringify(state));
  }, [state]);

  return { state, dispatch };
}
