-- Fix RLS issues properly

-- Enable RLS on tables that need it (excluding views)
ALTER TABLE IF EXISTS public.subscribers ENABLE ROW LEVEL SECURITY;

-- Update functions to have proper search_path for security
CREATE OR REPLACE FUNCTION public.handle_assignment_notification()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Send notification when assignment is created
  IF TG_OP = 'INSERT' THEN
    INSERT INTO notifications (user_id, type, title, message, data)
    VALUES (
      NEW.assigned_to,
      'assignment',
      'New Task Assignment',
      'You have been assigned a new task',
      jsonb_build_object('task_id', NEW.task_id, 'assignment_id', NEW.id)
    );
  END IF;
  
  -- Send notification when assignment status changes
  IF TG_OP = 'UPDATE' AND NEW.status != OLD.status THEN
    INSERT INTO notifications (user_id, type, title, message, data)
    VALUES (
      NEW.assigned_by,
      'assignment_update',
      'Assignment Status Updated',
      format('Assignment status changed to %s', NEW.status),
      jsonb_build_object('task_id', NEW.task_id, 'assignment_id', NEW.id, 'status', NEW.status)
    );
  END IF;
  
  RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.update_task_assignment_status()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
    -- Update task assignment_status based on assignment table status
    UPDATE tasks 
    SET assignment_status = NEW.status,
        updated_at = NOW()
    WHERE id = NEW.task_id;
    
    RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$;