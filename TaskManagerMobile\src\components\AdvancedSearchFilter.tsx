import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Task } from '../types/task';
import { format, isWithinInterval, parseISO } from 'date-fns';

interface FilterCriteria {
  searchText: string;
  status: string[];
  priority: string[];
  category: string[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  hasAttachments: boolean | null;
  hasVoiceNotes: boolean | null;
}

interface SavedFilter {
  id: string;
  name: string;
  criteria: FilterCriteria;
}

interface AdvancedSearchFilterProps {
  tasks: Task[];
  onFilterChange: (filteredTasks: Task[]) => void;
  visible: boolean;
  onClose: () => void;
}

export default function AdvancedSearchFilter({
  tasks,
  onFilterChange,
  visible,
  onClose,
}: AdvancedSearchFilterProps) {
  const [searchText, setSearchText] = useState('');
  const [criteria, setCriteria] = useState<FilterCriteria>({
    searchText: '',
    status: [],
    priority: [],
    category: [],
    dateRange: { start: null, end: null },
    hasAttachments: null,
    hasVoiceNotes: null,
  });
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [filterName, setFilterName] = useState('');

  // Available options
  const statusOptions = ['pending', 'in-progress', 'completed', 'on-hold', 'cancelled'];
  const priorityOptions = ['low', 'medium', 'high'];
  const categoryOptions = useMemo(() => {
    const categories = new Set(tasks.map(task => task.category).filter(Boolean));
    return Array.from(categories) as string[];
  }, [tasks]);

  // Load saved filters
  useEffect(() => {
    loadSavedFilters();
  }, []);

  // Apply filters when criteria change
  useEffect(() => {
    applyFilters();
  }, [criteria, searchText, tasks]);

  const loadSavedFilters = async () => {
    try {
      const mockFilters: SavedFilter[] = [
        {
          id: '1',
          name: 'High Priority Pending',
          criteria: {
            searchText: '',
            status: ['pending'],
            priority: ['high'],
            category: [],
            dateRange: { start: null, end: null },
            hasAttachments: null,
            hasVoiceNotes: null,
          },
        },
        {
          id: '2',
          name: 'This Week Tasks',
          criteria: {
            searchText: '',
            status: [],
            priority: [],
            category: [],
            dateRange: {
              start: new Date(),
              end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            },
            hasAttachments: null,
            hasVoiceNotes: null,
          },
        },
      ];
      setSavedFilters(mockFilters);
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }
  };

  const toggleArrayFilter = (array: string[], value: string): string[] => {
    return array.includes(value)
      ? array.filter(item => item !== value)
      : [...array, value];
  };

  const applyFilters = () => {
    let filtered = tasks;

    // Full-text search
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(task =>
        task.title?.toLowerCase().includes(searchLower) ||
        task.description?.toLowerCase().includes(searchLower) ||
        (task.category && task.category.toLowerCase().includes(searchLower))
      );
    }

    // Apply criteria filters
    if (criteria.status.length > 0) {
      filtered = filtered.filter(task => criteria.status.includes(task.status || ''));
    }

    if (criteria.priority.length > 0) {
      filtered = filtered.filter(task => criteria.priority.includes(task.priority || ''));
    }

    if (criteria.category.length > 0) {
      filtered = filtered.filter(task => 
        task.category && criteria.category.includes(task.category)
      );
    }

    if (criteria.dateRange.start && criteria.dateRange.end) {
      filtered = filtered.filter(task => {
        const taskDate = parseISO(task.date);
        return isWithinInterval(taskDate, {
          start: criteria.dateRange.start!,
          end: criteria.dateRange.end!,
        });
      });
    }

    onFilterChange(filtered);
  };

  const saveCurrentFilter = () => {
    if (!filterName.trim()) return;

    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name: filterName,
      criteria: { ...criteria, searchText },
    };

    setSavedFilters([...savedFilters, newFilter]);
    setFilterName('');
  };

  const loadFilter = (filter: SavedFilter) => {
    setCriteria(filter.criteria);
    setSearchText(filter.criteria.searchText);
    onClose();
  };

  const clearAllFilters = () => {
    setSearchText('');
    setCriteria({
      searchText: '',
      status: [],
      priority: [],
      category: [],
      dateRange: { start: null, end: null },
      hasAttachments: null,
      hasVoiceNotes: null,
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Advanced Search & Filter</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#1f2937" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* Search */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Search</Text>
              <TextInput
                style={styles.searchInput}
                placeholder="Search tasks..."
                value={searchText}
                onChangeText={setSearchText}
              />
            </View>

            {/* Status Filter */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Status</Text>
              <View style={styles.filterOptions}>
                {statusOptions.map(status => (
                  <TouchableOpacity
                    key={status}
                    style={[
                      styles.filterOption,
                      criteria.status.includes(status) && styles.selectedOption,
                    ]}
                    onPress={() =>
                      setCriteria(prev => ({
                        ...prev,
                        status: toggleArrayFilter(prev.status, status),
                      }))
                    }
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        criteria.status.includes(status) && styles.selectedOptionText,
                      ]}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Priority Filter */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Priority</Text>
              <View style={styles.filterOptions}>
                {priorityOptions.map(priority => (
                  <TouchableOpacity
                    key={priority}
                    style={[
                      styles.filterOption,
                      criteria.priority.includes(priority) && styles.selectedOption,
                    ]}
                    onPress={() =>
                      setCriteria(prev => ({
                        ...prev,
                        priority: toggleArrayFilter(prev.priority, priority),
                      }))
                    }
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        criteria.priority.includes(priority) && styles.selectedOptionText,
                      ]}
                    >
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Category Filter */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Category</Text>
              <View style={styles.filterOptions}>
                {categoryOptions.map(category => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.filterOption,
                      criteria.category.includes(category) && styles.selectedOption,
                    ]}
                    onPress={() =>
                      setCriteria(prev => ({
                        ...prev,
                        category: toggleArrayFilter(prev.category, category),
                      }))
                    }
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        criteria.category.includes(category) && styles.selectedOptionText,
                      ]}
                    >
                      {category}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Saved Filters */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Saved Filters</Text>
              {savedFilters.map(filter => (
                <TouchableOpacity
                  key={filter.id}
                  style={styles.savedFilter}
                  onPress={() => loadFilter(filter)}
                >
                  <Text style={styles.savedFilterName}>{filter.name}</Text>
                  <Ionicons name="chevron-forward" size={20} color="#6b7280" />
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.clearButton} onPress={clearAllFilters}>
              <Text style={styles.clearButtonText}>Clear All</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={saveCurrentFilter}
            >
              <Text style={styles.saveButtonText}>Save Filter</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 20,
  },
  selectedOption: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  filterOptionText: {
    fontSize: 14,
    color: '#6b7280',
  },
  selectedOptionText: {
    color: '#ffffff',
  },
  savedFilter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    marginBottom: 8,
  },
  savedFilterName: {
    fontSize: 14,
    color: '#1f2937',
  },
  footer: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  clearButton: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 14,
    color: '#6b7280',
  },
  saveButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 14,
    color: '#ffffff',
    fontWeight: '600',
  },
});