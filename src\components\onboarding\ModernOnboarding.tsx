import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { DemoMode } from './DemoMode';
import { WelcomePage } from './WelcomePage';
import { OnboardingTour } from './OnboardingTour';
import { OnboardingService } from '@/services/onboardingService';

type OnboardingStep = 'demo' | 'welcome' | 'tour' | 'app';

export const ModernOnboarding: React.FC = () => {
  const { user } = useAuth();
  const isSignedIn = !!user;
  const userId = user?.id;
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('demo');
  const [hasSeenDemo, setHasSeenDemo] = useState(false);

  useEffect(() => {
    // Check if user has completed onboarding
    if (isSignedIn && userId) {
      const hasCompletedOnboarding = OnboardingService.hasCompletedOnboarding(userId);
      
      if (hasCompletedOnboarding) {
        navigate('/');
        return;
      }
      
      // If signed in, show welcome instead of demo
      setCurrentStep('welcome');
    }
  }, [isSignedIn, userId, navigate]);

  const handleDemoSignUp = () => {
    navigate('/sign-up');
  };

  const handleContinueDemo = () => {
    setHasSeenDemo(true);
    // Continue showing demo but don't redirect
  };

  const handleWelcomeComplete = () => {
    if (userId) {
      OnboardingService.markOnboardingComplete(userId);
    }
    navigate('/');
  };

  const handleStartTour = () => {
    setCurrentStep('tour');
  };

  const handleTourComplete = () => {
    if (userId) {
      OnboardingService.markOnboardingComplete(userId);
    }
    navigate('/');
  };

  const handleSkipToApp = () => {
    if (userId) {
      OnboardingService.markOnboardingComplete(userId);
    }
    navigate('/');
  };

  // Render appropriate step
  switch (currentStep) {
    case 'demo':
      return (
        <DemoMode
          onSignUp={handleDemoSignUp}
          onContinueDemo={handleContinueDemo}
        />
      );
      
    case 'welcome':
      return (
        <WelcomePage
          onLoadSamples={() => handleWelcomeComplete()}
          onStartTour={handleStartTour}
          onSkip={handleSkipToApp}
          isLoadingSamples={false}
          userName={isSignedIn ? 'there' : 'friend'}
        />
      );
      
    case 'tour':
      return (
        <OnboardingTour
          onComplete={handleTourComplete}
          onSkip={handleSkipToApp}
        />
      );
      
    default:
      return null;
  }
};