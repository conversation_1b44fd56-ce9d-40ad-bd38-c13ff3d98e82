import { SubscriptionPlan } from '@/types/subscription';

// File size constants (in bytes)
const MB = 1024 * 1024;
const GB = 1024 * MB;

export const SUBSCRIPTION_PLANS: Record<string, SubscriptionPlan> = {
  free: {
    id: 'free',
    name: 'Free',
    description: 'Basic task management with essential features',
    price: {
      monthly: 0,
      yearly: 0,
    },
    stripePriceIds: {
      monthly: '', // No Stripe price for free tier
      yearly: '',
    },
    features: [
      'Basic task management',
      'Email notifications',
      'Limited file attachments (5MB per file)',
      'Basic analytics',
      'Up to 1 comment per task',
      'Files deleted after 30 days',
    ],
    limits: {
      maxFileSize: 5 * MB, // 5MB per file
      maxStorageTotal: 50 * MB, // 50MB total storage
      maxCommentsPerTask: 1,
      hasVoiceFeatures: false,
      hasAdvancedAnalytics: false,
      fileRetentionDays: 30, // Files deleted after 30 days
      notificationTypes: ['email'],
    },
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    description: 'Advanced task management with unlimited features',
    price: {
      monthly: 3.99,
      yearly: 39.99, // Save ~$8/year
    },
    stripePriceIds: {
      monthly: 'pro_monthly', // These will be replaced with actual Stripe price IDs
      yearly: 'pro_yearly',
    },
    features: [
      'Everything in Free',
      'Voice notes and voice-to-text',
      'Unlimited file attachments (25MB per file)',
      'Advanced analytics and insights',
      'Unlimited comments per task',
      'Email + SMS notifications',
      'Files kept for 1 year',
      'Priority support',
    ],
    limits: {
      maxFileSize: 25 * MB, // 25MB per file
      maxStorageTotal: 500 * MB, // 500MB total storage
      maxCommentsPerTask: -1, // Unlimited
      hasVoiceFeatures: true,
      hasAdvancedAnalytics: true,
      fileRetentionDays: 365, // Files kept for 1 year
      notificationTypes: ['email', 'sms'],
    },
    popular: true,
  },
};

export const TRIAL_DURATION_DAYS = 7;

export const getSubscriptionPlan = (tier: string): SubscriptionPlan => {
  return SUBSCRIPTION_PLANS[tier] || SUBSCRIPTION_PLANS.free;
};

export const isFeatureAvailable = (tier: string, feature: string): boolean => {
  const plan = getSubscriptionPlan(tier);
  
  switch (feature) {
    case 'voice_notes':
      return plan.limits.hasVoiceFeatures;
    case 'advanced_analytics':
      return plan.limits.hasAdvancedAnalytics;
    case 'unlimited_comments':
      return plan.limits.maxCommentsPerTask === -1;
    case 'sms_notifications':
      return plan.limits.notificationTypes.includes('sms');
    default:
      return true;
  }
};

export const getStorageLimit = (tier: string): number => {
  const plan = getSubscriptionPlan(tier);
  return plan.limits.maxStorageTotal;
};

export const getFileSizeLimit = (tier: string): number => {
  const plan = getSubscriptionPlan(tier);
  return plan.limits.maxFileSize;
};

export const getFileRetentionDays = (tier: string): number => {
  const plan = getSubscriptionPlan(tier);
  return plan.limits.fileRetentionDays;
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatPrice = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};
