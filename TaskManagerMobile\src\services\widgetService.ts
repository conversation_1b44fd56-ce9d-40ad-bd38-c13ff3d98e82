import { Task } from '../types/task';
import { format, parseISO, isToday, isTomorrow } from 'date-fns';

export interface WidgetData {
  todayTasks: Task[];
  upcomingTasks: Task[];
  completedToday: number;
  totalToday: number;
  overdueTasks: Task[];
}

export class WidgetService {
  private static instance: WidgetService;

  static getInstance(): WidgetService {
    if (!WidgetService.instance) {
      WidgetService.instance = new WidgetService();
    }
    return WidgetService.instance;
  }

  // Generate widget data for home screen widgets
  async generateWidgetData(tasks: Task[]): Promise<WidgetData> {
    const today = new Date();
    const todayStr = format(today, 'yyyy-MM-dd');

    const todayTasks = tasks.filter(task => task.date === todayStr);
    const completedToday = todayTasks.filter(task => task.status === 'completed').length;
    
    const upcomingTasks = tasks
      .filter(task => {
        const taskDate = parseISO(task.date);
        return isTomorrow(taskDate) || (taskDate > today && taskDate <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
      })
      .sort((a, b) => parseISO(a.date).getTime() - parseISO(b.date).getTime())
      .slice(0, 5);

    const overdueTasks = tasks.filter(task => {
      const taskDate = parseISO(task.date);
      return taskDate < today && task.status !== 'completed';
    });

    return {
      todayTasks: todayTasks.slice(0, 5), // Limit for widget display
      upcomingTasks,
      completedToday,
      totalToday: todayTasks.length,
      overdueTasks: overdueTasks.slice(0, 3)
    };
  }

  // Create small widget layout (4x2)
  generateSmallWidgetLayout(data: WidgetData): string {
    const progressPercentage = data.totalToday > 0 ? Math.round((data.completedToday / data.totalToday) * 100) : 0;
    
    return `
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 16px; border-radius: 12px; color: white; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
          <h3 style="margin: 0; font-size: 16px; font-weight: 600;">Tasks Today</h3>
          <div style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 8px; font-size: 12px;">
            ${data.completedToday}/${data.totalToday}
          </div>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); height: 4px; border-radius: 2px; margin-bottom: 12px;">
          <div style="background: white; height: 100%; width: ${progressPercentage}%; border-radius: 2px; transition: width 0.3s ease;"></div>
        </div>
        
        ${data.todayTasks.slice(0, 2).map(task => `
          <div style="display: flex; align-items: center; margin-bottom: 8px; font-size: 13px;">
            <div style="width: 6px; height: 6px; border-radius: 50%; background: ${this.getPriorityColor(task.priority)}; margin-right: 8px;"></div>
            <span style="opacity: ${task.status === 'completed' ? '0.7' : '1'}; text-decoration: ${task.status === 'completed' ? 'line-through' : 'none'};">
              ${task.title.length > 25 ? task.title.substring(0, 25) + '...' : task.title}
            </span>
          </div>
        `).join('')}
        
        ${data.overdueTasks.length > 0 ? `
          <div style="background: rgba(239, 68, 68, 0.2); padding: 6px 8px; border-radius: 6px; margin-top: 8px; font-size: 11px;">
            ⚠️ ${data.overdueTasks.length} overdue task${data.overdueTasks.length !== 1 ? 's' : ''}
          </div>
        ` : ''}
      </div>
    `;
  }

  private getPriorityColor(priority: string): string {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  }

  private formatDate(dateString: string): string {
    const date = parseISO(dateString);
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    return format(date, 'MMM d');
  }

  // Background refresh functionality
  async scheduleBackgroundRefresh(): Promise<void> {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await (registration as any).sync.register('background-task-refresh');
        console.log('Background refresh scheduled');
      } catch (error) {
        console.error('Failed to schedule background refresh:', error);
      }
    }
  }

  // Generate widget configuration for different sizes
  getWidgetConfiguration() {
    return {
      small: {
        name: 'Task Summary',
        description: 'Quick overview of today\'s tasks',
        size: '4x2',
        refreshInterval: 15 // minutes
      }
    };
  }
}