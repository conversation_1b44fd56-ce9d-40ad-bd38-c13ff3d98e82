import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface AssignmentEmailRequest {
  taskId: string;
  taskTitle: string;
  taskDescription: string;
  assignedToEmail: string;
  assignedByName: string;
  dueDate?: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    const { 
      taskId, 
      taskTitle, 
      taskDescription, 
      assignedToEmail, 
      assignedByName,
      dueDate 
    }: AssignmentEmailRequest = await req.json();

    if (!taskId || !taskTitle || !assignedToEmail || !assignedByName) {
      throw new Error("Missing required fields");
    }

    // Create assignment notification in database
    const { error: notificationError } = await supabaseClient
      .from('notifications')
      .insert({
        user_id: assignedToEmail, // We'll use email as user_id for now
        type: 'assignment',
        title: 'New Task Assignment',
        message: `You have been assigned a new task: ${taskTitle}`,
        data: {
          task_id: taskId,
          assigned_by: assignedByName,
          due_date: dueDate
        }
      });

    if (notificationError) {
      console.error('Error creating notification:', notificationError);
    }

    // Send email notification
    const emailResponse = await resend.emails.send({
      from: "TaskManager <<EMAIL>>",
      to: [assignedToEmail],
      subject: `New Task Assignment: ${taskTitle}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb; margin-bottom: 20px;">New Task Assignment</h1>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #334155; margin-top: 0;">${taskTitle}</h2>
            <p style="color: #64748b; line-height: 1.6;">${taskDescription}</p>
            
            <div style="margin-top: 15px;">
              <strong>Assigned by:</strong> ${assignedByName}<br>
              ${dueDate ? `<strong>Due Date:</strong> ${new Date(dueDate).toLocaleDateString()}<br>` : ''}
            </div>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${Deno.env.get('SITE_URL') || 'http://localhost:3000'}/" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 6px; display: inline-block;">
              View Task
            </a>
          </div>

          <p style="color: #64748b; font-size: 14px; text-align: center;">
            You received this email because you were assigned a task in TaskManager.
          </p>
        </div>
      `,
    });

    console.log("Assignment email sent successfully:", emailResponse);

    return new Response(JSON.stringify({ 
      success: true, 
      emailId: emailResponse.data?.id 
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-assignment-email function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);