/* Fixed layout styles */
html, body {
  overflow: hidden;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Enhanced scrollbar styling */
.panel-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.panel-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.panel-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.panel-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 6px;
  border: transparent;
}

.panel-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.8);
}

/* Improved scroll container behavior */
.panel-scrollbar {
  position: relative;
  height: calc(100% - 40px); /* Account for header */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 20px;
  scroll-behavior: smooth;
}

/* Enhanced spacing for empty states */
.flex-col.items-center.justify-center {
  padding: 2rem 0;
}

/* Ensure proper height calculation */
.scroll-content-wrapper {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ensure proper flex scrolling behavior */
.flex-scroll-col {
  display: flex;
  flex-direction: column;
  min-height: 0;
  flex: 1 1 0%;
}

/* Fix for flex layouts with scrolling children */
.flex-1 {
  min-height: 0;
}

/* Ensure proper height calculations for scrollable areas */
.h-full {
  height: 100%;
}

/* Ensure overflow is properly handled */
.overflow-y-auto {
  overflow-y: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

/* Custom scrollbar for the task and analytics panels */
.panel-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.panel-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.panel-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.panel-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 6px;
  border: transparent;
}

.panel-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.8);
}

/* Hide vertical scrollbar while keeping horizontal scrollbar */
.hide-vertical-scrollbar {
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

.hide-vertical-scrollbar::-webkit-scrollbar:vertical {
  display: none !important;
}

.hide-vertical-scrollbar::-webkit-scrollbar:horizontal {
  display: block !important;
}

/* For Firefox */
.hide-vertical-scrollbar {
  scrollbar-width: horizontal !important;
  scrollbar-height: thin !important;
}