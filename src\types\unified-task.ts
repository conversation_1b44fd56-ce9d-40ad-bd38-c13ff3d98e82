// Unified task types that work across both web and mobile platforms

export interface FileReference {
  key: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  lastModified?: number;
  url?: string; // Optional URL for stored files
}

export interface VoiceNote {
  url: string;
  duration: number; // in seconds
  created_at: string;
}

export interface Comment {
  id: string;
  text: string;
  author: string;
  timestamp: string;
}

export interface Task {
  id: string;
  user_id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed' | 'cancelled' | 'on-hold';
  date: string; // YYYY-MM-DD format
  due_date?: string; // YYYY-MM-DD format, optional due date
  category?: string;
  tags?: string[];
  location?: string;
  

  
  // Progress tracking
  completion_percentage?: number;
  estimated_hours?: number;
  actual_hours?: number;
  completed_at?: string;
  
  // Attachments and media
  attachments?: FileReference[];
  voice_notes?: VoiceNote[];
  voice_note_url?: string;
  voice_note_duration?: number;
  file_urls?: string[];
  links?: string[];
  
  // Metadata
  comments?: Comment[];
  created_at: string;
  updated_at: string;
  
  // Recurring tasks
  recurring_pattern?: {
    type: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    days_of_week?: number[];
    day_of_month?: number;
    end_date?: string;
    max_occurrences?: number;
  };
  recurring_task_id?: string;
  
  // Additional features
  reminder_settings?: {
    enabled: boolean;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    timing: {
      minutes_before?: number;
      hours_before?: number;
      days_before?: number;
      custom_times?: string[];
    };
  };
  weather_dependent?: boolean;
}



export interface TasksByDate {
  [date: string]: Task[];
}

export interface TaskFilter {
  status?: Task['status'][];
  priority?: Task['priority'][];
  category?: string[];

  date_range?: {
    start: string;
    end: string;
  };
  search?: string;
  tags?: string[];

  overdue_only?: boolean;
  has_voice_note?: boolean;
}

export interface TaskSort {
  field: 'created_at' | 'updated_at' | 'due_date' | 'priority' | 'title' | 'completion_percentage';
  direction: 'asc' | 'desc';
}

// Utility types for task creation and updates
export type CreateTaskInput = Omit<Task, 'id' | 'created_at' | 'updated_at' | 'user_id'> & {
  user_id?: string;
};

export type UpdateTaskInput = Partial<Omit<Task, 'id' | 'created_at' | 'user_id'>>;



// Legacy compatibility types
export type TaskInsert = CreateTaskInput;
export type TaskUpdate = UpdateTaskInput;