import { supabase } from '../lib/supabase';
import { Platform } from 'react-native';
import { getSubscriptionPlan, getFileSizeLimit, getStorageLimit } from '../config/subscription';

export interface FileAttachment {
  id: string;
  taskId: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  createdAt: string;
}

export class FileService {
  static async uploadFile(
    taskId: string,
    fileUri: string,
    fileName: string,
    fileType: string,
    fileSize: number,
    userId: string,
    subscriptionTier: string = 'free'
  ): Promise<string> {
    try {
      // Check subscription limits
      const plan = getSubscriptionPlan(subscriptionTier);
      const maxFileSize = plan.limits.maxFileSize;
      
      if (fileSize > maxFileSize) {
        throw new Error(`File size (${this.formatFileSize(fileSize)}) exceeds limit (${this.formatFileSize(maxFileSize)})`);
      }

      // Check total storage usage
      const storageUsage = await this.getStorageUsage(userId, subscriptionTier);
      if (storageUsage.totalBytes + fileSize > storageUsage.limitBytes) {
        throw new Error(`Upload would exceed storage limit. Available: ${this.formatFileSize(storageUsage.limitBytes - storageUsage.totalBytes)}`);
      }

      // Read file as blob
      const response = await fetch(fileUri);
      const blob = await response.blob();
      
      // Generate unique filename with user folder structure
      const fileExt = fileName.split('.').pop();
      const uniqueFileName = `${userId}/${taskId}/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${fileExt}`;
      
      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('task-attachments')
        .upload(uniqueFileName, blob, {
          contentType: fileType,
          upsert: false
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('task-attachments')
        .getPublicUrl(data.path);

      // Save attachment record
      const { data: attachment, error: dbError } = await supabase
        .from('file_attachments')
        .insert({
          user_id: userId,
          task_id: taskId,
          filename: fileName,
          file_type: fileType,
          file_size: fileSize,
          storage_path: data.path
        })
        .select()
        .single();

      if (dbError) throw dbError;

      return attachment.id;
    } catch (error) {
      console.error('Upload file error:', error);
      throw error;
    }
  }

  /**
   * Get storage usage for a user
   */
  static async getStorageUsage(userId: string, subscriptionTier: string = 'free'): Promise<{
    totalBytes: number;
    fileCount: number;
    limitBytes: number;
    canUpload: boolean;
  }> {
    try {
      const { data, error } = await supabase
        .from('file_attachments')
        .select('file_size')
        .eq('user_id', userId);

      if (error) throw error;

      const totalBytes = data.reduce((sum, file) => sum + file.file_size, 0);
      const fileCount = data.length;
      const limitBytes = getStorageLimit(subscriptionTier);

      return {
        totalBytes,
        fileCount,
        limitBytes,
        canUpload: totalBytes < limitBytes
      };
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return {
        totalBytes: 0,
        fileCount: 0,
        limitBytes: getStorageLimit(subscriptionTier),
        canUpload: true
      };
    }
  }

  static async getAttachments(taskId: string): Promise<FileAttachment[]> {
    try {
      const { data, error } = await supabase
        .from('file_attachments')
        .select('*')
        .eq('task_id', taskId)
        .order('uploaded_at', { ascending: false });

      if (error) throw error;

      return data?.map(file => ({
        id: file.id,
        taskId: file.task_id,
        fileName: file.filename,
        fileUrl: supabase.storage.from('task-attachments').getPublicUrl(file.storage_path).data.publicUrl,
        fileType: file.file_type,
        fileSize: file.file_size,
        createdAt: file.uploaded_at
      })) || [];
    } catch (error) {
      console.error('Get attachments error:', error);
      return [];
    }
  }

  static async deleteAttachment(attachmentId: string): Promise<void> {
    try {
      // First get the attachment to get the storage path
      const { data: attachment, error: fetchError } = await supabase
        .from('file_attachments')
        .select('storage_path')
        .eq('id', attachmentId)
        .single();

      if (fetchError) throw fetchError;

      if (attachment) {
        // Delete from storage
        await supabase.storage
          .from('task-attachments')
          .remove([attachment.storage_path]);
      }

      // Delete from database
      const { error } = await supabase
        .from('file_attachments')
        .delete()
        .eq('id', attachmentId);

      if (error) throw error;
    } catch (error) {
      console.error('Delete attachment error:', error);
      throw error;
    }
  }

  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static getFileIcon(fileType: string): string {
    const type = fileType.toLowerCase();
    if (type.includes('image')) return '🖼️';
    if (type.includes('pdf')) return '📄';
    if (type.includes('word')) return '📝';
    if (type.includes('excel') || type.includes('sheet')) return '📊';
    if (type.includes('video')) return '🎥';
    if (type.includes('audio')) return '🎵';
    return '📎';
  }
}