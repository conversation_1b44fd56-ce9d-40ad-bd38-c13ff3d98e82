import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Task } from '../types/task';
import { supabase } from '../lib/supabase';

interface TaskCardProps {
  task: Task;
  onPress: () => void;
  onRefresh: () => void;
}

const TaskCard: React.FC<TaskCardProps> = ({ task, onPress, onRefresh }) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#EF4444';
      case 'medium':
        return '#F59E0B';
      case 'low':
        return '#10B981';
      default:
        return '#6B7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#10B981';
      case 'in-progress':
        return '#F59E0B';
      case 'pending':
        return '#6B7280';
      case 'cancelled':
        return '#EF4444';
      case 'on-hold':
        return '#8B5CF6';
      default:
        return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'in-progress':
        return 'play-circle';
      case 'pending':
        return 'time';
      case 'cancelled':
        return 'close-circle';
      case 'on-hold':
        return 'pause-circle';
      default:
        return 'help-circle';
    }
  };

  const toggleTaskStatus = async (e: any) => {
    e.stopPropagation();
    
    const newStatus = task.status === 'completed' ? 'pending' : 'completed';
    const completedAt = newStatus === 'completed' ? new Date().toISOString() : null;
    
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ 
          status: newStatus,
          completed_at: completedAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      if (error) throw error;
      
      onRefresh();
    } catch (error) {
      console.error('Error updating task status:', error);
      Alert.alert('Error', 'Failed to update task status');
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        task.status === 'completed' && styles.completedContainer
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <Text
            style={[
              styles.title,
              task.status === 'completed' && styles.completedTitle
            ]}
            numberOfLines={2}
          >
            {task.title}
          </Text>
          <TouchableOpacity
            style={styles.statusButton}
            onPress={toggleTaskStatus}
          >
            <Ionicons
              name={getStatusIcon(task.status || 'pending')}
              size={24}
              color={getStatusColor(task.status || 'pending')}
            />
          </TouchableOpacity>
        </View>
        
        <View style={styles.metaRow}>
          <View style={styles.priorityBadge}>
            <View
              style={[
                styles.priorityDot,
                { backgroundColor: getPriorityColor(task.priority) }
              ]}
            />
            <Text style={styles.priorityText}>
              {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
            </Text>
          </View>
          
          <View style={styles.statusBadge}>
            <Text
              style={[
                styles.statusText,
                { color: getStatusColor(task.status || 'pending') }
              ]}
            >
              {(task.status || 'pending').replace('-', ' ').toUpperCase()}
            </Text>
          </View>
        </View>
      </View>

      {task.description && (
        <Text
          style={[
            styles.description,
            task.status === 'completed' && styles.completedDescription
          ]}
          numberOfLines={3}
        >
          {task.description}
        </Text>
      )}

      {(task.category || task.tags?.length || task.attachments?.length || task.comments?.length) && (
        <View style={styles.footer}>
          {task.category && (
            <View style={styles.categoryBadge}>
              <Ionicons name="folder-outline" size={12} color="#6B7280" />
              <Text style={styles.categoryText}>{task.category}</Text>
            </View>
          )}
          
          <View style={styles.indicators}>
            {task.attachments && Array.isArray(task.attachments) && task.attachments.length > 0 && (
              <View style={styles.indicator}>
                <Ionicons name="attach" size={14} color="#6B7280" />
                <Text style={styles.indicatorText}>{task.attachments.length}</Text>
              </View>
            )}
            
            {task.comments && Array.isArray(task.comments) && task.comments.length > 0 && (
              <View style={styles.indicator}>
                <Ionicons name="chatbubble-outline" size={14} color="#6B7280" />
                <Text style={styles.indicatorText}>{task.comments.length}</Text>
              </View>
            )}
            
            {task.voiceNotes && Array.isArray(task.voiceNotes) && task.voiceNotes.length > 0 && (
              <View style={styles.indicator}>
                <Ionicons name="mic-outline" size={14} color="#6B7280" />
                <Text style={styles.indicatorText}>{task.voiceNotes.length}</Text>
              </View>
            )}
            
            {task.tags && Array.isArray(task.tags) && task.tags.length > 0 && (
              <View style={styles.indicator}>
                <Ionicons name="pricetag-outline" size={14} color="#6B7280" />
                <Text style={styles.indicatorText}>{task.tags.length}</Text>
              </View>
            )}
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    borderLeftWidth: 3,
    borderLeftColor: '#E5E7EB',
  },
  completedContainer: {
    opacity: 0.7,
    borderLeftColor: '#10B981',
  },
  header: {
    marginBottom: 8,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginRight: 12,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  statusButton: {
    padding: 4,
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priorityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
  },
  priorityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  priorityText: {
    fontSize: 11,
    color: '#374151',
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '600',
  },
  description: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    marginBottom: 12,
  },
  completedDescription: {
    color: '#9CA3AF',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#EFF6FF',
    borderRadius: 8,
  },
  categoryText: {
    fontSize: 11,
    color: '#1D4ED8',
    marginLeft: 4,
    fontWeight: '500',
  },
  indicators: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  indicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  indicatorText: {
    fontSize: 11,
    color: '#6B7280',
  },
});

export default TaskCard;
