@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Premium Brand Colors - Modern blue gradient system */
    --background: 0 0% 100%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    /* Premium primary - Deep sophisticated blue */
    --primary: 217 91% 60%; /* Modern electric blue */
    --primary-foreground: 0 0% 100%;
    --primary-glow: 217 91% 70%; /* Lighter version for glows */
    
    /* Sophisticated secondary palette */
    --secondary: 215 25% 97%;
    --secondary-foreground: 215 25% 15%;

    --muted: 215 20% 96%;
    --muted-foreground: 215 15% 50%;

    --accent: 142 71% 45%; /* Premium green accent */
    --accent-foreground: 0 0% 100%;

    /* Enhanced status colors */
    --success: 142 71% 45%;
    --warning: 38 92% 50%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 20% 90%;
    --input: 215 20% 96%;
    --ring: 217 91% 60%;

    --radius: 0.75rem; /* More modern radius */

    /* Premium gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(217 91% 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(215 25% 97%), hsl(215 20% 94%));
    
    /* Scrollbar colors */
    --scrollbar-track: 215 20% 96%;
    --scrollbar-thumb: 215 15% 70%;
    --scrollbar-thumb-hover: 217 91% 60%;
    --gradient-accent: linear-gradient(135deg, hsl(142 71% 45%), hsl(142 71% 55%));
    
    /* Glass morphism effects */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-backdrop: blur(10px);
    
    /* Premium shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-glow: 0 0 0 1px rgba(217, 119, 255, 0.3), 0 0 25px rgba(217, 119, 255, 0.2);

    /* Sidebar premium styling */
    --sidebar-background: 215 25% 98%;
    --sidebar-foreground: 215 25% 20%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 20% 94%;
    --sidebar-accent-foreground: 215 25% 20%;
    --sidebar-border: 215 20% 88%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 215 28% 8%;
    --foreground: 215 15% 92%;

    --card: 215 28% 10%;
    --card-foreground: 215 15% 92%;

    --popover: 215 28% 10%;
    --popover-foreground: 215 15% 92%;

    --primary: 217 91% 65%;
    --primary-foreground: 215 28% 8%;
    --primary-glow: 217 91% 75%;

    --secondary: 215 25% 15%;
    --secondary-foreground: 215 15% 85%;

    --muted: 215 25% 15%;
    --muted-foreground: 215 15% 65%;

    --accent: 142 71% 50%;
    --accent-foreground: 215 28% 8%;

    --success: 142 71% 50%;
    --warning: 38 92% 55%;
    --destructive: 0 84% 65%;
    --destructive-foreground: 215 15% 92%;

    --border: 215 25% 18%;
    --input: 215 25% 15%;
    --ring: 217 91% 65%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 65%), hsl(217 91% 75%));
    --gradient-secondary: linear-gradient(135deg, hsl(215 25% 15%), hsl(215 25% 18%));
    --gradient-accent: linear-gradient(135deg, hsl(142 71% 50%), hsl(142 71% 60%));
    
    /* Dark glass morphism */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    
    /* Dark shadows with glow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 0 1px rgba(217, 119, 255, 0.3), 0 0 25px rgba(217, 119, 255, 0.15);

    --sidebar-background: 215 28% 9%;
    --sidebar-foreground: 215 15% 85%;
    --sidebar-primary: 217 91% 65%;
    --sidebar-primary-foreground: 215 28% 8%;
    --sidebar-accent: 215 25% 15%;
    --sidebar-accent-foreground: 215 15% 85%;
    --sidebar-border: 215 25% 18%;
    --sidebar-ring: 217 91% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv11', 'ss01';
    font-variant-numeric: tabular-nums;
  }

  /* Premium typography */
  .text-premium {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv11', 'ss01';
    letter-spacing: -0.01em;
  }

  .text-mono {
    font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Consolas', monospace;
  }

  /* Enhanced cursor styling */
  input, textarea {
    caret-color: hsl(var(--primary));
  }
}

/* Premium component styling */
@layer components {
  /* Glass morphism card */
  .glass-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
  }

  /* Premium buttons */
  .btn-premium {
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: var(--radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    letter-spacing: -0.01em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
  }

  .btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-premium:hover::before {
    opacity: 1;
  }

  .btn-premium:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  /* Premium floating elements */
  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Enhanced calendar styling */
  .calendar-tile {
    position: relative;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--radius);
  }

  .calendar-tile:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
  }


  /* Premium task cards */
  .task-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
  }

  .task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .task-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: hsl(var(--primary) / 0.3);
  }

  .task-card:hover::before {
    opacity: 1;
  }

  /* Premium form inputs */
  .input-premium {
    background: hsl(var(--input));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
  }

  .input-premium:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
    outline: none;
  }

  /* Modern loading states */
  .loading-shimmer {
    background: linear-gradient(90deg, 
      hsl(var(--muted)) 25%, 
      hsl(var(--muted-foreground) / 0.1) 50%, 
      hsl(var(--muted)) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Premium status indicators */
  .status-indicator {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    letter-spacing: 0.025em;
  }

  .status-indicator::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  .status-success {
    background: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
  }

  .status-success::before {
    background: hsl(var(--success));
  }

  .status-warning {
    background: hsl(var(--warning) / 0.1);
    color: hsl(var(--warning));
  }

  .status-warning::before {
    background: hsl(var(--warning));
  }

  .status-error {
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive));
  }

  .status-error::before {
    background: hsl(var(--destructive));
  }

  /* Enhanced mobile optimizations */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .mobile-smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }

  .mobile-optimized {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced focus styles */
  .focus-premium {
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .focus-premium:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);
  }

  /* Premium animations */
  .animate-slide-up {
    animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slide-down {
    animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      transform: translateY(30px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Animation control - disable all animations when no-animations class is present */
  .no-animations *,
  .no-animations *::before,
  .no-animations *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    transition-delay: 0ms !important;
    scroll-behavior: auto !important;
  }

  /* Responsive design utilities */
  .container-premium {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-premium {
      padding: 0 2rem;
    }
  }

  @media (min-width: 1024px) {
    .container-premium {
      padding: 0 3rem;
    }
  }

  /* Mobile-first responsive typography */
  .text-responsive-sm {
    font-size: clamp(0.75rem, 2vw, 0.875rem);
  }

  .text-responsive-base {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
  }

  .text-responsive-lg {
    font-size: clamp(1rem, 3vw, 1.125rem);
  }

  .text-responsive-xl {
    font-size: clamp(1.125rem, 3.5vw, 1.25rem);
  }

  .text-responsive-2xl {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
  }

  .text-responsive-3xl {
    font-size: clamp(1.5rem, 5vw, 1.875rem);
  }

  /* Mobile-friendly touch targets */
  .btn-touch {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Safe area handling for mobile devices */
  .safe-area-padding {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .safe-area-padding-x {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  .safe-area-padding-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  /* Enhanced landscape/portrait optimizations */
  @media (orientation: landscape) and (max-width: 768px) {
    .mobile-landscape-optimized {
      height: 100vh;
      height: 100dvh;
    }
    
    .mobile-landscape-content {
      display: flex;
      flex-direction: row;
      height: 100%;
      overflow: hidden;
    }
    
    .mobile-landscape-sidebar {
      width: 280px;
      flex-shrink: 0;
      border-right: 1px solid hsl(var(--border));
    }
    
    .mobile-landscape-main {
      flex: 1;
      overflow-y: auto;
      height: 100%;
    }
  }

  /* Virtual keyboard optimizations */
  .keyboard-aware {
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .keyboard-visible {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height */
  }

  /* Premium modal styling */
  .modal-premium {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border-radius: calc(var(--radius) * 2);
    box-shadow: var(--shadow-lg);
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes modalSlideIn {
    from {
      transform: scale(0.95) translateY(20px);
      opacity: 0;
    }
    to {
      transform: scale(1) translateY(0);
      opacity: 1;
    }
  }

  /* Enhanced scrollbar styles for better visibility */
  .scrollbar-visible {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--scrollbar-thumb)) hsl(var(--scrollbar-track));
  }

  .scrollbar-visible::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-visible::-webkit-scrollbar-track {
    background: hsl(var(--scrollbar-track));
    border-radius: 4px;
  }

  .scrollbar-visible::-webkit-scrollbar-thumb {
    background: hsl(var(--scrollbar-thumb));
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .scrollbar-visible::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--scrollbar-thumb-hover));
  }

  /* Apply to ScrollArea components */
  [data-radix-scroll-area-viewport] {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--scrollbar-thumb)) hsl(var(--scrollbar-track));
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
    background: hsl(var(--scrollbar-track));
    border-radius: 4px;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
    background: hsl(var(--scrollbar-thumb));
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--scrollbar-thumb-hover));
  }

  /* Enhanced dropdowns */
  .dropdown-premium {
    background: hsl(var(--popover));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    z-index: 50;
    animation: dropdownSlideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes dropdownSlideIn {
    from {
      transform: scale(0.95) translateY(-10px);
      opacity: 0;
    }
    to {
      transform: scale(1) translateY(0);
      opacity: 1;
    }
  }
}
