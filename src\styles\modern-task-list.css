/* Modern Task List Enhancements - Updated for Sleek Design */

/* Enhanced animations and transitions */
.task-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.task-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.08);
}

/* Enhanced glass morphism effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced scrollbar with modern styling */
.panel-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.panel-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.panel-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.panel-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}

.dark .panel-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark .panel-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark .panel-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Enhanced priority badge animations */
.priority-badge {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.priority-badge:hover {
  transform: scale(1.08) translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced status badge glow effects */
.status-badge {
  position: relative;
  overflow: hidden;
}

.status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.status-badge:hover::before {
  left: 100%;
}

/* Enhanced focus states */
.modern-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 0 0 6px rgba(59, 130, 246, 0.1);
}

/* Enhanced gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects for interactive elements */
.interactive-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced modern button styles */
.modern-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 16px;
  padding: 14px 28px;
  color: white;
  font-weight: 700;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modern-button:hover::before {
  left: 100%;
}

.modern-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
}

/* Enhanced card hover effects */
.card-hover {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 35px 60px -15px rgba(0, 0, 0, 0.25);
}

/* Enhanced modern typography */
.modern-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

/* Enhanced subtle border effects */
.border-subtle {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: border-color 0.3s ease;
}

.border-subtle:hover {
  border-color: rgba(0, 0, 0, 0.15);
}

.dark .border-subtle {
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.dark .border-subtle:hover {
  border-color: rgba(255, 255, 255, 0.15);
}

/* Enhanced loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg, #f8fafc 25%, #e2e8f0 50%, #f8fafc 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced modern spacing */
.spacing-modern > * + * {
  margin-top: 1.5rem;
}

/* Micro-interactions */
.micro-interaction {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.micro-interaction:hover {
  transform: scale(1.05);
}

/* Enhanced responsive improvements */
@media (max-width: 768px) {
  .task-card {
    margin: 0.75rem;
    border-radius: 20px;
  }
  
  .glass-effect {
    backdrop-filter: none;
    background: rgba(255, 255, 255, 0.95);
  }
  
  .modern-button {
    padding: 12px 24px;
    font-size: 13px;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .task-card {
    background: rgba(30, 41, 59, 0.8);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus ring enhancements */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5), 0 0 0 4px rgba(99, 102, 241, 0.1);
}

/* Text selection styling */
::selection {
  background: rgba(99, 102, 241, 0.2);
}

.dark ::selection {
  background: rgba(99, 102, 241, 0.3);
}