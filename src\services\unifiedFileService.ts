import { supabase } from '@/integrations/supabase/client';
import { getSubscriptionPlan, getFileSizeLimit, getStorageLimit } from '@/config/subscription';
import { FileReference } from '@/types/unified-task';

export interface FileUploadResult {
  id: string;
  url: string;
  path: string;
}

export interface StorageUsage {
  totalBytes: number;
  fileCount: number;
  limitBytes: number;
  canUpload: boolean;
}

export class UnifiedFileService {
  private static BUCKET_NAME = 'task-attachments';

  /**
   * Upload file to Supabase Storage with subscription limits
   */
  static async uploadFile(
    file: File, 
    taskId: string, 
    userId: string,
    subscriptionTier: string = 'free'
  ): Promise<FileUploadResult> {
    try {
      // Check subscription limits
      const plan = getSubscriptionPlan(subscriptionTier);
      const maxFileSize = plan.limits.maxFileSize;
      
      if (file.size > maxFileSize) {
        throw new Error(`File size (${this.formatFileSize(file.size)}) exceeds limit (${this.formatFileSize(maxFileSize)})`);
      }

      // Check total storage usage
      const storageUsage = await this.getStorageUsage(userId, subscriptionTier);
      if (storageUsage.totalBytes + file.size > storageUsage.limitBytes) {
        throw new Error(`Upload would exceed storage limit. Available: ${this.formatFileSize(storageUsage.limitBytes - storageUsage.totalBytes)}`);
      }

      // Generate unique file path
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/${taskId}/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${fileExt}`;

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          contentType: file.type,
          upsert: false
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(data.path);

      // Save file metadata to database
      const { data: fileRecord, error: dbError } = await supabase
        .from('file_attachments')
        .insert({
          user_id: userId,
          task_id: taskId,
          filename: file.name,
          file_type: file.type,
          file_size: file.size,
          storage_path: data.path
        })
        .select()
        .single();

      if (dbError) throw dbError;

      return {
        id: fileRecord.id,
        url: publicUrl,
        path: data.path
      };
    } catch (error) {
      console.error('File upload error:', error);
      throw error;
    }
  }

  /**
   * Get files for a task
   */
  static async getTaskFiles(taskId: string): Promise<FileReference[]> {
    try {
      const { data, error } = await supabase
        .from('file_attachments')
        .select('*')
        .eq('task_id', taskId)
        .order('uploaded_at', { ascending: false });

      if (error) throw error;

      return data.map(file => ({
        key: file.id,
        name: file.filename,
        type: file.file_type,
        size: file.file_size,
        uploadedAt: file.uploaded_at,
        url: supabase.storage.from(this.BUCKET_NAME).getPublicUrl(file.storage_path).data.publicUrl
      }));
    } catch (error) {
      console.error('Error fetching task files:', error);
      return [];
    }
  }

  /**
   * Delete a file
   */
  static async deleteFile(fileId: string): Promise<void> {
    try {
      // Get file info first
      const { data: fileInfo, error: fetchError } = await supabase
        .from('file_attachments')
        .select('storage_path')
        .eq('id', fileId)
        .single();

      if (fetchError) throw fetchError;

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([fileInfo.storage_path]);

      if (storageError) throw storageError;

      // Delete from database
      const { error: dbError } = await supabase
        .from('file_attachments')
        .delete()
        .eq('id', fileId);

      if (dbError) throw dbError;
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }

  /**
   * Get storage usage for a user
   */
  static async getStorageUsage(userId: string, subscriptionTier: string = 'free'): Promise<StorageUsage> {
    try {
      const { data, error } = await supabase
        .from('file_attachments')
        .select('file_size')
        .eq('user_id', userId);

      if (error) throw error;

      const totalBytes = data.reduce((sum, file) => sum + file.file_size, 0);
      const fileCount = data.length;
      const limitBytes = getStorageLimit(subscriptionTier);

      return {
        totalBytes,
        fileCount,
        limitBytes,
        canUpload: totalBytes < limitBytes
      };
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return {
        totalBytes: 0,
        fileCount: 0,
        limitBytes: getStorageLimit(subscriptionTier),
        canUpload: true
      };
    }
  }

  /**
   * Check if user can upload a file
   */
  static async canUploadFile(
    file: File, 
    userId: string, 
    subscriptionTier: string = 'free'
  ): Promise<{ canUpload: boolean; reason?: string }> {
    try {
      const plan = getSubscriptionPlan(subscriptionTier);
      
      // Check file size
      if (file.size > plan.limits.maxFileSize) {
        return {
          canUpload: false,
          reason: `File size (${this.formatFileSize(file.size)}) exceeds limit (${this.formatFileSize(plan.limits.maxFileSize)})`
        };
      }

      // Check storage limit
      const storageUsage = await this.getStorageUsage(userId, subscriptionTier);
      if (storageUsage.totalBytes + file.size > storageUsage.limitBytes) {
        return {
          canUpload: false,
          reason: `Upload would exceed storage limit. Available: ${this.formatFileSize(storageUsage.limitBytes - storageUsage.totalBytes)}`
        };
      }

      return { canUpload: true };
    } catch (error) {
      console.error('Error checking upload eligibility:', error);
      return { canUpload: false, reason: 'Error checking upload eligibility' };
    }
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file type icon
   */
  static getFileIcon(fileType: string): string {
    const type = fileType.toLowerCase();
    if (type.includes('image')) return '🖼️';
    if (type.includes('pdf')) return '📄';
    if (type.includes('word')) return '📝';
    if (type.includes('excel') || type.includes('sheet')) return '📊';
    if (type.includes('video')) return '🎥';
    if (type.includes('audio')) return '🎵';
    return '📎';
  }
}