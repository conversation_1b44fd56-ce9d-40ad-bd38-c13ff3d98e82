import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import ExportService, { ExportOptions } from '../services/exportService';
import { Task } from '../types/task';

interface ExportModalProps {
  visible: boolean;
  onClose: () => void;
  tasks: Task[];
  analytics?: {
    completionRate: number;
    tasksByStatus: Record<string, number>;
    tasksByPriority: Record<string, number>;
    tasksByCategory: Record<string, number>;
  };
}

export default function ExportModal({ visible, onClose, tasks, analytics }: ExportModalProps) {
  const [format, setFormat] = useState<'pdf' | 'csv' | 'json'>('csv');
  const [includeCompleted, setIncludeCompleted] = useState(true);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<string[]>([]);
  const [exporting, setExporting] = useState(false);

  const allStatuses = ['pending', 'in-progress', 'completed', 'cancelled', 'on-hold'];
  const allPriorities = ['low', 'medium', 'high'];

  const handleExport = async () => {
    setExporting(true);
    try {
      // Generate comprehensive analytics data
      const analyticsData = ExportService.generateAnalyticsData(tasks, 'custom');
      
      // Configure export options with enhanced features
      const options: ExportOptions = {
        format,
        includeCompleted,
        statusFilter: selectedStatuses.length > 0 ? selectedStatuses : undefined,
        priorityFilter: selectedPriorities.length > 0 ? selectedPriorities : undefined,
        includeAnalytics: true, // Always include analytics for comprehensive exports
      };

      // Use the enhanced export and share method
      const success = await ExportService.exportAndShare(
        tasks,
        options,
        analyticsData
      );

      if (success) {
        console.log('Export completed successfully');
        onClose();
      } else {
        console.error('Export failed - sharing not available or error occurred');
      }
    } catch (error) {
      console.error('Export error:', error);
    } finally {
      setExporting(false);
    }
  };

  const toggleStatus = (status: string) => {
    setSelectedStatuses(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const togglePriority = (priority: string) => {
    setSelectedPriorities(prev =>
      prev.includes(priority)
        ? prev.filter(p => p !== priority)
        : [...prev, priority]
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Export Tasks</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* Format Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Export Format</Text>
              <View style={styles.formatButtons}>
                {(['csv', 'json', 'pdf'] as const).map((f) => (
                  <TouchableOpacity
                    key={f}
                    style={[
                      styles.formatButton,
                      format === f && styles.selectedFormat,
                    ]}
                    onPress={() => setFormat(f)}
                  >
                    <Text style={[
                      styles.formatText,
                      format === f && styles.selectedFormatText,
                    ]}>
                      {f.toUpperCase()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Filters */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Filters</Text>
              
              <View style={styles.filterRow}>
                <Text style={styles.filterLabel}>Include Completed Tasks</Text>
                <Switch
                  value={includeCompleted}
                  onValueChange={setIncludeCompleted}
                  trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
                  thumbColor={includeCompleted ? '#ffffff' : '#ffffff'}
                />
              </View>

              <Text style={styles.filterSubtitle}>Status</Text>
              <View style={styles.filterOptions}>
                {allStatuses.map((status) => (
                  <TouchableOpacity
                    key={status}
                    style={[
                      styles.filterOption,
                      selectedStatuses.includes(status) && styles.selectedFilter,
                    ]}
                    onPress={() => toggleStatus(status)}
                  >
                    <Text style={[
                      styles.filterOptionText,
                      selectedStatuses.includes(status) && styles.selectedFilterText,
                    ]}>
                      {status.replace('-', ' ').toUpperCase()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <Text style={styles.filterSubtitle}>Priority</Text>
              <View style={styles.filterOptions}>
                {allPriorities.map((priority) => (
                  <TouchableOpacity
                    key={priority}
                    style={[
                      styles.filterOption,
                      selectedPriorities.includes(priority) && styles.selectedFilter,
                    ]}
                    onPress={() => togglePriority(priority)}
                  >
                    <Text style={[
                      styles.filterOptionText,
                      selectedPriorities.includes(priority) && styles.selectedFilterText,
                    ]}>
                      {priority.toUpperCase()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Summary */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Summary</Text>
              <Text style={styles.summaryText}>
                {tasks.length} total tasks
                {selectedStatuses.length > 0 && ` • ${selectedStatuses.length} status filters`}
                {selectedPriorities.length > 0 && ` • ${selectedPriorities.length} priority filters`}
              </Text>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.exportButton]}
              onPress={handleExport}
              disabled={exporting}
            >
              <Text style={styles.exportButtonText}>
                {exporting ? 'Exporting...' : 'Export'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  content: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  formatButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  formatButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d5db',
    alignItems: 'center',
  },
  selectedFormat: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  formatText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  selectedFormatText: {
    color: '#ffffff',
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  filterLabel: {
    fontSize: 14,
    color: '#1f2937',
  },
  filterSubtitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  selectedFilter: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  filterOptionText: {
    fontSize: 12,
    color: '#6b7280',
  },
  selectedFilterText: {
    color: '#ffffff',
  },
  summaryText: {
    fontSize: 14,
    color: '#6b7280',
  },
  footer: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f3f4f6',
  },
  cancelButtonText: {
    color: '#6b7280',
    fontSize: 16,
    fontWeight: '600',
  },
  exportButton: {
    backgroundColor: '#10b981',
  },
  exportButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});