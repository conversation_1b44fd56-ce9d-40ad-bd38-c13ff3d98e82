import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

interface OfflineTask {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  retryCount: number;
}

export function useOffline() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineQueue, setOfflineQueue] = useState<OfflineTask[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    // Load offline queue from localStorage
    const savedQueue = localStorage.getItem('offline-task-queue');
    if (savedQueue) {
      try {
        setOfflineQueue(JSON.parse(savedQueue));
      } catch (error) {
        console.error('Error loading offline queue:', error);
      }
    }

    const handleOnline = () => {
      setIsOnline(true);
      toast({
        title: 'Back Online',
        description: 'Syncing your offline changes...',
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast({
        title: 'You\'re Offline',
        description: 'Changes will be saved locally and synced when back online',
        variant: 'destructive',
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  // Save queue to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('offline-task-queue', JSON.stringify(offlineQueue));
  }, [offlineQueue]);

  const addToOfflineQueue = useCallback((task: Omit<OfflineTask, 'timestamp' | 'retryCount'>) => {
    const offlineTask: OfflineTask = {
      ...task,
      timestamp: Date.now(),
      retryCount: 0,
    };

    setOfflineQueue(prev => [...prev, offlineTask]);
    
    toast({
      title: 'Saved Offline',
      description: 'Your changes will sync when you\'re back online',
    });
  }, [toast]);

  const processOfflineQueue = useCallback(async (syncFunction: (task: OfflineTask) => Promise<void>) => {
    if (!isOnline || offlineQueue.length === 0) return;

    const queue = [...offlineQueue];
    const failedTasks: OfflineTask[] = [];

    for (const task of queue) {
      try {
        await syncFunction(task);
        // Remove successful task from queue
        setOfflineQueue(prev => prev.filter(t => t.id !== task.id));
      } catch (error) {
        console.error('Error syncing task:', error);
        
        // Increment retry count
        const updatedTask = { ...task, retryCount: task.retryCount + 1 };
        
        // If retry count exceeds limit, move to failed tasks
        if (updatedTask.retryCount >= 3) {
          failedTasks.push(updatedTask);
        } else {
          // Keep in queue for retry
          setOfflineQueue(prev => prev.map(t => t.id === task.id ? updatedTask : t));
        }
      }
    }

    // Show results
    if (queue.length > failedTasks.length) {
      toast({
        title: 'Sync Complete',
        description: `${queue.length - failedTasks.length} changes synced successfully`,
      });
    }

    if (failedTasks.length > 0) {
      toast({
        title: 'Some Changes Failed to Sync',
        description: `${failedTasks.length} changes couldn't be synced. Please try again.`,
        variant: 'destructive',
      });
    }
  }, [isOnline, offlineQueue, toast]);

  const clearOfflineQueue = useCallback(() => {
    setOfflineQueue([]);
    localStorage.removeItem('offline-task-queue');
  }, []);

  const getOfflineData = useCallback((key: string) => {
    try {
      const data = localStorage.getItem(`offline-${key}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error reading offline data:', error);
      return null;
    }
  }, []);

  const setOfflineData = useCallback((key: string, data: any) => {
    try {
      localStorage.setItem(`offline-${key}`, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  }, []);

  const removeOfflineData = useCallback((key: string) => {
    localStorage.removeItem(`offline-${key}`);
  }, []);

  return {
    isOnline,
    offlineQueue,
    addToOfflineQueue,
    processOfflineQueue,
    clearOfflineQueue,
    getOfflineData,
    setOfflineData,
    removeOfflineData,
  };
}

// Hook for offline-first data management
export function useOfflineData<T>(key: string, initialData: T) {
  const [data, setData] = useState<T>(initialData);
  const [isLoading, setIsLoading] = useState(true);
  const { isOnline, getOfflineData, setOfflineData } = useOffline();

  useEffect(() => {
    // Load data from offline storage on mount
    const offlineData = getOfflineData(key);
    if (offlineData) {
      setData(offlineData);
    }
    setIsLoading(false);
  }, [key, getOfflineData]);

  const updateData = useCallback((newData: T | ((prev: T) => T)) => {
    setData(prev => {
      const updated = typeof newData === 'function' ? (newData as Function)(prev) : newData;
      setOfflineData(key, updated);
      return updated;
    });
  }, [key, setOfflineData]);

  const syncData = useCallback(async (syncFunction: (data: T) => Promise<T>) => {
    if (!isOnline) return data;

    try {
      const syncedData = await syncFunction(data);
      updateData(syncedData);
      return syncedData;
    } catch (error) {
      console.error('Error syncing data:', error);
      return data;
    }
  }, [isOnline, data, updateData]);

  return {
    data,
    updateData,
    syncData,
    isLoading,
    isOnline,
  };
}
