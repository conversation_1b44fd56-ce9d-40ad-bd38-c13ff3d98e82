import { UserProfile, ProfileData } from '../types';

export abstract class BaseUserService {
  abstract getUserProfile(userId: string): Promise<UserProfile | null>;
  abstract updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile>;
  abstract deleteUserProfile(userId: string): Promise<void>;
  abstract syncProfile(userData: any): Promise<ProfileData | null>;

  // Shared business logic
  validateUserProfile(profile: Partial<UserProfile>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (profile.email && !this.isValidEmail(profile.email)) {
      errors.push('Invalid email format');
    }

    if (profile.timezone && !this.isValidTimezone(profile.timezone)) {
      errors.push('Invalid timezone');
    }

    if (profile.work_hours) {
      if (!this.isValidWorkHours(profile.work_hours)) {
        errors.push('Invalid work hours configuration');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  getDisplayName(profile: UserProfile): string {
    if (profile.full_name) return profile.full_name;
    if (profile.first_name && profile.last_name) {
      return `${profile.first_name} ${profile.last_name}`;
    }
    if (profile.first_name) return profile.first_name;
    return profile.email.split('@')[0];
  }

  getUserInitials(profile: UserProfile): string {
    const name = this.getDisplayName(profile);
    const parts = name.split(' ');
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
    }
    return name.slice(0, 2).toUpperCase();
  }

  isWorkingHours(profile: UserProfile, date: Date = new Date()): boolean {
    const day = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const time = date.toTimeString().slice(0, 5); // HH:MM format

    if (!profile.work_hours.days.includes(day)) {
      return false;
    }

    return time >= profile.work_hours.start && time <= profile.work_hours.end;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }

  private isValidWorkHours(workHours: UserProfile['work_hours']): boolean {
    if (!workHours.start || !workHours.end) return false;
    
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(workHours.start) || !timeRegex.test(workHours.end)) {
      return false;
    }

    if (!Array.isArray(workHours.days) || workHours.days.length === 0) {
      return false;
    }

    return workHours.days.every(day => day >= 0 && day <= 6);
  }
}