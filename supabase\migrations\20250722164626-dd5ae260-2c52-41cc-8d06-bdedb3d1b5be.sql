-- Enable RLS on all tables that have policies but <PERSON><PERSON> disabled
ALTER TABLE public.file_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Drop and recreate the task_analytics view without SECURITY DEFINER
-- to resolve the security definer view issue
DROP VIEW IF EXISTS public.task_analytics;

-- Recreate as a regular view (not SECURITY DEFINER)
CREATE VIEW public.task_analytics AS
SELECT 
    t.user_id,
    COUNT(*) as total_tasks,
    COUNT(*) FILTER (WHERE t.status = 'completed') as completed_tasks,
    COUNT(*) FILTER (WHERE t.status = 'pending') as pending_tasks,
    COUNT(*) FILTER (WHERE t.status = 'in-progress') as in_progress_tasks,
    COUNT(*) FILTER (WHERE t.status = 'on-hold') as on_hold_tasks,
    COUNT(*) FILTER (WHERE t.status = 'cancelled') as cancelled_tasks,
    COUNT(*) FILTER (WHERE t.priority = 'high') as high_priority_tasks,
    COUNT(*) FILTER (WHERE t.assigned_to IS NOT NULL) as assigned_tasks,
    COUNT(*) FILTER (WHERE t.due_date < CURRENT_DATE AND t.status != 'completed') as overdue_tasks,
    SUM(t.estimated_hours) as total_estimated_hours,
    SUM(t.actual_hours) as total_actual_hours,
    AVG(t.completion_percentage) as avg_completion_percentage
FROM public.tasks t
GROUP BY t.user_id;