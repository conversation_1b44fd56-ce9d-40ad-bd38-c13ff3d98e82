// Unified store creation utility with persistence and caching
import { StateCreator, create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Platform detection
const isReactNative = typeof window === 'undefined' || !window.localStorage;

// Storage interface for cross-platform persistence
interface UnifiedStorage {
  getItem: (name: string) => string | null | Promise<string | null>;
  setItem: (name: string, value: string) => void | Promise<void>;
  removeItem: (name: string) => void | Promise<void>;
}

// Web storage implementation
const webStorage: UnifiedStorage = {
  getItem: (name: string) => localStorage.getItem(name),
  setItem: (name: string, value: string) => localStorage.setItem(name, value),
  removeItem: (name: string) => localStorage.removeItem(name),
};

// React Native AsyncStorage implementation (imported dynamically)
let asyncStorage: UnifiedStorage | null = null;

if (isReactNative) {
  try {
    // Dynamic import for React Native - only in actual RN environment
    const AsyncStorage = require('@react-native-async-storage/async-storage');
    asyncStorage = {
      getItem: AsyncStorage.default?.getItem || AsyncStorage.getItem,
      setItem: AsyncStorage.default?.setItem || AsyncStorage.setItem,
      removeItem: AsyncStorage.default?.removeItem || AsyncStorage.removeItem,
    };
  } catch (error) {
    console.warn('AsyncStorage not available, using memory storage');
  }
}

// Fallback memory storage for testing/development
const memoryStorage: UnifiedStorage = (() => {
  const store = new Map<string, string>();
  return {
    getItem: (name: string) => store.get(name) || null,
    setItem: (name: string, value: string) => {
      store.set(name, value);
    },
    removeItem: (name: string) => {
      store.delete(name);
    },
  };
})();

// Get appropriate storage based on platform
const getStorage = (): UnifiedStorage => {
  if (isReactNative && asyncStorage) return asyncStorage;
  if (!isReactNative && typeof window !== 'undefined') return webStorage;
  return memoryStorage;
};

// Unified store configuration options
export interface UnifiedStoreConfig<T> {
  name: string;
  version?: number;
  migrate?: (persistedState: unknown, version: number) => T;
  partialize?: (state: T) => Partial<T>;
  skipHydration?: boolean;
  enableDevtools?: boolean;
  enableImmer?: boolean;
  enableSubscriptions?: boolean;
}

// Cache management interface
export interface CacheConfig {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of cached items
  persistent?: boolean; // Whether to persist cache across sessions
}

// Cache manager for efficient data storage
export class UnifiedCacheManager<T = any> {
  private cache = new Map<string, { data: T; timestamp: number; ttl: number }>();
  private config: Required<CacheConfig>;
  private storage: UnifiedStorage;

  constructor(config: CacheConfig = {}) {
    this.config = {
      ttl: config.ttl || 5 * 60 * 1000, // 5 minutes default
      maxSize: config.maxSize || 100,
      persistent: config.persistent ?? true,
    };
    this.storage = getStorage();
    
    if (this.config.persistent) {
      this.loadFromStorage();
    }
  }

  async set(key: string, data: T, customTtl?: number): Promise<void> {
    const ttl = customTtl || this.config.ttl;
    const item = {
      data,
      timestamp: Date.now(),
      ttl,
    };

    // Remove expired items if cache is full
    if (this.cache.size >= this.config.maxSize) {
      this.cleanup();
    }

    this.cache.set(key, item);

    if (this.config.persistent) {
      await this.persistToStorage();
    }
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    this.cache.delete(key);
    if (this.config.persistent) {
      this.persistToStorage();
    }
  }

  clear(): void {
    this.cache.clear();
    if (this.config.persistent) {
      this.persistToStorage();
    }
  }

  private cleanup(): void {
    const now = Date.now();
    const expired: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        expired.push(key);
      }
    }

    expired.forEach(key => this.cache.delete(key));

    // If still full, remove oldest items
    if (this.cache.size >= this.config.maxSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, Math.floor(this.config.maxSize * 0.2));
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }

  private async loadFromStorage(): Promise<void> {
    try {
      const stored = await this.storage.getItem('unified-cache');
      if (stored) {
        const parsed = JSON.parse(stored);
        this.cache = new Map(parsed);
        this.cleanup(); // Clean up expired items on load
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }

  private async persistToStorage(): Promise<void> {
    try {
      const serialized = JSON.stringify(Array.from(this.cache.entries()));
      await this.storage.setItem('unified-cache', serialized);
    } catch (error) {
      console.warn('Failed to persist cache to storage:', error);
    }
  }
}

// Global cache instance
export const globalCache = new UnifiedCacheManager({
  ttl: 10 * 60 * 1000, // 10 minutes
  maxSize: 200,
  persistent: true,
});

// Enhanced store creator with unified patterns (simplified without complex middleware chains)
export function createUnifiedStore<T>(
  initializer: StateCreator<T, [], [], T>,
  config: UnifiedStoreConfig<T>
) {
  // Apply persistence middleware directly
  const persistedStore = persist(
    initializer,
    {
      name: config.name,
      version: config.version || 1,
      migrate: config.migrate,
      partialize: config.partialize,
      skipHydration: config.skipHydration,
      storage: {
        getItem: async (name: string) => {
          const storage = getStorage();
          const item = await storage.getItem(name);
          return item ? JSON.parse(item) : null;
        },
        setItem: async (name: string, value: any) => {
          const storage = getStorage();
          await storage.setItem(name, JSON.stringify(value));
        },
        removeItem: async (name: string) => {
          const storage = getStorage();
          await storage.removeItem(name);
        },
      },
    }
  );

  // Apply devtools in development
  if (config.enableDevtools !== false && process.env.NODE_ENV === 'development') {
    return create(devtools(persistedStore, { name: config.name }));
  }

  return create(persistedStore);
}

// Base store interface for common patterns
export interface BaseStoreState {
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
  isOnline: boolean;
}

export interface BaseStoreActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLastUpdated: (timestamp: number) => void;
  setOnlineStatus: (online: boolean) => void;
  reset: () => void;
}

// Optimistic update utilities
export interface OptimisticUpdate<T> {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: T;
  originalData?: T;
  timestamp: number;
  retryCount: number;
}

export function createOptimisticUpdateManager<T>() {
  const pendingUpdates = new Map<string, OptimisticUpdate<T>>();

  return {
    addUpdate: (update: Omit<OptimisticUpdate<T>, 'timestamp' | 'retryCount'>) => {
      pendingUpdates.set(update.id, {
        ...update,
        timestamp: Date.now(),
        retryCount: 0,
      });
    },

    removeUpdate: (id: string) => {
      pendingUpdates.delete(id);
    },

    getPendingUpdates: (): OptimisticUpdate<T>[] => {
      return Array.from(pendingUpdates.values());
    },

    retryUpdate: (id: string): OptimisticUpdate<T> | null => {
      const update = pendingUpdates.get(id);
      if (update) {
        update.retryCount++;
        return update;
      }
      return null;
    },

    clearAll: () => {
      pendingUpdates.clear();
    },
  };
}

// Real-time synchronization utilities
export interface RealtimeConfig {
  enabled: boolean;
  channels: string[];
  onUpdate?: (data: any) => void;
  onError?: (error: Error) => void;
}

export function createRealtimeManager(config: RealtimeConfig) {
  let isConnected = false;
  let reconnectAttempts = 0;
  const maxReconnectAttempts = 5;

  return {
    connect: () => {
      if (!config.enabled || isConnected) return;
      
      try {
        // Platform-specific realtime connection logic
        if (isReactNative) {
          // Mobile realtime setup
          console.log('Setting up mobile realtime connection');
        } else {
          // Web realtime setup
          console.log('Setting up web realtime connection');
        }
        
        isConnected = true;
        reconnectAttempts = 0;
      } catch (error) {
        config.onError?.(error as Error);
        
        // Retry connection with exponential backoff
        if (reconnectAttempts < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts) * 1000;
          setTimeout(() => {
            reconnectAttempts++;
            // Recursive reconnect attempt
          }, delay);
        }
      }
    },

    disconnect: () => {
      isConnected = false;
      console.log('Disconnected from realtime');
    },

    isConnected: () => isConnected,
  };
}