// Platform detection utilities
import { useState, useEffect } from 'react';

export interface PlatformInfo {
  isWeb: boolean;
  isMobile: boolean;
  isNative: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  userAgent: string;
  platform: 'web' | 'ios' | 'android' | 'unknown';
}

export function usePlatform(): PlatformInfo {
  const [platformInfo, setPlatformInfo] = useState<PlatformInfo>({
    isWeb: true,
    isMobile: false,
    isNative: false,
    isIOS: false,
    isAndroid: false,
    userAgent: '',
    platform: 'web',
  });

  useEffect(() => {
    const detectPlatform = (): PlatformInfo => {
      // Check if we're in React Native environment
      const isReactNative = typeof window === 'undefined' || !!global.navigator?.product?.match(/ReactNative/);
      
      if (isReactNative) {
        // React Native environment
        const platform = global.navigator?.platform || '';
        const isIOS = platform.toLowerCase().includes('ios');
        const isAndroid = platform.toLowerCase().includes('android');
        
        return {
          isWeb: false,
          isMobile: true,
          isNative: true,
          isIOS,
          isAndroid,
          userAgent: global.navigator?.userAgent || '',
          platform: isIOS ? 'ios' : isAndroid ? 'android' : 'unknown',
        };
      }

      // Web environment
      if (typeof window !== 'undefined' && window.navigator) {
        const userAgent = window.navigator.userAgent.toLowerCase();
        const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isIOS = /iphone|ipad|ipod/i.test(userAgent);
        const isAndroid = /android/i.test(userAgent);

        return {
          isWeb: true,
          isMobile: isMobileDevice,
          isNative: false,
          isIOS,
          isAndroid,
          userAgent: window.navigator.userAgent,
          platform: 'web',
        };
      }

      // Fallback for SSR or unknown environment
      return {
        isWeb: true,
        isMobile: false,
        isNative: false,
        isIOS: false,
        isAndroid: false,
        userAgent: '',
        platform: 'web',
      };
    };

    setPlatformInfo(detectPlatform());
  }, []);

  return platformInfo;
}

// Utility functions for platform-specific logic
export function getPlatformSpecificStyle(
  webStyle: any,
  nativeStyle: any,
  platform: PlatformInfo
) {
  return platform.isNative ? nativeStyle : webStyle;
}

export function usePlatformSpecificValue<T>(webValue: T, nativeValue: T): T {
  const platform = usePlatform();
  return platform.isNative ? nativeValue : webValue;
}

// Capacitor detection (for hybrid apps)
export function useCapacitor() {
  const [isCapacitor, setIsCapacitor] = useState(false);
  const [capacitorPlatform, setCapacitorPlatform] = useState<string>('web');

  useEffect(() => {
    const checkCapacitor = async () => {
      try {
        // Check if Capacitor is available
        if (typeof window !== 'undefined' && (window as any).Capacitor) {
          setIsCapacitor(true);
          
          // Get platform info from Capacitor
          const { Device } = await import('@capacitor/device');
          const info = await Device.getInfo();
          setCapacitorPlatform(info.platform);
        }
      } catch (error) {
        // Capacitor not available
        setIsCapacitor(false);
      }
    };

    checkCapacitor();
  }, []);

  return {
    isCapacitor,
    capacitorPlatform,
    isCapacitorIOS: capacitorPlatform === 'ios',
    isCapacitorAndroid: capacitorPlatform === 'android',
    isCapacitorWeb: capacitorPlatform === 'web',
  };
}