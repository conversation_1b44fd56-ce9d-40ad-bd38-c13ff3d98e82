export interface FileReference {
  key: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  lastModified?: number;
}

export interface Task {
  id: string;
  date: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status?: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold';
  assignment_status?: 'unassigned' | 'assigned' | 'accepted' | 'declined' | 'completed';
  completedAt?: string;
  dueDate?: string;
  due_date?: string; // Database field
  category?: string;
  tags?: string[];
  links?: string[];
  attachments?: FileReference[];
  voiceNotes?: VoiceNote[];
  comments?: Comment[];
  assigned_to?: string;
  assignment_comments?: string;
  created_at: string;
  user_id: string;
  updated_at?: string;
}

export interface CreateTaskData {
  title: string;
  description?: string;
  due_date: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed';
}

export interface TasksByDate {
  [date: string]: Task[];
}

export interface TaskAssignment {
  id: string;
  task_id: string;
  assigned_to: string;
  assigned_by: string;
  status: 'pending' | 'accepted' | 'declined' | 'completed';
  assignment_comments?: string;
  response_comments?: string;
  assigned_at: string;
  responded_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every X days/weeks/months/years
  days_of_week?: number[]; // For weekly: [0,1,2,3,4,5,6] (Sunday = 0)
  day_of_month?: number; // For monthly: 1-31
  end_date?: string;
  max_occurrences?: number;
}

export interface ReminderSettings {
  enabled: boolean;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  timing: {
    minutes_before?: number;
    hours_before?: number;
    days_before?: number;
    custom_times?: string[]; // ['09:00', '15:30']
  };
}

export interface UserProfile {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  accept_assignments: boolean;
  notification_preferences: {
    email: boolean;
    push: boolean;
    assignment: boolean;
    reminder: boolean;
  };
  timezone: string;
  work_hours: {
    start: string; // '09:00'
    end: string; // '17:00'
    days: number[]; // [1,2,3,4,5] (Monday = 1)
  };
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
}

export interface TaskAnalytics {
  user_id: string;
  total_tasks: number;
  completed_tasks: number;
  pending_tasks: number;
  in_progress_tasks: number;
  on_hold_tasks: number;
  cancelled_tasks: number;
  assigned_tasks: number;
  overdue_tasks: number;
  high_priority_tasks: number;
  avg_completion_percentage: number;
  total_estimated_hours: number;
  total_actual_hours: number;
}

export interface VoiceNote {
  url: string;
  duration: number; // in seconds
  created_at: string;
}

export interface TaskFilter {
  status?: Task['status'][];
  priority?: Task['priority'][];
  category?: string[];
  assignment_status?: Task['assignment_status'][];
  date_range?: {
    start: string;
    end: string;
  };
  search?: string;
  tags?: string[];
  assigned_to?: string;
  overdue_only?: boolean;
  has_voice_note?: boolean;
}

export interface TaskSort {
  field: 'created_at' | 'updated_at' | 'due_date' | 'priority' | 'title' | 'completion_percentage';
  direction: 'asc' | 'desc';
}

// Comment interface
export interface Comment {
  id: string;
  text: string;
  author: string;
  timestamp: string;
}

// Utility types for task creation and updates
export type CreateTaskInput = Omit<Task, 'id' | 'created_at' | 'updated_at' | 'user_id'> & {
  user_id?: string;
};

export type UpdateTaskInput = Partial<Omit<Task, 'id' | 'created_at' | 'user_id'>>;

export type CreateAssignmentInput = Omit<TaskAssignment, 'id' | 'created_at' | 'updated_at' | 'assigned_at'>;

export type UpdateAssignmentInput = Partial<Pick<TaskAssignment, 'status' | 'response_comments'>>;
