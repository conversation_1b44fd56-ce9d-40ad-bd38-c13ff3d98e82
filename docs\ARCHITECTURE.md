# Architecture Documentation

## Overview
The application follows a layered architecture with clear separation between:
- Presentation (React components)
- Application logic (Services, Hooks)
- Data access (Repositories)
- Infrastructure (Cache, Logging)

## Key Components

### 1. Repository Layer
- Abstraction over data sources (Supabase)
- Type-safe database operations
- Located in `src/repositories/`

### 2. Service Layer
- Business logic implementation
- Uses repositories for data access
- Includes:
  - CacheService (Redis)
  - RetryService
  - TaskService

### 3. Dependency Injection
- Configured in `src/di/container.ts`
- Uses tsyringe for DI
- Enables easy testing and swapping implementations

### 4. Error Handling
- Global ErrorBoundary component
- Retry mechanism for transient failures
- Centralized error logging

## Integration Points

```mermaid
flowchart TD
    UI[UI Components] --> Hooks[Custom Hooks]
    Hooks --> Services[Services]
    Services --> Repositories[Repositories]
    Repositories --> DB[(Supabase)]
    Services --> Cache[(Redis Cache)]
    Services --> Logger[Logging]
```

## Best Practices
1. Always use repositories for data access
2. Inject dependencies rather than importing directly
3. Wrap operations that may fail in retry logic
4. Use ErrorBoundary for UI error containment