import { Task } from '../types';

export interface SyncQueueItem {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  retryCount?: number;
  lastError?: string;
}

export interface SyncConflict {
  localItem: Task;
  remoteItem: Task;
  conflictType: 'data' | 'deletion' | 'version';
  field?: string;
}

export interface SyncResult {
  success: boolean;
  conflicts: SyncConflict[];
  processedItems: number;
  failedItems: number;
  errors: string[];
}

export abstract class BaseSyncService {
  protected syncQueue: SyncQueueItem[] = [];
  protected isOnline: boolean = true;
  protected isSyncing: boolean = false;

  abstract getRemoteItem(id: string, type: string): Promise<any>;
  abstract pushToRemote(item: SyncQueueItem): Promise<void>;
  abstract getLastSyncTimestamp(): Promise<number>;
  abstract setLastSyncTimestamp(timestamp: number): Promise<void>;

  // Conflict resolution strategies
  resolveConflict(conflict: SyncConflict, strategy: 'local' | 'remote' | 'merge' | 'manual' = 'remote'): Task {
    switch (strategy) {
      case 'local':
        return conflict.localItem;
      case 'remote':
        return conflict.remoteItem;
      case 'merge':
        return this.mergeItems(conflict.localItem, conflict.remoteItem);
      default:
        throw new Error('Manual resolution required');
    }
  }

  private mergeItems(local: Task, remote: Task): Task {
    // Smart merge logic - prefer newer timestamps for most fields
    const merged = { ...local };

    // Always use remote ID and creation time
    merged.id = remote.id;
    merged.created_at = remote.created_at;

    // Use most recent update timestamp
    if (new Date(remote.updated_at || remote.created_at) > new Date(local.updated_at || local.created_at)) {
      merged.updated_at = remote.updated_at;
      merged.title = remote.title;
      merged.description = remote.description;
      merged.status = remote.status;
      merged.priority = remote.priority;
    }

    // Merge arrays (combine unique items)
    if (local.tags && remote.tags) {
      merged.tags = [...new Set([...local.tags, ...remote.tags])];
    }

    if (local.links && remote.links) {
      merged.links = [...new Set([...local.links, ...remote.links])];
    }

    // Prefer remote for critical fields
    if (remote.due_date) merged.due_date = remote.due_date;
    

    return merged;
  }

  addToQueue(type: 'create' | 'update' | 'delete', data: any): void {
    const item: SyncQueueItem = {
      id: `${type}_${data.id || Date.now()}`,
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0
    };

    this.syncQueue.push(item);
    this.saveQueueToStorage();
  }

  async processQueue(): Promise<SyncResult> {
    if (this.isSyncing || !this.isOnline) {
      return {
        success: false,
        conflicts: [],
        processedItems: 0,
        failedItems: 0,
        errors: ['Sync already in progress or offline']
      };
    }

    this.isSyncing = true;
    const result: SyncResult = {
      success: true,
      conflicts: [],
      processedItems: 0,
      failedItems: 0,
      errors: []
    };

    try {
      for (const item of [...this.syncQueue]) {
        try {
          await this.processQueueItem(item);
          this.removeFromQueue(item.id);
          result.processedItems++;
        } catch (error) {
          console.error('Failed to process queue item:', error);
          result.failedItems++;
          result.errors.push(`Failed to process ${item.type} for ${item.data.id}: ${error}`);
          
          // Increment retry count
          item.retryCount = (item.retryCount || 0) + 1;
          item.lastError = error instanceof Error ? error.message : String(error);
          
          // Remove if too many retries
          if (item.retryCount >= 3) {
            this.removeFromQueue(item.id);
          }
        }
      }

      await this.setLastSyncTimestamp(Date.now());
    } finally {
      this.isSyncing = false;
      this.saveQueueToStorage();
    }

    result.success = result.failedItems === 0;
    return result;
  }

  private async processQueueItem(item: SyncQueueItem): Promise<void> {
    // Check for conflicts before processing
    if (item.type === 'update') {
      const remoteItem = await this.getRemoteItem(item.data.id, 'task');
      if (remoteItem) {
        const conflict = this.detectConflict(item.data, remoteItem);
        if (conflict) {
          // For now, automatically resolve with remote version
          item.data = this.resolveConflict(conflict, 'remote');
        }
      }
    }

    await this.pushToRemote(item);
  }

  private detectConflict(local: Task, remote: Task): SyncConflict | null {
    const localUpdate = new Date(local.updated_at || local.created_at);
    const remoteUpdate = new Date(remote.updated_at || remote.created_at);

    // Check if both items were modified since last sync
    if (Math.abs(localUpdate.getTime() - remoteUpdate.getTime()) > 1000) {
      // Find specific conflicting fields
      const conflicts: string[] = [];
      if (local.title !== remote.title) conflicts.push('title');
      if (local.description !== remote.description) conflicts.push('description');
      if (local.status !== remote.status) conflicts.push('status');
      if (local.priority !== remote.priority) conflicts.push('priority');

      if (conflicts.length > 0) {
        return {
          localItem: local,
          remoteItem: remote,
          conflictType: 'data',
          field: conflicts[0]
        };
      }
    }

    return null;
  }

  private removeFromQueue(itemId: string): void {
    this.syncQueue = this.syncQueue.filter(item => item.id !== itemId);
  }

  private saveQueueToStorage(): void {
    // Implementation depends on platform (localStorage for web, AsyncStorage for mobile)
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('syncQueue', JSON.stringify(this.syncQueue));
    }
  }

  protected loadQueueFromStorage(): void {
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem('syncQueue');
      if (stored) {
        try {
          this.syncQueue = JSON.parse(stored);
        } catch (error) {
          console.error('Failed to load sync queue:', error);
          this.syncQueue = [];
        }
      }
    }
  }

  setOnlineStatus(isOnline: boolean): void {
    this.isOnline = isOnline;
    if (isOnline && this.syncQueue.length > 0) {
      // Auto-sync when coming online
      setTimeout(() => this.processQueue(), 1000);
    }
  }

  getQueueStatus(): { itemCount: number; isSyncing: boolean; lastError?: string } {
    return {
      itemCount: this.syncQueue.length,
      isSyncing: this.isSyncing,
      lastError: this.syncQueue.find(item => item.lastError)?.lastError
    };
  }
}