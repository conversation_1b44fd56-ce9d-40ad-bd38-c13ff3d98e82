import { Platform } from 'react-native';
import { supabase } from '../lib/supabase';

export interface NotificationSettings {
  enabled: boolean;
  dailyReminders: boolean;
  reminderTime: string;
  taskDueReminders: boolean;
  dueReminderMinutes: number;
  priorityReminders: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

export interface PushNotification {
  id: string;
  title: string;
  body: string;
  data?: any;
  scheduledAt?: Date;
  repeat?: 'daily' | 'weekly' | 'monthly';
}

// Simplified notification service for mobile
class PushNotificationService {
  private static instance: PushNotificationService;
  private notificationSettings: NotificationSettings = {
    enabled: true,
    dailyReminders: true,
    reminderTime: '09:00',
    taskDueReminders: true,
    dueReminderMinutes: 30,
    priorityReminders: true,
    soundEnabled: true,
    vibrationEnabled: true,
  };

  static getInstance(): PushNotificationService {
    if (!PushNotificationService.instance) {
      PushNotificationService.instance = new PushNotificationService();
    }
    return PushNotificationService.instance;
  }

  async initialize() {
    // In a real app, this would request permissions and register for push
    console.log('Push notification service initialized');
    await this.loadSettings();
  }

  async scheduleNotification(notification: PushNotification): Promise<string> {
    // In a real app, this would use Expo Notifications
    console.log('Scheduling notification:', notification);
    return `notification-${Date.now()}`;
  }

  async scheduleTaskReminder(task: any, minutesBefore: number = 30) {
    if (!task.due_date) return;

    const dueDate = new Date(task.due_date);
    const reminderTime = new Date(dueDate.getTime() - minutesBefore * 60000);

    if (reminderTime > new Date()) {
      await this.scheduleNotification({
        id: `task-${task.id}`,
        title: `Task Due: ${task.title}`,
        body: task.description || 'You have a task due soon',
        data: { taskId: task.id, type: 'task_reminder' },
        scheduledAt: reminderTime,
      });
    }
  }

  async scheduleDailyReminder() {
    if (!this.notificationSettings.dailyReminders) return;

    await this.scheduleNotification({
      id: 'daily-reminder',
      title: 'Daily Task Reminder',
      body: 'Check your tasks for today',
      data: { type: 'daily_reminder' },
      scheduledAt: new Date(),
      repeat: 'daily',
    });
  }

  async schedulePriorityReminder(task: any) {
    if (!this.notificationSettings.priorityReminders || task.priority === 'low') return;

    const priorityText = task.priority === 'high' ? 'High Priority' : 'Medium Priority';
    
    await this.scheduleNotification({
      id: `priority-${task.id}`,
      title: `${priorityText} Task: ${task.title}`,
      body: `Don't forget about this ${task.priority} priority task`,
      data: { taskId: task.id, type: 'priority_reminder' },
      scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000),
    });
  }

  async cancelNotification(identifier: string) {
    console.log('Canceling notification:', identifier);
  }

  async cancelAllNotifications() {
    console.log('Canceling all notifications');
  }

  async getScheduledNotifications() {
    return [];
  }

  async updateSettings(settings: Partial<NotificationSettings>) {
    this.notificationSettings = { ...this.notificationSettings, ...settings };
    
    // Save to database
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      await supabase
        .from('profiles')
        .update({ notification_settings: this.notificationSettings })
        .eq('id', user.id);
    }
  }

  async loadSettings() {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      const { data } = await supabase
        .from('profiles')
        .select('notification_settings')
        .eq('id', user.id)
        .single();
      
      if (data?.notification_settings) {
        this.notificationSettings = { ...this.notificationSettings, ...data.notification_settings };
      }
    }
  }

  getSettings(): NotificationSettings {
    return this.notificationSettings;
  }
}

export const pushNotificationService = PushNotificationService.getInstance();

// Web push notification service
export class WebPushNotificationService {
  private static instance: WebPushNotificationService;
  private notificationSettings: NotificationSettings = {
    enabled: true,
    dailyReminders: true,
    reminderTime: '09:00',
    taskDueReminders: true,
    dueReminderMinutes: 30,
    priorityReminders: true,
    soundEnabled: true,
    vibrationEnabled: true,
  };

  static getInstance(): WebPushNotificationService {
    if (!WebPushNotificationService.instance) {
      WebPushNotificationService.instance = new WebPushNotificationService();
    }
    return WebPushNotificationService.instance;
  }

  async initialize() {
    if ('Notification' in window && 'serviceWorker' in navigator) {
      await this.requestPermission();
      await this.registerServiceWorker();
    }
    await this.loadSettings();
  }

  private async requestPermission() {
    if (Notification.permission === 'default') {
      await Notification.requestPermission();
    }
  }

  private async registerServiceWorker() {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', registration);
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  async scheduleNotification(notification: PushNotification): Promise<string> {
    if (Notification.permission !== 'granted') return '';

    const notificationId = `notification-${Date.now()}`;
    
    if (notification.scheduledAt && notification.scheduledAt > new Date()) {
      // Schedule for later
      setTimeout(() => {
        this.showNotification(notification);
      }, notification.scheduledAt.getTime() - Date.now());
    } else {
      // Show immediately
      this.showNotification(notification);
    }

    return notificationId;
  }

  private showNotification(notification: PushNotification) {
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.body,
        icon: '/icon-192x192.png',
        data: notification.data,
      });
    }
  }

  async scheduleTaskReminder(task: any, minutesBefore: number = 30) {
    if (!task.due_date) return;

    const dueDate = new Date(task.due_date);
    const reminderTime = new Date(dueDate.getTime() - minutesBefore * 60000);

    if (reminderTime > new Date()) {
      await this.scheduleNotification({
        id: `task-${task.id}`,
        title: `Task Due: ${task.title}`,
        body: task.description || 'You have a task due soon',
        data: { taskId: task.id, type: 'task_reminder' },
        scheduledAt: reminderTime,
      });
    }
  }

  async scheduleDailyReminder() {
    if (!this.notificationSettings.dailyReminders) return;

    const [hours, minutes] = this.notificationSettings.reminderTime.split(':').map(Number);
    const now = new Date();
    const reminderTime = new Date(now);
    reminderTime.setHours(hours, minutes, 0, 0);

    if (reminderTime < now) {
      reminderTime.setDate(reminderTime.getDate() + 1);
    }

    await this.scheduleNotification({
      id: 'daily-reminder',
      title: 'Daily Task Reminder',
      body: 'Check your tasks for today',
      data: { type: 'daily_reminder' },
      scheduledAt: reminderTime,
    });
  }

  async schedulePriorityReminder(task: any) {
    if (!this.notificationSettings.priorityReminders || task.priority === 'low') return;

    const priorityText = task.priority === 'high' ? 'High Priority' : 'Medium Priority';
    
    await this.scheduleNotification({
      id: `priority-${task.id}`,
      title: `${priorityText} Task: ${task.title}`,
      body: `Don't forget about this ${task.priority} priority task`,
      data: { taskId: task.id, type: 'priority_reminder' },
      scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000),
    });
  }

  async cancelNotification(identifier: string) {
    console.log('Canceling web notification:', identifier);
  }

  async cancelAllNotifications() {
    console.log('Canceling all web notifications');
  }

  async getScheduledNotifications() {
    return [];
  }

  async updateSettings(settings: Partial<NotificationSettings>) {
    this.notificationSettings = { ...this.notificationSettings, ...settings };
    
    // Save to database
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      await supabase
        .from('profiles')
        .update({ notification_settings: this.notificationSettings })
        .eq('id', user.id);
    }
  }

  async loadSettings() {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      const { data } = await supabase
        .from('profiles')
        .select('notification_settings')
        .eq('id', user.id)
        .single();
      
      if (data?.notification_settings) {
        this.notificationSettings = { ...this.notificationSettings, ...data.notification_settings };
      }
    }
  }

  getSettings(): NotificationSettings {
    return this.notificationSettings;
  }
}

export const webPushNotificationService = WebPushNotificationService.getInstance();