import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FileService } from '../services/fileService';
import { VoiceService } from '../services/voiceService';

interface FilePreviewProps {
  visible: boolean;
  onClose: () => void;
  file: {
    id: string;
    fileName: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
    createdAt: string;
  } | null;
  onDelete?: (fileId: string) => void;
  type?: 'attachment' | 'voice';
}

export default function FilePreview({ visible, onClose, file, onDelete, type = 'attachment' }: FilePreviewProps) {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!file) return;

    Alert.alert(
      'Delete File',
      `Are you sure you want to delete ${file.fileName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              if (type === 'attachment') {
                await FileService.deleteAttachment(file.id);
              } else {
                await VoiceService.deleteVoiceNote(file.id);
              }
              onDelete?.(file.id);
              onClose();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete file');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const isImage = file?.fileType?.includes('image');
  const isAudio = file?.fileType?.includes('audio');
  const isPDF = file?.fileName?.toLowerCase().endsWith('.pdf');

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!file) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.headerTitle} numberOfLines={1}>
              {file.fileName}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#1f2937" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* File Preview */}
            <View style={styles.previewContainer}>
              {isImage ? (
                <Image
                  source={{ uri: file.fileUrl }}
                  style={styles.imagePreview}
                  resizeMode="contain"
                />
              ) : isAudio ? (
                <View style={styles.audioContainer}>
                  <Ionicons name="musical-notes" size={64} color="#3b82f6" />
                  <Text style={styles.audioText}>Audio File</Text>
                </View>
              ) : isPDF ? (
                <View style={styles.pdfContainer}>
                  <Ionicons name="document-text" size={64} color="#ef4444" />
                  <Text style={styles.pdfText}>PDF Document</Text>
                </View>
              ) : (
                <View style={styles.genericContainer}>
                  <Ionicons name="document" size={64} color="#6b7280" />
                  <Text style={styles.genericText}>File</Text>
                </View>
              )}
            </View>

            {/* File Details */}
            <View style={styles.detailsContainer}>
              <View style={styles.detailRow}>
                <Ionicons name="document-text-outline" size={20} color="#6b7280" />
                <Text style={styles.detailLabel}>Name:</Text>
                <Text style={styles.detailValue}>{file.fileName}</Text>
              </View>

              <View style={styles.detailRow}>
                <Ionicons name="cube-outline" size={20} color="#6b7280" />
                <Text style={styles.detailLabel}>Size:</Text>
                <Text style={styles.detailValue}>{formatFileSize(file.fileSize)}</Text>
              </View>

              <View style={styles.detailRow}>
                <Ionicons name="calendar-outline" size={20} color="#6b7280" />
                <Text style={styles.detailLabel}>Created:</Text>
                <Text style={styles.detailValue}>{formatDate(file.createdAt)}</Text>
              </View>

              <View style={styles.detailRow}>
                <Ionicons name="link-outline" size={20} color="#6b7280" />
                <Text style={styles.detailLabel}>Type:</Text>
                <Text style={styles.detailValue}>{file.fileType}</Text>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionContainer}>
              <TouchableOpacity
                style={[styles.actionButton, styles.viewButton]}
                onPress={() => {
                  // Open file in browser/system viewer
                  // This would need platform-specific implementation
                }}
              >
                <Ionicons name="open-outline" size={20} color="#3b82f6" />
                <Text style={styles.viewButtonText}>Open</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={handleDelete}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#ef4444" />
                ) : (
                  <>
                    <Ionicons name="trash-outline" size={20} color="#ef4444" />
                    <Text style={styles.deleteButtonText}>Delete</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    flex: 1,
    marginRight: 8,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  previewContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 200,
  },
  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  audioContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  audioText: {
    marginTop: 8,
    fontSize: 16,
    color: '#3b82f6',
  },
  pdfContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  pdfText: {
    marginTop: 8,
    fontSize: 16,
    color: '#ef4444',
  },
  genericContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  genericText: {
    marginTop: 8,
    fontSize: 16,
    color: '#6b7280',
  },
  detailsContainer: {
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
    marginRight: 8,
    width: 60,
  },
  detailValue: {
    fontSize: 14,
    color: '#1f2937',
    flex: 1,
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  viewButton: {
    backgroundColor: '#f0f9ff',
    borderWidth: 1,
    borderColor: '#3b82f6',
  },
  viewButtonText: {
    color: '#3b82f6',
    fontSize: 14,
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#ef4444',
  },
  deleteButtonText: {
    color: '#ef4444',
    fontSize: 14,
    fontWeight: '600',
  },
});