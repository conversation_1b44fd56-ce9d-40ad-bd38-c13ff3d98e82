export type SubscriptionTier = 'free' | 'pro';
export type SubscriptionStatus = 'trial' | 'active' | 'past_due' | 'canceled' | 'unpaid';

export interface SubscriptionLimits {
  maxFileSize: number; // in bytes
  maxStorageTotal: number; // in bytes
  maxCommentsPerTask: number;
  hasVoiceFeatures: boolean;
  hasAdvancedAnalytics: boolean;
  fileRetentionDays: number; // -1 for permanent
  notificationTypes: ('email' | 'sms')[];
}

export interface SubscriptionPlan {
  id: SubscriptionTier;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  stripePriceIds: {
    monthly: string;
    yearly: string;
  };
  features: string[];
  limits: SubscriptionLimits;
  popular?: boolean;
}

export interface UserSubscription {
  id: string;
  tier: SubscriptionTier;
  status: SubscriptionStatus;
  trialEndsAt?: string | null;
  stripeCustomerId?: string | null;
  stripeSubscriptionId?: string | null;
  storageUsedBytes: number;
  createdAt: string;
  updatedAt: string;
}

export interface StorageUsage {
  used: number;
  limit: number;
  percentage: number;
  filesCount: number;
  oldestFileDate?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export interface Invoice {
  id: string;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'failed';
  createdAt: string;
  paidAt?: string;
  downloadUrl?: string;
}

export interface BillingHistory {
  invoices: Invoice[];
  nextBillingDate?: string;
  nextAmount?: number;
}
