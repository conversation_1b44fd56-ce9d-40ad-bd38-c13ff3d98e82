import React, { useState, useRef, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DraggableFABProps {
  onQuickAdd?: () => void;
  className?: string;
}

export const DraggableFAB: React.FC<DraggableFABProps> = ({ 
  onQuickAdd, 
  className 
}) => {
  const [position, setPosition] = useState({ x: 20, y: 20 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const fabRef = useRef<HTMLButtonElement>(null);
  const [hasMoved, setHasMoved] = useState(false);

  // Load saved position from localStorage
  useEffect(() => {
    const savedPosition = localStorage.getItem('fab-position');
    if (savedPosition) {
      try {
        const parsed = JSON.parse(savedPosition);
        setPosition(parsed);
      } catch (error) {
        console.error('Failed to parse saved FAB position:', error);
      }
    }
  }, []);

  // Save position to localStorage
  const savePosition = (newPosition: { x: number; y: number }) => {
    localStorage.setItem('fab-position', JSON.stringify(newPosition));
  };

  // Constrain position to viewport bounds
  const constrainPosition = (x: number, y: number) => {
    const fabSize = 56; // FAB size in pixels
    const margin = 8; // Minimum margin from edges
    
    const maxX = window.innerWidth - fabSize - margin;
    const maxY = window.innerHeight - fabSize - margin;
    
    return {
      x: Math.max(margin, Math.min(x, maxX)),
      y: Math.max(margin, Math.min(y, maxY))
    };
  };

  // Mouse events
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    setHasMoved(false);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;
    const constrainedPosition = constrainPosition(newX, newY);
    
    setPosition(constrainedPosition);
    setHasMoved(true);
  };

  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false);
      savePosition(position);
    }
  };

  // Touch events
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    setIsDragging(true);
    setHasMoved(false);
    setDragStart({
      x: touch.clientX - position.x,
      y: touch.clientY - position.y
    });
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    
    const touch = e.touches[0];
    const newX = touch.clientX - dragStart.x;
    const newY = touch.clientY - dragStart.y;
    const constrainedPosition = constrainPosition(newX, newY);
    
    setPosition(constrainedPosition);
    setHasMoved(true);
  };

  const handleTouchEnd = () => {
    if (isDragging) {
      setIsDragging(false);
      savePosition(position);
    }
  };

  // Handle click (only if not dragged)
  const handleClick = () => {
    if (!hasMoved && onQuickAdd) {
      onQuickAdd();
    }
  };

  // Add global event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isDragging, dragStart, position]);

  return (
    <button
      ref={fabRef}
      className={cn(
        "fixed z-50 w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center group touch-manipulation",
        isDragging && "cursor-grabbing scale-110 shadow-2xl",
        !isDragging && "cursor-grab hover:scale-105",
        className
      )}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      onClick={handleClick}
      aria-label="Add new task"
    >
      <Plus 
        className={cn(
          "h-6 w-6 transition-transform duration-200",
          isDragging ? "rotate-45" : "group-hover:rotate-90"
        )} 
      />
      
      {/* Ripple effect */}
      <div className="absolute inset-0 rounded-full bg-white/20 scale-0 group-active:scale-100 transition-transform duration-150" />
    </button>
  );
};