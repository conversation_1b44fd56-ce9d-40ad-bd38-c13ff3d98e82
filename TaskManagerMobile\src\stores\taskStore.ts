import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { Task } from '../types/task';

interface TaskStore {
  tasks: Task[];
  tasksByDate: Record<string, Task[]>;
  loadTasks: () => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  createTask: (task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) => Promise<Task | null>;
  refreshTasks: () => Promise<void>;
}

export const useTaskStore = create<TaskStore>((set, get) => ({
  tasks: [],
  tasksByDate: {},
  
  loadTasks: async () => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) return;

      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', user.user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading tasks:', error);
        return;
      }

      const tasks = data || [];
      const tasksByDate = tasks.reduce((acc: Record<string, Task[]>, task) => {
        const date = task.date.split('T')[0];
        if (!acc[date]) acc[date] = [];
        acc[date].push(task);
        return acc;
      }, {});

      set({ tasks, tasksByDate });
    } catch (error) {
      console.error('Error in loadTasks:', error);
    }
  },



  updateTask: async (id: string, updates: Partial<Task>) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (error) {
        console.error('Error updating task:', error);
        return;
      }

      // Refresh local state
      await get().refreshTasks();
    } catch (error) {
      console.error('Error in updateTask:', error);
    }
  },

  deleteTask: async (id: string) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting task:', error);
        return;
      }

      // Refresh local state
      await get().refreshTasks();
    } catch (error) {
      console.error('Error in deleteTask:', error);
    }
  },

  createTask: async (task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) return null;

      const { data, error } = await supabase
        .from('tasks')
        .insert({
          ...task,
          user_id: user.user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating task:', error);
        return null;
      }

      // Refresh local state
      await get().refreshTasks();
      return data;
    } catch (error) {
      console.error('Error in createTask:', error);
      return null;
    }
  },

  refreshTasks: async () => {
    await get().loadTasks();
  },
}));
