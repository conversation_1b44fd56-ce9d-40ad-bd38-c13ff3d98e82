import { openDB } from 'idb';
import { FileReference } from '@/types/task';

// Initialize IndexedDB for file attachments
export const initFileStorage = async () => {
  return await openDB('TaskAttachments', 1, {
    upgrade(db) {
      if (!db.objectStoreNames.contains('attachments')) {
        db.createObjectStore('attachments');
      }
    }
  });
};

// Store a file in IndexedDB
export const storeFile = async (file: File): Promise<FileReference> => {
  const db = await initFileStorage();
  const fileKey = `attachment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${file.name}`;
  
  await db.put('attachments', file, fileKey);
  
  return {
    key: fileKey,
    name: file.name,
    type: file.type,
    size: file.size,
    uploadedAt: new Date().toISOString(),
    lastModified: file.lastModified
  };
};

// Retrieve a file from IndexedDB
export const getFile = async (key: string): Promise<File | null> => {
  try {
    const db = await initFileStorage();
    return await db.get('attachments', key);
  } catch (error) {
    console.error('Error retrieving file:', error);
    return null;
  }
};

// Delete a file from IndexedDB
export const deleteFile = async (key: string): Promise<boolean> => {
  try {
    const db = await initFileStorage();
    await db.delete('attachments', key);
    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

// Open a file in the default program
export const openFile = async (fileRef: FileReference): Promise<void> => {
  const file = await getFile(fileRef.key);
  if (file) {
    const url = URL.createObjectURL(file);
    window.open(url, '_blank');
    // Clean up the URL after a delay to prevent memory leaks
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  } else {
    throw new Error(`File "${fileRef.name}" not found in storage`);
  }
};

// Format file size for display
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Get file type icon
export const getFileTypeInfo = (type: string) => {
  if (type.startsWith('image/')) {
    return { icon: 'image', color: 'green' };
  }
  if (type.includes('text') || type.includes('document')) {
    return { icon: 'document', color: 'blue' };
  }
  if (type.includes('pdf')) {
    return { icon: 'pdf', color: 'red' };
  }
  if (type.includes('video/')) {
    return { icon: 'video', color: 'purple' };
  }
  if (type.includes('audio/')) {
    return { icon: 'audio', color: 'orange' };
  }
  return { icon: 'file', color: 'gray' };
};