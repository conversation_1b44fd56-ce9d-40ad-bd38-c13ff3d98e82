# Professional Task Management SaaS

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4.1-purple.svg)](https://vitejs.dev/)

A modern, privacy-first task management SaaS with advanced analytics, voice integration, team collaboration, and cross-platform synchronization.

## ✨ Features

### 🎯 Core Features
- **Visual Calendar Interface** - GitHub-style contribution calendar with task visualization
- **Smart Task Management** - Create, edit, and organize tasks with priorities, categories, and tags
- **Voice Integration** - Voice-to-text task creation and voice note attachments
- **File Attachments** - Upload and manage files with tasks
- **Real-time Collaboration** - Team task assignments with acceptance/decline workflow
- **Advanced Analytics** - Productivity insights with interactive charts and progress tracking

### 🚀 Advanced Features
- **Cross-platform Sync** - Seamless synchronization between web and mobile apps
- **Offline Support** - Work offline with automatic sync when connected
- **Recurring Tasks** - Set up repeating tasks with flexible patterns
- **Progressive Web App** - Install on any device for native-like experience
- **Multi-language Support** - English, Arabic, and French translations
- **Dark/Light Theme** - System-aware theme with customization options

### 📊 Analytics & Insights
- Productivity tracking and completion rates
- Task priority and category distribution
- Time-based trend analysis
- Goal tracking and streak monitoring
- Export capabilities (CSV, JSON, PDF)

## 🏗️ Architecture

### Tech Stack
- **Frontend (Web)**: React 18, TypeScript, Vite, Tailwind CSS
- **Frontend (Mobile)**: React Native, Expo, TypeScript
- **Backend**: Supabase (PostgreSQL + Real-time + Storage)
- **Authentication**: Clerk (Multi-provider auth)
- **Payments**: Stripe integration
- **State Management**: Zustand + React Query
- **UI Components**: Radix UI + shadcn/ui

### Project Structure
```
├── src/                          # Web application source
│   ├── components/              # Reusable UI components
│   │   ├── ui/                 # Base UI components (shadcn/ui)
│   │   ├── calendar/           # Calendar-specific components
│   │   ├── auth/               # Authentication components
│   │   └── onboarding/         # Onboarding flow components
│   ├── pages/                  # Route components
│   ├── hooks/                  # Custom React hooks
│   ├── services/               # Business logic and API calls
│   ├── stores/                 # Zustand state stores
│   ├── lib/                    # Utility functions
│   └── types/                  # TypeScript type definitions
├── TaskManagerMobile/          # React Native mobile app
│   ├── src/
│   │   ├── components/         # Mobile UI components
│   │   ├── screens/            # Mobile screens
│   │   ├── navigation/         # Navigation setup
│   │   ├── services/           # Mobile services
│   │   └── stores/             # Mobile state stores
├── api/                        # Serverless API functions
├── supabase/                   # Database schema and migrations
└── public/                     # Static assets
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or bun
- Git

### Web Application Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/scheduled-task-view-fresh.git
   cd scheduled-task-view-fresh
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   bun install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Configure the following environment variables:
   ```env
   # Supabase Configuration
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   
   # Clerk Authentication
   VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
   
   # Stripe (Optional)
   VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   ```

4. **Database Setup**
   - Create a new Supabase project
   - Run the schema migrations from `supabase/schema_updated.sql`
   - Configure Row Level Security (RLS) policies
   - Set up storage buckets for file attachments

5. **Start Development Server**
   ```bash
   npm run dev
   # or
   bun dev
   ```

   The application will be available at `http://localhost:8080`

### Mobile Application Setup

1. **Navigate to mobile directory**
   ```bash
   cd TaskManagerMobile
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Configure mobile environment variables:
   ```env
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
   ```

4. **Start Expo Development Server**
   ```bash
   npx expo start
   ```

## 🔧 Development

### Available Scripts

**Web Application:**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm test` - Run test suite
- `npm run test:coverage` - Run tests with coverage

**Mobile Application:**
- `npm start` - Start Expo dev server
- `npm run android` - Run on Android
- `npm run ios` - Run on iOS
- `npm run web` - Run web version
- `npm test` - Run mobile tests

### Code Style and Conventions

- **TypeScript** - Strict type checking enabled
- **ESLint** - Airbnb configuration with React rules
- **Prettier** - Automatic code formatting
- **Conventional Commits** - Standardized commit messages
- **Husky** - Pre-commit hooks for code quality

### Testing Strategy

- **Unit Tests** - Jest + React Testing Library
- **Integration Tests** - Testing complete user workflows
- **E2E Tests** - Playwright for critical paths
- **Mobile Tests** - Jest + React Native Testing Library

## 🏢 Supabase Setup

### Database Schema

The application uses the following main tables:
- `profiles` - User profile information
- `tasks` - Task data with relationships
- `task_assignments` - Team collaboration data
- `file_attachments` - File metadata
- `recurring_patterns` - Recurring task configurations

### Required Supabase Configuration

1. **Enable Row Level Security (RLS)** on all tables
2. **Create Storage Buckets**:
   - `task-attachments` - For file uploads
   - `voice-notes` - For voice recordings
3. **Configure Authentication Providers** (Google, GitHub, etc.)
4. **Set up Real-time subscriptions** for live collaboration

### Migration Instructions

Run the complete database setup:
```sql
-- Execute the contents of supabase/schema_updated.sql
-- This creates all tables, RLS policies, and storage buckets
```

## 🔐 Authentication Setup (Clerk)

### Clerk Configuration

1. **Create Clerk Application**
   - Sign up at [clerk.dev](https://clerk.dev)
   - Create new application
   - Enable desired authentication providers

2. **Configure Authentication**
   ```javascript
   // Web: src/main.tsx
   import { ClerkProvider } from '@clerk/clerk-react'
   
   // Mobile: App.tsx
   import { ClerkProvider } from '@clerk/clerk-expo'
   ```

3. **JWT Template Setup**
   - Create custom JWT template in Clerk dashboard
   - Configure Supabase claims for RLS integration

## 📱 Mobile Development

### Expo Configuration

The mobile app uses Expo managed workflow with:
- **Expo Router** - File-based navigation
- **Expo Notifications** - Push notifications
- **Expo SecureStore** - Secure token storage
- **Expo ImagePicker** - File attachments
- **Expo Audio** - Voice recording

### Platform-Specific Features

- **iOS**: Native calendar integration, Siri shortcuts
- **Android**: Android widgets, notification channels
- **Web**: PWA capabilities, offline support

## 🚀 Deployment

### Web Application Deployment

**Vercel (Recommended):**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

**Netlify:**
```bash
# Build command: npm run build
# Publish directory: dist
```

### Mobile App Deployment

**Expo Application Services (EAS):**
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure builds
eas build:configure

# Build for production
eas build --platform all --profile production

# Submit to app stores
eas submit --platform all
```

## 🧪 Testing

### Running Tests

```bash
# Web tests
npm test

# Mobile tests
cd TaskManagerMobile && npm test

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

### Test Structure

- `__tests__/` - Unit tests
- `e2e/` - End-to-end tests
- `__mocks__/` - Mock implementations
- Coverage threshold: 80%

## 🔄 API Documentation

### Core Endpoints

**Tasks Management:**
- `GET /api/tasks` - Retrieve user tasks
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task

**Team Collaboration:**
- `POST /api/assignments` - Create task assignment
- `PUT /api/assignments/:id` - Update assignment status
- `GET /api/assignments` - Get user assignments

**Analytics:**
- `GET /api/analytics/overview` - Get productivity overview
- `GET /api/analytics/trends` - Get trend data
- `GET /api/export/:format` - Export user data

For detailed API documentation, see [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)

## 📊 Performance

### Bundle Analysis
```bash
# Analyze web bundle
npm run build:analyze

# Mobile bundle analysis
npx expo bundle --platform ios --dev false --asset-dest ./dist --bundle-output ./dist/main.js
```

### Performance Optimizations
- Code splitting with lazy loading
- Image optimization and lazy loading
- Service worker for caching
- Database query optimization
- Real-time subscription management

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make changes and add tests
4. Run tests: `npm test`
5. Commit changes: `git commit -m 'feat: add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Create Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Supabase](https://supabase.com) - Backend infrastructure
- [Clerk](https://clerk.dev) - Authentication platform
- [Vercel](https://vercel.com) - Deployment platform
- [Expo](https://expo.dev) - Mobile development platform

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/yourinvite)
- 📚 Documentation: [docs.yourdomain.com](https://docs.yourdomain.com)

---

Made with ❤️ by [Your Name](https://github.com/yourusername)
