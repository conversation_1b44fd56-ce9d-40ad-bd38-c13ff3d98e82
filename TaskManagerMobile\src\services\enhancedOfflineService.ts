import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../lib/supabase';
import { Task } from '../types/task';

interface QueuedOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  retryCount: number;
}

interface ConflictData {
  operation: QueuedOperation;
  serverData: any;
  timestamp: number;
}

export class EnhancedOfflineService {
  private static instance: EnhancedOfflineService;
  private syncQueue: QueuedOperation[] = [];
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;
  private readonly MAX_RETRIES = 3;
  private readonly SYNC_INTERVAL = 30000; // 30 seconds
  private syncTimer?: NodeJS.Timeout;

  private constructor() {
    this.loadQueueFromStorage();
    this.startPeriodicSync();
  }

  static getInstance(): EnhancedOfflineService {
    if (!EnhancedOfflineService.instance) {
      EnhancedOfflineService.instance = new EnhancedOfflineService();
    }
    return EnhancedOfflineService.instance;
  }

  private async loadQueueFromStorage() {
    try {
      const stored = await AsyncStorage.getItem('offline_sync_queue');
      if (stored) {
        this.syncQueue = JSON.parse(stored);
        console.log(`Loaded ${this.syncQueue.length} operations from storage`);
      }
    } catch (error) {
      console.error('Failed to load sync queue from storage:', error);
    }
  }

  private async saveQueueToStorage() {
    try {
      await AsyncStorage.setItem('offline_sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('Failed to save sync queue to storage:', error);
    }
  }

  private startPeriodicSync() {
    this.syncTimer = setInterval(() => {
      if (this.isOnline && this.syncQueue.length > 0) {
        this.processQueue();
      }
    }, this.SYNC_INTERVAL);
  }

  // Update online status
  setOnlineStatus(isOnline: boolean) {
    const wasOffline = !this.isOnline;
    this.isOnline = isOnline;
    
    if (isOnline && wasOffline && this.syncQueue.length > 0) {
      console.log('Back online, processing queued operations...');
      this.processQueue();
    }
  }

  // Optimistic task operations
  async createTaskOffline(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    const optimisticTask: Task = {
      ...task,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Store optimistic task locally
    await this.storeOptimisticTask(optimisticTask);

    // Queue for sync
    this.queueOperation({
      id: optimisticTask.id,
      type: 'create',
      data: task,
      timestamp: Date.now(),
      retryCount: 0
    });

    if (this.isOnline) {
      this.processQueue();
    }

    return optimisticTask;
  }

  async updateTaskOffline(id: string, updates: Partial<Task>): Promise<void> {
    // Apply optimistic update locally
    await this.applyOptimisticUpdate(id, updates);

    // Queue for sync
    this.queueOperation({
      id: `update_${id}_${Date.now()}`,
      type: 'update',
      data: { id, updates },
      timestamp: Date.now(),
      retryCount: 0
    });

    if (this.isOnline) {
      this.processQueue();
    }
  }

  async deleteTaskOffline(id: string): Promise<void> {
    // Mark as deleted locally
    await this.markTaskAsDeleted(id);

    // Queue for sync
    this.queueOperation({
      id: `delete_${id}_${Date.now()}`,
      type: 'delete',
      data: { id },
      timestamp: Date.now(),
      retryCount: 0
    });

    if (this.isOnline) {
      this.processQueue();
    }
  }

  private async storeOptimisticTask(task: Task) {
    try {
      const existingTasks = await this.getLocalTasks();
      const updatedTasks = [...existingTasks, task];
      await AsyncStorage.setItem('local_tasks', JSON.stringify(updatedTasks));
    } catch (error) {
      console.error('Failed to store optimistic task:', error);
    }
  }

  private async applyOptimisticUpdate(id: string, updates: Partial<Task>) {
    try {
      const existingTasks = await this.getLocalTasks();
      const updatedTasks = existingTasks.map(task => 
        task.id === id ? { ...task, ...updates, updatedAt: new Date().toISOString() } : task
      );
      await AsyncStorage.setItem('local_tasks', JSON.stringify(updatedTasks));
    } catch (error) {
      console.error('Failed to apply optimistic update:', error);
    }
  }

  private async markTaskAsDeleted(id: string) {
    try {
      const existingTasks = await this.getLocalTasks();
      const updatedTasks = existingTasks.filter(task => task.id !== id);
      await AsyncStorage.setItem('local_tasks', JSON.stringify(updatedTasks));
    } catch (error) {
      console.error('Failed to mark task as deleted:', error);
    }
  }

  async getLocalTasks(): Promise<Task[]> {
    try {
      const stored = await AsyncStorage.getItem('local_tasks');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get local tasks:', error);
      return [];
    }
  }

  private queueOperation(operation: QueuedOperation) {
    this.syncQueue.push(operation);
    this.saveQueueToStorage();
    console.log(`Queued ${operation.type} operation:`, operation.id);
  }

  private async processQueue() {
    if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`Processing ${this.syncQueue.length} queued operations...`);

    const processedOperations: string[] = [];

    for (const operation of [...this.syncQueue]) {
      try {
        await this.executeOperation(operation);
        processedOperations.push(operation.id);
        console.log(`Successfully synced operation: ${operation.id}`);
      } catch (error) {
        console.error(`Failed to sync operation ${operation.id}:`, error);
        
        operation.retryCount++;
        
        if (operation.retryCount >= this.MAX_RETRIES) {
          console.warn(`Operation ${operation.id} exceeded max retries, removing from queue`);
          processedOperations.push(operation.id);
          
          // Store failed operation for manual resolution
          await this.storeFailedOperation(operation);
        }
      }
    }

    // Remove processed operations
    this.syncQueue = this.syncQueue.filter(op => !processedOperations.includes(op.id));
    await this.saveQueueToStorage();
    
    this.syncInProgress = false;
    console.log(`Sync completed. ${this.syncQueue.length} operations remaining.`);
  }

  private async executeOperation(operation: QueuedOperation): Promise<void> {
    switch (operation.type) {
      case 'create':
        await this.syncCreateTask(operation);
        break;
      case 'update':
        await this.syncUpdateTask(operation);
        break;
      case 'delete':
        await this.syncDeleteTask(operation);
        break;
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  private async syncCreateTask(operation: QueuedOperation): Promise<void> {
    const { data, error } = await supabase
      .from('tasks')
      .insert(operation.data)
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // Duplicate key
        console.log('Task already exists, resolving conflict...');
        await this.resolveCreateConflict(operation);
        return;
      }
      throw error;
    }

    // Update local storage with server ID
    await this.updateLocalTaskId(operation.id, data.id);
  }

  private async syncUpdateTask(operation: QueuedOperation): Promise<void> {
    const { id, updates } = operation.data;
    
    // Check for conflicts by comparing timestamps
    const { data: serverTask, error: fetchError } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') { // Not found
        console.log('Task not found on server, may have been deleted');
        return;
      }
      throw fetchError;
    }

    // Detect conflict
    const hasConflict = this.detectConflict(serverTask, operation.timestamp);
    
    if (hasConflict) {
      const resolution = await this.resolveUpdateConflict(serverTask, updates, operation);
      if (resolution) {
        const { error } = await supabase
          .from('tasks')
          .update(resolution)
          .eq('id', id);
          
        if (error) throw error;
      }
    } else {
      const { error } = await supabase
        .from('tasks')
        .update(updates)
        .eq('id', id);
        
      if (error) throw error;
    }
  }

  private async syncDeleteTask(operation: QueuedOperation): Promise<void> {
    const { id } = operation.data;
    
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', id);

    if (error && error.code !== 'PGRST116') {
      throw error;
    }
  }

  private detectConflict(serverTask: Task, localTimestamp: number): boolean {
    const serverModified = new Date(serverTask.updatedAt).getTime();
    return serverModified > localTimestamp;
  }

  private async resolveUpdateConflict(serverTask: Task, localUpdates: Partial<Task>, operation: QueuedOperation): Promise<Partial<Task> | null> {
    // Implement conflict resolution strategies
    
    // Strategy 1: Last-write-wins for most fields
    const resolved = { ...localUpdates };
    
    // Strategy 2: Smart merge for specific fields
    if (localUpdates.status && serverTask.status !== localUpdates.status) {
      // Prefer completion over other states
      if (localUpdates.status === 'completed' || serverTask.status === 'completed') {
        resolved.status = 'completed';
        resolved.completedAt = localUpdates.completedAt || serverTask.completedAt || new Date().toISOString();
      }
    }
    
    // Strategy 3: Merge arrays (tags, attachments, etc.)
    if (localUpdates.tags && serverTask.tags) {
      const mergedTags = [...new Set([...serverTask.tags, ...localUpdates.tags])];
      resolved.tags = mergedTags;
    }
    
    // Strategy 4: Store conflict for user resolution if needed
    if (this.requiresUserResolution(serverTask, localUpdates)) {
      await this.storeConflictForUser(operation, serverTask);
      return null; // Don't auto-resolve
    }
    
    resolved.updatedAt = new Date().toISOString();
    return resolved;
  }

  private requiresUserResolution(serverTask: Task, localUpdates: Partial<Task>): boolean {
    // Determine if conflict requires user intervention
    const criticalFields = ['title', 'description', 'priority'];
    
    return criticalFields.some(field => {
      return localUpdates[field as keyof Task] && 
             serverTask[field as keyof Task] && 
             localUpdates[field as keyof Task] !== serverTask[field as keyof Task];
    });
  }

  private async resolveCreateConflict(operation: QueuedOperation): Promise<void> {
    // Convert create to update operation
    console.log('Converting create conflict to update');
    // Implementation depends on specific requirements
  }

  private async updateLocalTaskId(tempId: string, serverId: string) {
    try {
      const localTasks = await this.getLocalTasks();
      const updatedTasks = localTasks.map(task => 
        task.id === tempId ? { ...task, id: serverId } : task
      );
      await AsyncStorage.setItem('local_tasks', JSON.stringify(updatedTasks));
    } catch (error) {
      console.error('Failed to update local task ID:', error);
    }
  }

  private async storeConflictForUser(operation: QueuedOperation, serverData: Task) {
    try {
      const conflicts = await AsyncStorage.getItem('user_conflicts') || '[]';
      const conflictsArray = JSON.parse(conflicts);
      
      conflictsArray.push({
        operation,
        serverData,
        timestamp: Date.now()
      });
      
      await AsyncStorage.setItem('user_conflicts', JSON.stringify(conflictsArray));
      console.log('Stored conflict for user resolution');
    } catch (error) {
      console.error('Failed to store conflict:', error);
    }
  }

  private async storeFailedOperation(operation: QueuedOperation) {
    try {
      const failed = await AsyncStorage.getItem('failed_operations') || '[]';
      const failedArray = JSON.parse(failed);
      
      failedArray.push({
        ...operation,
        failedAt: Date.now()
      });
      
      await AsyncStorage.setItem('failed_operations', JSON.stringify(failedArray));
    } catch (error) {
      console.error('Failed to store failed operation:', error);
    }
  }

  // Public methods
  async forceSync(): Promise<void> {
    return this.processQueue();
  }

  getQueueStatus() {
    return {
      queueLength: this.syncQueue.length,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress
    };
  }

  async getConflicts(): Promise<ConflictData[]> {
    try {
      const stored = await AsyncStorage.getItem('user_conflicts');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get conflicts:', error);
      return [];
    }
  }

  async resolveConflict(conflictId: string, resolution: 'local' | 'server' | 'merge'): Promise<void> {
    // Implementation for user conflict resolution
    console.log(`Resolving conflict ${conflictId} with strategy: ${resolution}`);
  }

  destroy() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
  }
}