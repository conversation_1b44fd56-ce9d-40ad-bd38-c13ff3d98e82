import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { format, startOfWeek, addDays, isSameDay, getWeek, getYear } from 'date-fns';

interface YearlyCalendarProps {
  year: number;
  tasksByDate: { [date: string]: any[] };
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  onYearChange: (year: number) => void;
  onCreateTask: () => void;
}

export function YearlyCalendar({
  year,
  tasksByDate,
  selectedDate,
  onDateSelect,
  onYearChange,
  onCreateTask,
}: YearlyCalendarProps) {
  const [currentYear, setCurrentYear] = useState(year);
  const [viewMode, setViewMode] = useState<'yearly' | 'monthly'>('yearly');

  const screenWidth = Dimensions.get('window').width;

  const getTaskIntensity = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const tasks = tasksByDate[dateKey] || [];
    
    if (tasks.length === 0) return 0;
    if (tasks.length <= 2) return 1;
    if (tasks.length <= 5) return 2;
    if (tasks.length <= 10) return 3;
    return 4;
  };

  const getIntensityColor = (intensity: number) => {
    const colors = ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127'];
    return colors[intensity] || colors[0];
  };

  const generateYearData = useMemo(() => {
    const weeks = [];
    const firstDay = new Date(currentYear, 0, 1);
    const lastDay = new Date(currentYear, 11, 31);
    
    let currentWeekStart = startOfWeek(firstDay);
    
    while (currentWeekStart <= lastDay) {
      const week = [];
      for (let i = 0; i < 7; i++) {
        const day = addDays(currentWeekStart, i);
        if (getYear(day) === currentYear) {
          week.push({
            date: day,
            intensity: getTaskIntensity(day),
            isSelected: isSameDay(day, selectedDate),
          });
        } else {
          week.push(null);
        }
      }
      weeks.push(week);
      currentWeekStart = addDays(currentWeekStart, 7);
    }
    
    return weeks;
  }, [currentYear, tasksByDate, selectedDate]);

  const generateMonthData = useMemo(() => {
    const months = [];
    for (let month = 0; month < 12; month++) {
      const monthData = [];
      const firstDay = new Date(currentYear, month, 1);
      const lastDay = new Date(currentYear, month + 1, 0);
      
      let currentDay = firstDay;
      while (currentDay <= lastDay) {
        monthData.push({
          date: currentDay,
          intensity: getTaskIntensity(currentDay),
          isSelected: isSameDay(currentDay, selectedDate),
        });
        currentDay = addDays(currentDay, 1);
      }
      
      months.push({
        month: format(firstDay, 'MMMM'),
        days: monthData,
      });
    }
    return months;
  }, [currentYear, tasksByDate, selectedDate]);

  const navigateYear = (direction: number) => {
    const newYear = currentYear + direction;
    setCurrentYear(newYear);
    onYearChange(newYear);
  };

  const renderYearlyView = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      <View style={styles.yearContainer}>
        <View style={styles.monthLabels}>
          <Text style={styles.monthLabel}>Jan</Text>
          <Text style={styles.monthLabel}>Feb</Text>
          <Text style={styles.monthLabel}>Mar</Text>
          <Text style={styles.monthLabel}>Apr</Text>
          <Text style={styles.monthLabel}>May</Text>
          <Text style={styles.monthLabel}>Jun</Text>
          <Text style={styles.monthLabel}>Jul</Text>
          <Text style={styles.monthLabel}>Aug</Text>
          <Text style={styles.monthLabel}>Sep</Text>
          <Text style={styles.monthLabel}>Oct</Text>
          <Text style={styles.monthLabel}>Nov</Text>
          <Text style={styles.monthLabel}>Dec</Text>
        </View>
        
        <View style={styles.calendarGrid}>
          {generateYearData.map((week, weekIndex) => (
            <View key={weekIndex} style={styles.weekRow}>
              {week.map((day, dayIndex) => (
                day ? (
                  <TouchableOpacity
                    key={dayIndex}
                    style={[
                      styles.dayCell,
                      { backgroundColor: getIntensityColor(day.intensity) },
                      day.isSelected && styles.selectedDay,
                    ]}
                    onPress={() => onDateSelect(day.date)}
                  />
                ) : (
                  <View key={dayIndex} style={styles.emptyCell} />
                )
              ))}
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );

  const renderMonthlyView = () => (
    <ScrollView>
      {generateMonthData.map((monthData, monthIndex) => (
        <View key={monthIndex} style={styles.monthContainer}>
          <Text style={styles.monthTitle}>{monthData.month}</Text>
          <View style={styles.monthGrid}>
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <Text key={day} style={styles.weekdayLabel}>{day}</Text>
            ))}
            {monthData.days.map((day, dayIndex) => (
              <TouchableOpacity
                key={dayIndex}
                style={[
                  styles.monthDayCell,
                  { backgroundColor: getIntensityColor(day.intensity) },
                  day.isSelected && styles.selectedMonthDay,
                ]}
                onPress={() => onDateSelect(day.date)}
              >
                <Text style={styles.monthDayText}>{format(day.date, 'd')}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ))}
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.controls}>
          <TouchableOpacity onPress={() => navigateYear(-1)}>
            <Text style={styles.navButton}>←</Text>
          </TouchableOpacity>
          
          <Text style={styles.yearText}>{currentYear}</Text>
          
          <TouchableOpacity onPress={() => navigateYear(1)}>
            <Text style={styles.navButton}>→</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.viewControls}>
          <TouchableOpacity
            style={[styles.viewButton, viewMode === 'yearly' && styles.activeView]}
            onPress={() => setViewMode('yearly')}
          >
            <Text style={[styles.viewButtonText, viewMode === 'yearly' && styles.activeViewText]}>
              Yearly
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.viewButton, viewMode === 'monthly' && styles.activeView]}
            onPress={() => setViewMode('monthly')}
          >
            <Text style={[styles.viewButtonText, viewMode === 'monthly' && styles.activeViewText]}>
              Monthly
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.legend}>
        <Text style={styles.legendText}>Less</Text>
        <View style={[styles.legendBox, { backgroundColor: '#ebedf0' }]} />
        <View style={[styles.legendBox, { backgroundColor: '#c6e48b' }]} />
        <View style={[styles.legendBox, { backgroundColor: '#7bc96f' }]} />
        <View style={[styles.legendBox, { backgroundColor: '#239a3b' }]} />
        <View style={[styles.legendBox, { backgroundColor: '#196127' }]} />
        <Text style={styles.legendText}>More</Text>
      </View>

      {viewMode === 'yearly' ? renderYearlyView() : renderMonthlyView()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  navButton: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#3B82F6',
  },
  yearText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  viewControls: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 2,
  },
  viewButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  activeView: {
    backgroundColor: '#FFFFFF',
  },
  viewButtonText: {
    fontSize: 12,
    color: '#6B7280',
  },
  activeViewText: {
    color: '#1F2937',
    fontWeight: '600',
  },
  legend: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    paddingVertical: 8,
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
  },
  legendBox: {
    width: 12,
    height: 12,
    borderRadius: 2,
  },
  yearContainer: {
    padding: 16,
  },
  monthLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  monthLabel: {
    fontSize: 12,
    color: '#6B7280',
    width: 40,
    textAlign: 'center',
  },
  calendarGrid: {
    gap: 2,
  },
  weekRow: {
    flexDirection: 'row',
    gap: 2,
  },
  dayCell: {
    width: 12,
    height: 12,
    borderRadius: 2,
  },
  selectedDay: {
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  emptyCell: {
    width: 12,
    height: 12,
  },
  monthContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  monthTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  monthGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 2,
  },
  weekdayLabel: {
    width: 40,
    textAlign: 'center',
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  monthDayCell: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
  },
  selectedMonthDay: {
    borderWidth: 2,
    borderColor: '#3B82F6',
  },
  monthDayText: {
    fontSize: 14,
    color: '#1F2937',
  },
});