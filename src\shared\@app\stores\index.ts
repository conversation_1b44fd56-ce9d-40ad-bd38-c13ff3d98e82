// Unified store export file for cross-platform usage
export * from './unified-store';
export * from './task-store';
export * from './subscription-store';

// Platform-specific exports
export { useUnifiedTaskStore } from './task-store';
export { useUnifiedSubscriptionStore } from './subscription-store';

// Re-export cache utilities
export { globalCache } from './unified-store';

// Store integration utilities
import { useUnifiedTaskStore } from './task-store';
import { useUnifiedSubscriptionStore } from './subscription-store';

// Unified store hooks for easier usage
export const useStores = () => {
  const taskStore = useUnifiedTaskStore();
  const subscriptionStore = useUnifiedSubscriptionStore();

  return {
    tasks: taskStore,
    subscription: subscriptionStore,
    
    // Cross-store computed values
    canCreateTask: () => {
      const plan = subscriptionStore.getCurrentPlan();
      const taskCount = taskStore.tasks.length;
      
      if (!plan) return false;
      
      // Free tier has task limits, pro tier is unlimited
      if (plan.id === 'free') {
        return taskCount < 100; // Free tier limit
      }
      
      return true; // Pro tier unlimited
    },
    
    canUseAdvancedFeatures: () => {
      return subscriptionStore.canUseFeature('hasAdvancedAnalytics');
    },
    
    getStorageStatus: () => {
      const storage = subscriptionStore.storageUsage;
      const canUpload = subscriptionStore.canUploadFile;
      
      return {
        used: storage?.used || 0,
        limit: storage?.limit || 0,
        percentage: storage?.percentage || 0,
        canUpload,
      };
    },
    
    // Unified actions
    resetAllStores: () => {
      taskStore.reset();
      subscriptionStore.reset();
    },
    
    syncAllStores: async () => {
      await Promise.all([
        taskStore.syncWithRemote(),
        subscriptionStore.checkSubscriptionStatus(),
      ]);
    },
  };
};

// Store persistence utilities
export const clearAllStorageData = async () => {
  // Clear all store data from storage
  const storage = typeof window !== 'undefined' ? localStorage : null;
  
  if (storage) {
    // Web storage clearing
    const keys = Object.keys(storage);
    keys.forEach(key => {
      if (key.includes('unified-') || key.includes('-store')) {
        storage.removeItem(key);
      }
    });
  } else {
    // React Native storage clearing
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const allKeys = await AsyncStorage.getAllKeys();
      const storeKeys = allKeys.filter(key => 
        key.includes('unified-') || key.includes('-store')
      );
      await AsyncStorage.multiRemove(storeKeys);
    } catch (error) {
      console.warn('Failed to clear AsyncStorage:', error);
    }
  }
  
  // Clear global cache
  const { globalCache } = await import('./unified-store');
  globalCache.clear();
};

// Store hydration utilities
export const hydrateStoresFromCache = async () => {
  // This would be called on app startup to restore state
  console.log('Hydrating stores from persistent storage...');
  
  // The zustand persist middleware handles this automatically,
  // but we can add custom logic here if needed
};

// Cross-platform store synchronization
export const syncStoresAcrossDevices = async (userId: string) => {
  // This would sync store state across multiple devices
  // using a backend service or real-time database
  console.log(`Syncing stores for user ${userId}...`);
  
  const stores = useStores();
  
  try {
    // Upload current state to sync service
    const syncData = {
      userId,
      timestamp: Date.now(),
      tasks: stores.tasks.tasks,
      subscription: stores.subscription.subscription,
      lastSync: stores.tasks.lastSyncTimestamp,
    };
    
    // This would call a sync API endpoint
    console.log('Store sync data prepared:', syncData);
    
    // Download and merge remote state
    // Handle conflicts using the store's conflict resolution
    
  } catch (error) {
    console.error('Failed to sync stores across devices:', error);
  }
};