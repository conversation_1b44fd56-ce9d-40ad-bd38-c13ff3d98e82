import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Calendar, 
  Users, 
  Plus,
  Search,
  Download
} from 'lucide-react';
import { UserButton } from '@/components/ui/user-button';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTouchGestures } from '@/hooks/use-touch-gestures';
import { useVirtualKeyboard } from '@/hooks/useVirtualKeyboard';

interface MobileNavigationProps {
  onExportOpen?: () => void;
  onSearch?: () => void;
  unreadCount?: number;
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({ 
  onExportOpen,
  onSearch,
  unreadCount = 0
}) => {
  const location = useLocation();
  const { isVisible: isVirtualKeyboardVisible } = useVirtualKeyboard();
  
  // Touch gestures for swipe navigation
  const { touchHandlers } = useTouchGestures({
    onSwipeLeft: () => {
      // Could implement sidebar toggle
    },
    onSwipeRight: () => {
      // Could implement sidebar toggle
    }
  });

  const navItems = [
    {
      path: '/',
      label: 'Home',
      icon: Home,
      color: 'text-primary',
      badge: undefined as number | undefined
    },
    {
      path: '/calendar',
      label: 'Calendar',
      icon: Calendar,
      color: 'text-accent',
      badge: unreadCount > 0 ? unreadCount : undefined
    },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <>
      {/* Top Bar */}
      <div className="glass-card sticky top-0 z-50 animate-slide-down border-b border-border/30 safe-area-padding-x">
        <div className="flex h-14 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Calendar className="w-4 h-4 text-white" />
              </div>
              <span className="text-responsive-lg font-bold text-premium">
                TaskManager Pro
              </span>
            </Link>
          </div>

          <div className="flex items-center gap-1">
            {onSearch && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onSearch}
                className="btn-touch focus-premium rounded-md w-8 h-8 p-0"
              >
                <Search className="h-4 w-4" />
              </Button>
            )}

            {onExportOpen && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onExportOpen}
                className="btn-touch focus-premium rounded-md w-8 h-8 p-0"
              >
                <Download className="h-4 w-4" />
              </Button>
            )}

            <div className="w-8 h-8 flex items-center justify-center">
              <UserButton
                afterSignOutUrl="/"
                appearance={{
                  elements: {
                    avatarBox: "w-7 h-7",
                    userButtonTrigger: "focus-premium rounded-md"
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div 
        className={cn(
          "glass-card fixed bottom-0 left-0 right-0 z-50 border-t border-border/30 safe-area-padding-bottom transition-transform duration-300",
          isVirtualKeyboardVisible && "translate-y-full"
        )}
      >
        <div className="grid grid-cols-5 h-16 px-2">
          {navItems.map((item) => {
            const IconComponent = item.icon;
            const active = isActive(item.path);
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={cn(
                  "flex flex-col items-center justify-center gap-1 py-2 rounded-lg transition-all duration-200 btn-touch focus-premium touch-manipulation relative",
                  active 
                    ? `${item.color} bg-primary/10` 
                    : "text-muted-foreground hover:text-foreground active:scale-95"
                )}
              >
                <div className="relative">
                  <IconComponent className={cn(
                    "h-5 w-5 transition-all duration-200",
                    active && "scale-110"
                  )} />
                  
                  {item.badge && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 h-4 w-4 p-0 text-xs flex items-center justify-center animate-pulse"
                    >
                      {item.badge > 9 ? '9+' : item.badge}
                    </Badge>
                  )}
                </div>
                
                <span className={cn(
                  "text-xs font-medium transition-all duration-200",
                  active ? "opacity-100" : "opacity-70"
                )}>
                  {item.label}
                </span>
                
                {/* Active indicator */}
                {active && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
                )}
              </Link>
            );
          })}
        </div>
        
        {/* Bottom safe area spacing */}
        <div className="h-safe-area-inset-bottom" />
      </div>

      {/* Content padding to account for fixed navigation */}
      <div className="h-14" /> {/* Top padding */}
      <div className="h-16" /> {/* Bottom padding */}
    </>
  );
};