import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Calendar, 
  Users, 
  Bell,
  Search,
  Plus
} from 'lucide-react';
import { UserButton } from '@/components/ui/user-button';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ThemeToggle } from '@/components/calendar/ThemeToggle';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-mobile';

interface PremiumHeaderProps {
  onQuickAdd?: () => void;
  onSearch?: (query: string) => void;
  onOpenSettings?: () => void;
}

export const PremiumHeader: React.FC<PremiumHeaderProps> = ({
  onQuickAdd,
  onSearch,
  onOpenSettings
}) => {
  const { isMobile } = useScreenSize();
  const location = useLocation();
  
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch?.(query);
  };

  const navItems = [
    { path: '/', label: 'Home', icon: Home },
    { path: '/calendar', label: 'Calendar', icon: Calendar },


  ];

  if (isMobile) {
    // Mobile header - simplified
    return (
      <header className="glass-card fixed top-0 left-0 right-0 z-50 animate-slide-down safe-area-padding-x">
        <div className="flex items-center justify-between h-16 px-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-white" />
            </div>
            <span className="text-responsive-lg font-bold text-premium">
              TaskManager
            </span>
          </Link>

          {/* Mobile Actions */}
          <div className="flex items-center space-x-2">
            {onQuickAdd && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onQuickAdd}
                className="btn-touch focus-premium rounded-full p-2"
              >
                <Plus className="w-5 h-5" />
              </Button>
            )}
            
            <UserButton 
              afterSignOutUrl="/"
              appearance={{
                elements: {
                  avatarBox: "w-8 h-8",
                  userButtonTrigger: "focus-premium"
                }
              }}
            />
          </div>
        </div>
      </header>
    );
  }

  // Desktop header - full featured
  return (
    <header className="glass-card fixed top-0 left-0 right-0 z-50 animate-slide-down border-b border-border/50">
      <div className="container-premium">
        <div className="flex items-center justify-between h-16">
          {/* Logo & Brand */}
          <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center floating-element">
              <Calendar className="w-5 h-5 text-white" />
            </div>
            <div>
              <span className="text-responsive-xl font-bold text-premium">
                TaskManager Pro
              </span>
              <div className="text-xs text-muted-foreground -mt-1">
                Premium Task Management
              </div>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const IconComponent = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={cn(
                    "flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 focus-premium",
                    isActive 
                      ? "bg-primary/10 text-primary border border-primary/20 shadow-sm" 
                      : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                  )}
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>

          {/* Search & Actions */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative hidden sm:block">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={handleSearch}
                className="input-premium pl-10 w-64 text-sm"
              />
            </div>

            {/* Quick Actions */}
            <div className="flex items-center space-x-2">
              <ThemeToggle className="focus-premium" />
              
              {onQuickAdd && (
                <Button
                  onClick={onQuickAdd}
                  className="btn-premium focus-premium"
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  New Task
                </Button>
              )}

              <UserButton 
                afterSignOutUrl="/"
                appearance={{
                  elements: {
                    avatarBox: "w-9 h-9 ring-2 ring-primary/10",
                    userButtonTrigger: "focus-premium rounded-full transition-all duration-200 hover:ring-4 hover:ring-primary/20"
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};