import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Calendar, DateData } from 'react-native-calendars';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { supabase } from '../lib/supabase';
import { Task } from '../types/task';
import { format, parseISO } from 'date-fns';

interface MarkedDates {
  [key: string]: {
    marked?: boolean;
    dotColor?: string;
    selected?: boolean;
    selectedColor?: string;
  };
}

const CalendarScreen = () => {
  const navigation = useNavigation();
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [tasks, setTasks] = useState<Task[]>([]);
  const [markedDates, setMarkedDates] = useState<MarkedDates>({});
  const [calendarView, setCalendarView] = useState<'monthly' | 'contribution'>('monthly');
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTasks();
  }, []);

  useEffect(() => {
    updateMarkedDates();
  }, [tasks, selectedDate]);

  const fetchTasks = async () => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', 'temp-user-id') // TODO: Replace with actual authenticated user ID
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTasks(data || []);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      Alert.alert('Error', 'Failed to fetch tasks');
    } finally {
      setLoading(false);
    }
  };

  const updateMarkedDates = () => {
    const marked: MarkedDates = {};

    // Mark dates that have tasks
    tasks.forEach((task) => {
      if (task.date) {
        const dateKey = task.date;
        if (!marked[dateKey]) {
          marked[dateKey] = {
            marked: true,
            dotColor: '#3B82F6',
          };
        }
      }
    });

    // Mark selected date
    if (selectedDate) {
      marked[selectedDate] = {
        ...marked[selectedDate],
        selected: true,
        selectedColor: '#3B82F6',
      };
    }

    setMarkedDates(marked);
  };

  const onDayPress = (day: DateData) => {
    setSelectedDate(day.dateString);
  };

  const getTasksForSelectedDate = () => {
    return tasks.filter((task) => task.date === selectedDate);
  };

  const navigateToCreateTask = () => {
    navigation.navigate('CreateTask' as never, { date: selectedDate } as never);
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return '#EF4444';
      case 'medium':
        return '#F59E0B';
      case 'low':
        return '#10B981';
      default:
        return '#6B7280';
    }
  };

  const selectedDateTasks = getTasksForSelectedDate();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Calendar</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={navigateToCreateTask}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Calendar view toggle */}
      <View style={styles.viewToggle}>
        <TouchableOpacity
          style={[styles.toggleButton, calendarView === 'monthly' && styles.activeToggle]}
          onPress={() => setCalendarView('monthly')}
        >
          <Text style={[styles.toggleText, calendarView === 'monthly' && styles.activeToggleText]}>
            Monthly
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.toggleButton, calendarView === 'contribution' && styles.activeToggle]}
          onPress={() => setCalendarView('contribution')}
        >
          <Text style={[styles.toggleText, calendarView === 'contribution' && styles.activeToggleText]}>
            Year View
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.calendarContainer}>
        {calendarView === 'monthly' ? (
          <Calendar
          onDayPress={onDayPress}
          markedDates={markedDates}
          theme={{
            backgroundColor: '#ffffff',
            calendarBackground: '#ffffff',
            textSectionTitleColor: '#b6c1cd',
            selectedDayBackgroundColor: '#3B82F6',
            selectedDayTextColor: '#ffffff',
            todayTextColor: '#3B82F6',
            dayTextColor: '#2d4150',
            textDisabledColor: '#d9e1e8',
            dotColor: '#3B82F6',
            selectedDotColor: '#ffffff',
            arrowColor: '#3B82F6',
            disabledArrowColor: '#d9e1e8',
            monthTextColor: '#2d4150',
            indicatorColor: '#3B82F6',
            textDayFontWeight: '500',
            textMonthFontWeight: 'bold',
            textDayHeaderFontWeight: '600',
            textDayFontSize: 16,
            textMonthFontSize: 18,
            textDayHeaderFontSize: 14,
          }}
        />
        ) : (
          <ContributionCalendar
            year={currentYear}
            tasksByDate={tasks.reduce((acc, task) => {
              if (!acc[task.date]) acc[task.date] = [];
              acc[task.date].push(task);
              return acc;
            }, {} as Record<string, Task[]>)}
            selectedDate={new Date(selectedDate)}
            onDateSelect={(date) => setSelectedDate(format(date, 'yyyy-MM-dd'))}
            onYearChange={setCurrentYear}
            onCreateTask={navigateToCreateTask}
          />
        )}
      </View>

      <View style={styles.tasksSection}>
        <View style={styles.dateHeader}>
          <Text style={styles.dateTitle}>
            {selectedDate ? format(parseISO(selectedDate), 'MMMM d, yyyy') : 'Select a date'}
          </Text>
          <Text style={styles.taskCount}>
            {selectedDateTasks.length} task{selectedDateTasks.length !== 1 ? 's' : ''}
          </Text>
        </View>

        {selectedDateTasks.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="calendar-outline" size={48} color="#9CA3AF" />
            <Text style={styles.emptyText}>No tasks for this date</Text>
            <TouchableOpacity
              style={styles.createTaskButton}
              onPress={navigateToCreateTask}
            >
              <Text style={styles.createTaskText}>Create Task</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.tasksList}>
            {selectedDateTasks.map((task) => (
              <TouchableOpacity
                key={task.id}
                style={styles.taskCard}
                onPress={() => navigation.navigate('TaskDetails' as never, { taskId: task.id } as never)}
              >
                <View style={styles.taskHeader}>
                  <Text style={styles.taskTitle} numberOfLines={1}>
                    {task.title}
                  </Text>
                  <View
                    style={[
                      styles.priorityIndicator,
                      { backgroundColor: getPriorityColor(task.priority) },
                    ]}
                  />
                </View>
                {task.description && (
                  <Text style={styles.taskDescription} numberOfLines={2}>
                    {task.description}
                  </Text>
                )}
                <View style={styles.taskFooter}>
                  <Text style={styles.taskStatus}>
                    {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                  </Text>
                  <Text style={styles.taskPriority}>
                    {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  addButton: {
    backgroundColor: '#3B82F6',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarContainer: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tasksSection: {
    padding: 16,
  },
  dateHeader: {
    marginBottom: 16,
  },
  dateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  taskCount: {
    fontSize: 14,
    color: '#6B7280',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 12,
    marginBottom: 24,
  },
  createTaskButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createTaskText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  tasksList: {
    gap: 12,
  },
  taskCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
    marginRight: 12,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  taskDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskStatus: {
    fontSize: 12,
    color: '#059669',
    fontWeight: '500',
  },
  taskPriority: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
});

export default CalendarScreen;
