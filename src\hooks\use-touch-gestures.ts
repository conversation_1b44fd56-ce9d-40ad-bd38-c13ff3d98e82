import { useEffect, useRef, useState } from 'react';

interface TouchGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onTap?: () => void;
  onLongPress?: () => void;
  onPinchStart?: () => void;
  onPinchMove?: (scale: number) => void;
  onPinchEnd?: () => void;
  swipeThreshold?: number;
  longPressDelay?: number;
  minSwipeVelocity?: number;
  enablePinchZoom?: boolean;
}

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

interface PinchState {
  startDistance: number;
  currentDistance: number;
  scale: number;
  center: { x: number; y: number };
}

export function useTouchGestures(options: TouchGestureOptions = {}) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onTap,
    onLongPress,
    onPinchStart,
    onPinchMove,
    onPinchEnd,
    swipeThreshold = 50,
    longPressDelay = 500,
    minSwipeVelocity = 0.5,
    enablePinchZoom = false,
  } = options;

  const touchStartRef = useRef<TouchPoint | null>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const pinchStateRef = useRef<PinchState | null>(null);
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [isPinching, setIsPinching] = useState(false);

  const getDistance = (touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  const getCenter = (touch1: Touch, touch2: Touch) => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
    };
  };

  const handleTouchStart = (e: TouchEvent) => {
    const touches = e.touches;
    
    if (touches.length === 1) {
      // Single touch
      const touch = touches[0];
      touchStartRef.current = {
        x: touch.clientX,
        y: touch.clientY,
        timestamp: Date.now(),
      };

      // Start long press timer
      if (onLongPress) {
        longPressTimerRef.current = setTimeout(() => {
          setIsLongPressing(true);
          onLongPress();
        }, longPressDelay);
      }
    } else if (touches.length === 2 && enablePinchZoom) {
      // Pinch gesture
      const distance = getDistance(touches[0], touches[1]);
      const center = getCenter(touches[0], touches[1]);
      
      pinchStateRef.current = {
        startDistance: distance,
        currentDistance: distance,
        scale: 1,
        center,
      };
      
      setIsPinching(true);
      onPinchStart?.();
      
      // Clear long press timer
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
        longPressTimerRef.current = null;
      }
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    const touches = e.touches;
    
    if (touches.length === 1) {
      // Single touch move - cancel long press
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
        longPressTimerRef.current = null;
      }
      setIsLongPressing(false);
    } else if (touches.length === 2 && enablePinchZoom && pinchStateRef.current) {
      // Pinch gesture
      const distance = getDistance(touches[0], touches[1]);
      const center = getCenter(touches[0], touches[1]);
      
      const scale = distance / pinchStateRef.current.startDistance;
      
      pinchStateRef.current = {
        ...pinchStateRef.current,
        currentDistance: distance,
        scale,
        center,
      };
      
      onPinchMove?.(scale);
    }
  };

  const handleTouchEnd = (e: TouchEvent) => {
    // Clear long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    if (isLongPressing) {
      setIsLongPressing(false);
      return;
    }

    // Handle pinch end
    if (isPinching) {
      setIsPinching(false);
      onPinchEnd?.();
      pinchStateRef.current = null;
      return;
    }

    const touchStart = touchStartRef.current;
    if (!touchStart) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    const deltaTime = Date.now() - touchStart.timestamp;

    // Check for tap (short duration, minimal movement)
    if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
      onTap?.();
      return;
    }

    // Calculate velocity for better swipe detection
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const velocity = distance / deltaTime; // pixels per millisecond

    // Check for swipes with velocity consideration
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    if ((absDeltaX > swipeThreshold || absDeltaY > swipeThreshold) && velocity > minSwipeVelocity) {
      if (absDeltaX > absDeltaY) {
        // Horizontal swipe
        if (deltaX > 0) {
          onSwipeRight?.();
        } else {
          onSwipeLeft?.();
        }
      } else {
        // Vertical swipe
        if (deltaY > 0) {
          onSwipeDown?.();
        } else {
          onSwipeUp?.();
        }
      }
    }

    touchStartRef.current = null;
    setIsLongPressing(false);
  };

  const touchHandlers = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
    };
  }, []);

  return {
    touchHandlers,
    isLongPressing,
    isPinching,
    pinchScale: pinchStateRef.current?.scale || 1,
  };
}

// Hook for detecting device capabilities
export function useDeviceCapabilities() {
  const [capabilities, setCapabilities] = useState({
    hasTouch: false,
    hasHover: false,
    hasPointer: false,
    isStandalone: false,
  });

  useEffect(() => {
    const checkCapabilities = () => {
      setCapabilities({
        hasTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
        hasHover: window.matchMedia('(hover: hover)').matches,
        hasPointer: window.matchMedia('(pointer: fine)').matches,
        isStandalone: window.matchMedia('(display-mode: standalone)').matches,
      });
    };

    checkCapabilities();
    
    // Listen for changes
    const hoverQuery = window.matchMedia('(hover: hover)');
    const pointerQuery = window.matchMedia('(pointer: fine)');
    const standaloneQuery = window.matchMedia('(display-mode: standalone)');

    hoverQuery.addEventListener('change', checkCapabilities);
    pointerQuery.addEventListener('change', checkCapabilities);
    standaloneQuery.addEventListener('change', checkCapabilities);

    return () => {
      hoverQuery.removeEventListener('change', checkCapabilities);
      pointerQuery.removeEventListener('change', checkCapabilities);
      standaloneQuery.removeEventListener('change', checkCapabilities);
    };
  }, []);

  return capabilities;
}

// Hook for managing focus and keyboard navigation
export function useFocusManagement() {
  const [focusedElement, setFocusedElement] = useState<HTMLElement | null>(null);

  const trapFocus = (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  };

  const restoreFocus = (element: HTMLElement | null) => {
    if (element && element.focus) {
      element.focus();
    }
  };

  return {
    focusedElement,
    setFocusedElement,
    trapFocus,
    restoreFocus,
  };
}
