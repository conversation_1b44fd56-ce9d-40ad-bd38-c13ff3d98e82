import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, X, Calendar, Plus, Settings, BarChart3, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useScreenSize } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface OnboardingTourProps {
  onComplete: () => void;
  onSkip: () => void;
}

interface TourStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  targetSelector?: string;
  position: 'center' | 'top' | 'bottom' | 'left' | 'right';
  content: React.ReactNode;
}

const TOUR_STEPS: TourStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Your Task Calendar!',
    description: 'Let\'s take a quick tour to get you started',
    icon: <span className="text-2xl">👋</span>,
    position: 'center',
    content: (
      <div className="text-center space-y-4">
        <div className="text-4xl">🎯</div>
        <p className="text-lg">
          This quick tour will show you the key features of your task calendar
        </p>
        <p className="text-sm text-muted-foreground">
          It will only take about 2 minutes
        </p>
      </div>
    )
  },
  {
    id: 'calendar',
    title: 'Your Task Calendar',
    description: 'This is where all your tasks are displayed',
    icon: <Calendar className="h-5 w-5" />,
    position: 'center',
    content: (
      <div className="space-y-4">
        <div className="grid grid-cols-7 gap-1 p-4 bg-muted/30 rounded-lg">
          {Array.from({ length: 7 }, (_, i) => (
            <div key={i} className="aspect-square rounded border-2 border-dashed border-muted-foreground/30 flex items-center justify-center text-xs">
              {i + 1}
            </div>
          ))}
        </div>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary rounded-full"></div>
            <span className="text-sm">Click any date to view tasks</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-secondary rounded-full"></div>
            <span className="text-sm">Colored dots indicate tasks</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-destructive rounded-full"></div>
            <span className="text-sm">Colors represent priority levels</span>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'add-task',
    title: 'Adding New Tasks',
    description: 'Multiple ways to create tasks quickly',
    icon: <Plus className="h-5 w-5" />,
    position: 'center',
    content: (
      <div className="space-y-4">
        <div className="relative">
          <div className="absolute bottom-4 right-4 w-12 h-12 bg-primary rounded-full flex items-center justify-center shadow-lg">
            <Plus className="h-6 w-6 text-white" />
          </div>
          <div className="h-32 bg-muted/30 rounded-lg flex items-center justify-center text-sm text-muted-foreground">
            Calendar View
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Plus className="h-4 w-4 text-primary" />
            <span className="text-sm">Use the floating + button</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-primary" />
            <span className="text-sm">Double-click any date</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-mono bg-muted px-2 py-1 rounded">Ctrl+N</span>
            <span className="text-sm">Keyboard shortcut</span>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'task-details',
    title: 'Task Organization',
    description: 'Keep your tasks organized with priorities, categories, and more',
    icon: <BarChart3 className="h-5 w-5" />,
    position: 'center',
    content: (
      <div className="space-y-4">
        <div className="p-4 bg-muted/30 rounded-lg">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">Team Meeting</span>
              <Badge variant="destructive" className="text-xs">High</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Work</span>
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">meeting</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Weekly team sync at 10 AM
            </div>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="font-medium">Priority Levels</div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-destructive rounded-full"></div>
                <span>High</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>Medium</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Low</span>
              </div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="font-medium">Features</div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Check className="h-3 w-3 text-green-500" />
                <span>Due dates</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="h-3 w-3 text-green-500" />
                <span>Categories</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="h-3 w-3 text-green-500" />
                <span>Notes & files</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'settings',
    title: 'Customize Your Experience',
    description: 'Adjust settings to match your preferences',
    icon: <Settings className="h-5 w-5" />,
    position: 'center',
    content: (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="text-sm font-medium mb-2">Theme</div>
            <div className="flex gap-2">
              <div className="w-6 h-6 bg-background border-2 border-border rounded"></div>
              <div className="w-6 h-6 bg-secondary border-2 border-border rounded"></div>
            </div>
          </div>
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="text-sm font-medium mb-2">Notifications</div>
            <div className="text-xs text-muted-foreground">
              Task reminders & alerts
            </div>
          </div>
        </div>
        <div className="p-3 bg-muted/30 rounded-lg">
          <div className="text-sm font-medium mb-2">Data & Backup</div>
          <div className="text-xs text-muted-foreground">
            Export/import your tasks anytime
          </div>
        </div>
        <div className="text-center text-sm text-muted-foreground">
          Access settings from the menu anytime
        </div>
      </div>
    )
  },
  {
    id: 'complete',
    title: 'You\'re All Set!',
    description: 'Ready to start managing your tasks like a pro',
    icon: <span className="text-2xl">🎉</span>,
    position: 'center',
    content: (
      <div className="text-center space-y-4">
        <div className="text-4xl">🚀</div>
        <p className="text-lg">
          You now know the basics of your task calendar
        </p>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="p-3 bg-primary/5 rounded-lg">
            <div className="font-medium text-primary">Tips</div>
            <div className="text-muted-foreground text-xs mt-1">
              Use keyboard shortcuts for faster navigation
            </div>
          </div>
          <div className="p-3 bg-primary/5 rounded-lg">
            <div className="font-medium text-primary">Support</div>
            <div className="text-muted-foreground text-xs mt-1">
              Check settings for help & documentation
            </div>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Happy task management! 🎯
        </p>
      </div>
    )
  }
];

export const OnboardingTour: React.FC<OnboardingTourProps> = ({ onComplete, onSkip }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const { isMobile } = useScreenSize();

  const handleNext = () => {
    if (currentStep < TOUR_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    onSkip();
  };

  const progress = ((currentStep + 1) / TOUR_STEPS.length) * 100;
  const step = TOUR_STEPS[currentStep];

  return (
    <div className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="relative w-full max-w-2xl">
        {/* Progress bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-white mb-2">
            <span>Step {currentStep + 1} of {TOUR_STEPS.length}</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Tour card */}
        <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm dark:bg-gray-900/95">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  {step.icon}
                </div>
                <div>
                  <CardTitle className="text-xl">{step.title}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {step.description}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSkip}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Step content */}
            <div className="min-h-[300px] flex items-center justify-center">
              {step.content}
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSkip}
                  className="text-muted-foreground hover:text-foreground"
                >
                  Skip Tour
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={currentStep === 0}
                  className={cn(
                    "transition-all duration-200",
                    currentStep === 0 && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                
                <Button
                  onClick={handleNext}
                  size="sm"
                  className="bg-primary hover:bg-primary/90 text-white"
                >
                  {currentStep === TOUR_STEPS.length - 1 ? (
                    <>
                      <Check className="h-4 w-4 mr-1" />
                      Finish Tour
                    </>
                  ) : (
                    <>
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Step indicators */}
        <div className="flex justify-center mt-4 gap-2">
          {TOUR_STEPS.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentStep(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-200",
                index === currentStep
                  ? "bg-primary scale-125"
                  : index < currentStep
                  ? "bg-primary/60"
                  : "bg-white/40"
              )}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
