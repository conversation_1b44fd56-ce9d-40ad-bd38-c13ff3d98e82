import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/AppNavigator';
import TaskDetailsScreen from './TaskDetailsScreen';
import { StackNavigationProp } from '@react-navigation/stack';

type TaskDetailsModalRouteProp = RouteProp<RootStackParamList, 'TaskDetailsModal'>;
type TaskDetailsModalNavigationProp = StackNavigationProp<RootStackParamList>;

const TaskDetailsModalScreen = () => {
  const route = useRoute<TaskDetailsModalRouteProp>();
  const navigation = useNavigation<TaskDetailsModalNavigationProp>();
  const { taskId } = route.params;

  const handleClose = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      {/* Modal Header */}
      <View style={styles.modalHeader}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleClose}
        >
          <Ionicons name="close" size={24} color="#6B7280" />
        </TouchableOpacity>
        <Text style={styles.modalTitle}>Task Details</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Task Details Content */}
      <TaskDetailsScreen taskId={taskId} isModal={true} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  headerSpacer: {
    width: 40,
  },
});

export default TaskDetailsModalScreen;
