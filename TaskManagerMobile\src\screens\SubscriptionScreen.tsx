import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: {
    monthly: number;
    yearly: number;
  };
  features: string[];
  popular?: boolean;
}

const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    price: { monthly: 0, yearly: 0 },
    features: [
      'Up to 10 tasks per day',
      'Basic task management',
      'Mobile app access',
      'Local storage only'
    ],
  },
  {
    id: 'pro',
    name: 'Pro',
    price: { monthly: 9.99, yearly: 99.99 },
    features: [
      'Unlimited tasks',
      'Advanced analytics',
      'Cloud sync',
      'Voice recordings',
      'File attachments',
      'Team collaboration',
      'Priority support'
    ],
    popular: true,
  },
];

export default function SubscriptionScreen() {
  const navigation = useNavigation();
  const [currentPlan, setCurrentPlan] = useState<string>('free');
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [loading, setLoading] = useState(false);
  const [subscription] = useState({ tier: 'free', tasksCreated: 45, storageUsed: 2.1 }); // Mock data

  const handlePlanSelect = async (planId: string) => {
    if (planId === 'free') {
      Alert.alert(
        'Downgrade to Free',
        'Are you sure you want to downgrade? You will lose access to premium features.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Confirm', onPress: () => setCurrentPlan('free') }
        ]
      );
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement Stripe checkout integration
      Alert.alert('Coming Soon', 'Subscription management will be available soon!');
    } catch (error) {
      Alert.alert('Error', 'Failed to process subscription. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleManageBilling = () => {
    Alert.alert('Manage Billing', 'Billing management will be available soon!');
  };

  const PlanCard = ({ plan }: { plan: SubscriptionPlan }) => {
    const isCurrentPlan = currentPlan === plan.id;
    const price = billingCycle === 'monthly' ? plan.price.monthly : plan.price.yearly;
    const priceLabel = billingCycle === 'monthly' ? '/month' : '/year';

    return (
      <View style={[styles.planCard, isCurrentPlan && styles.currentPlanCard]}>
        {plan.popular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>Most Popular</Text>
          </View>
        )}
        
        <Text style={styles.planName}>{plan.name}</Text>
        
        <View style={styles.priceContainer}>
          <Text style={styles.price}>${price}</Text>
          <Text style={styles.priceLabel}>{priceLabel}</Text>
        </View>

        <View style={styles.featuresContainer}>
          {plan.features.map((feature, index) => (
            <View key={index} style={styles.featureRow}>
              <MaterialIcons name="check" size={16} color="#4CAF50" />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={[
            styles.selectButton,
            isCurrentPlan ? styles.currentPlanButton : styles.selectPlanButton
          ]}
          onPress={() => handlePlanSelect(plan.id)}
          disabled={loading || isCurrentPlan}
        >
          {loading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text style={[
              styles.selectButtonText,
              isCurrentPlan && styles.currentPlanButtonText
            ]}>
              {isCurrentPlan ? 'Current Plan' : 'Select Plan'}
            </Text>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Subscription</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Billing Cycle Toggle */}
        <View style={styles.billingToggleContainer}>
          <Text style={styles.sectionTitle}>Billing Cycle</Text>
          <View style={styles.billingToggle}>
            <TouchableOpacity
              style={[
                styles.billingOption,
                billingCycle === 'monthly' && styles.billingOptionActive
              ]}
              onPress={() => setBillingCycle('monthly')}
            >
              <Text style={[
                styles.billingOptionText,
                billingCycle === 'monthly' && styles.billingOptionTextActive
              ]}>
                Monthly
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.billingOption,
                billingCycle === 'yearly' && styles.billingOptionActive
              ]}
              onPress={() => setBillingCycle('yearly')}
            >
              <Text style={[
                styles.billingOptionText,
                billingCycle === 'yearly' && styles.billingOptionTextActive
              ]}>
                Yearly
              </Text>
            </TouchableOpacity>
          </View>
          {billingCycle === 'yearly' && (
            <Text style={styles.savingsText}>Save 17% with yearly billing!</Text>
          )}
        </View>

        {/* Subscription Plans */}
        <View style={styles.plansContainer}>
          {SUBSCRIPTION_PLANS.map((plan) => (
            <PlanCard key={plan.id} plan={plan} />
          ))}
        </View>

        {/* Billing Management */}
        {currentPlan !== 'free' && (
          <View style={styles.billingManagement}>
            <Text style={styles.sectionTitle}>Billing Management</Text>
            <TouchableOpacity
              style={styles.manageBillingButton}
              onPress={handleManageBilling}
            >
              <MaterialIcons name="credit-card" size={20} color="#007AFF" />
              <Text style={styles.manageBillingText}>Manage Billing</Text>
              <MaterialIcons name="chevron-right" size={20} color="#999" />
            </TouchableOpacity>
          </View>
        )}

        {/* Task Limits Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Usage</Text>
          <View style={styles.limitsContainer}>
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Tasks Created</Text>
              <Text style={styles.limitValue}>{subscription.tasksCreated} / {currentPlan === 'pro' ? '∞' : '100'}</Text>
            </View>
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Storage Used</Text>
              <Text style={styles.limitValue}>{subscription.storageUsed} GB / {currentPlan === 'pro' ? '50' : '5'} GB</Text>
            </View>
            <View style={styles.limitItem}>
              <Text style={styles.limitLabel}>Voice Features</Text>
              <Text style={currentPlan === 'pro' ? styles.enabledText : styles.disabledText}>
                {currentPlan === 'pro' ? 'Enabled' : 'Pro Only'}
              </Text>
            </View>
          </View>
        </View>

        {/* FAQ Section */}
        <View style={styles.faqContainer}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          
          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>Can I cancel anytime?</Text>
            <Text style={styles.faqAnswer}>
              Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.
            </Text>
          </View>

          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>What happens to my data if I cancel?</Text>
            <Text style={styles.faqAnswer}>
              Your data will be preserved for 30 days after cancellation, giving you time to export or resubscribe.
            </Text>
          </View>

          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>Can I upgrade or downgrade?</Text>
            <Text style={styles.faqAnswer}>
              Yes, you can change your plan at any time. Changes take effect immediately with prorated billing.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  billingToggleContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#e0e0e0',
    borderRadius: 8,
    padding: 4,
    marginBottom: 8,
  },
  billingOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  billingOptionActive: {
    backgroundColor: '#007AFF',
  },
  billingOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  billingOptionTextActive: {
    color: 'white',
  },
  savingsText: {
    fontSize: 12,
    color: '#4CAF50',
    textAlign: 'center',
    fontWeight: '500',
  },
  plansContainer: {
    gap: 16,
    marginBottom: 32,
  },
  planCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    position: 'relative',
  },
  currentPlanCard: {
    borderColor: '#007AFF',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    alignSelf: 'center',
    backgroundColor: '#FF6B35',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  planName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
    marginBottom: 20,
  },
  price: {
    fontSize: 32,
    fontWeight: '700',
    color: '#007AFF',
  },
  priceLabel: {
    fontSize: 16,
    color: '#666',
    marginLeft: 4,
  },
  featuresContainer: {
    marginBottom: 24,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  selectButton: {
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  selectPlanButton: {
    backgroundColor: '#007AFF',
  },
  currentPlanButton: {
    backgroundColor: '#e0e0e0',
  },
  selectButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  currentPlanButtonText: {
    color: '#666',
  },
  billingManagement: {
    marginBottom: 32,
  },
  manageBillingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  manageBillingText: {
    fontSize: 16,
    color: '#007AFF',
    marginLeft: 12,
    flex: 1,
  },
  faqContainer: {
    marginBottom: 32,
  },
  faqItem: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  faqAnswer: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  section: {
    marginBottom: 24,
  },
  limitsContainer: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  limitItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  limitLabel: {
    fontSize: 14,
    color: '#666',
  },
  limitValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  enabledText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
  },
  disabledText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#dc3545',
  },
});