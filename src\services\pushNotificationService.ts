import { 
  PushNotifications, 
  PushNotificationSchema, 
  ActionPerformed 
} from '@capacitor/push-notifications';
import { LocalNotifications } from '@capacitor/local-notifications';
import { Capacitor } from '@capacitor/core';
import { supabase } from '@/lib/supabase';

interface NotificationPayload {
  title: string;
  body: string;
  data?: Record<string, any>;
  schedule?: {
    at: Date;
  } | {
    every: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'year';
  };
}

export class PushNotificationService {
  private static initialized = false;

  static async initialize(userId: string): Promise<void> {
    if (!Capacitor.isNativePlatform() || this.initialized) {
      return;
    }

    try {
      // Request permission to use push notifications
      const permStatus = await PushNotifications.requestPermissions();
      
      if (permStatus.receive === 'granted') {
        // Register with Apple / Google to receive push via APNS/FCM
        await PushNotifications.register();
        
        // Register listeners
        this.setupListeners(userId);
        
        this.initialized = true;
      } else {
        console.warn('Push notification permission denied');
      }
    } catch (error) {
      console.error('Error initializing push notifications:', error);
    }
  }

  private static setupListeners(userId: string): void {
    // On success, we should be able to receive notifications
    PushNotifications.addListener('registration', (token) => {
      console.log('Push registration success, token:', token.value);
      this.saveDeviceToken(userId, token.value);
    });

    // Some issue with your setup and push will not work
    PushNotifications.addListener('registrationError', (error) => {
      console.error('Error on push registration:', error);
    });

    // Show us the notification payload if the app is open on our device
    PushNotifications.addListener('pushNotificationReceived', (notification) => {
      console.log('Push received:', notification);
      this.handleNotificationReceived(notification);
    });

    // Method called when tapping on a notification
    PushNotifications.addListener('pushNotificationActionPerformed', (notification) => {
      console.log('Push action performed:', notification);
      this.handleNotificationAction(notification);
    });
  }

  private static async saveDeviceToken(userId: string, token: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ push_token: token })
        .eq('id', userId);

      if (error) {
        console.error('Error saving device token:', error);
      }
    } catch (error) {
      console.error('Error saving device token:', error);
    }
  }

  private static handleNotificationReceived(notification: PushNotificationSchema): void {
    // Handle notification when app is in foreground
    console.log('Received notification:', notification);
  }

  private static handleNotificationAction(notification: ActionPerformed): void {
    // Handle notification tap
    console.log('Notification action:', notification);
    
    // Navigate based on notification data
    if (notification.notification.data?.taskId) {
      // Navigate to specific task
      window.location.hash = `#/task/${notification.notification.data.taskId}`;
    } else if (notification.notification.data?.route) {
      // Navigate to specific route
      window.location.hash = `#${notification.notification.data.route}`;
    }
  }

  // Local notification methods
  static async scheduleLocalNotification(payload: NotificationPayload): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      // Fallback for web - use browser notifications
      this.showWebNotification(payload);
      return;
    }

    try {
      await LocalNotifications.requestPermissions();
      
      const notification = {
        id: Date.now(),
        title: payload.title,
        body: payload.body,
        extra: payload.data || {},
        ...(payload.schedule && { schedule: payload.schedule })
      };

      await LocalNotifications.schedule({
        notifications: [notification]
      });
      
      console.log('Local notification scheduled:', notification);
    } catch (error) {
      console.error('Error scheduling local notification:', error);
    }
  }

  private static showWebNotification(payload: NotificationPayload): void {
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(payload.title, {
          body: payload.body,
          icon: '/favicon.ico',
          tag: 'task-manager',
          data: payload.data
        });
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then((permission) => {
          if (permission === 'granted') {
            new Notification(payload.title, {
              body: payload.body,
              icon: '/favicon.ico',
              tag: 'task-manager',
              data: payload.data
            });
          }
        });
      }
    }
  }

  // Task reminder methods
  static async scheduleTaskReminder(taskId: string, taskTitle: string, dueDate: Date): Promise<void> {
    const reminderTime = new Date(dueDate.getTime() - 30 * 60 * 1000); // 30 minutes before
    
    if (reminderTime > new Date()) {
      await this.scheduleLocalNotification({
        title: 'Task Reminder',
        body: `"${taskTitle}" is due in 30 minutes`,
        data: { taskId, type: 'reminder' },
        schedule: { at: reminderTime }
      });
    }
  }

  static async scheduleTaskOverdueNotification(taskId: string, taskTitle: string): Promise<void> {
    await this.scheduleLocalNotification({
      title: 'Task Overdue',
      body: `"${taskTitle}" is now overdue`,
      data: { taskId, type: 'overdue' }
    });
  }


  // Daily digest
  static async scheduleDailyDigest(taskCount: number): Promise<void> {
    if (taskCount > 0) {
      await this.scheduleLocalNotification({
        title: 'Daily Task Summary',
        body: `You have ${taskCount} tasks due today`,
        data: { type: 'digest' },
        schedule: { every: 'day' }
      });
    }
  }

  // Clear notifications
  static async clearNotifications(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      await LocalNotifications.cancel({ notifications: [] });
    }
  }

  static async clearNotificationById(id: number): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      await LocalNotifications.cancel({ notifications: [{ id }] });
    }
  }
}