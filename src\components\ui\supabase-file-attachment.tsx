import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Paperclip,
    Trash2,
    Upload,
    File,
    Image,
    FileText,
    Eye,
    AlertCircle,
    X,
    Camera,
    Video
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileAttachmentInfo {
  name: string;
  size: number;
  type: string;
  file?: File; // For new files being uploaded
  url?: string; // For existing files already uploaded
}

interface SupabaseFileAttachmentProps {
  attachments: FileAttachmentInfo[];
  onAttachmentsChange: (attachments: FileAttachmentInfo[]) => void;
  onDeleteAttachment?: (index: number) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  showPreview?: boolean;
  disabled?: boolean;
  enableCameraCapture?: boolean;
  enableVideoCapture?: boolean;
}

const getFileIcon = (type: string) => {
  if (type.startsWith('image/')) {
    return <Image className="h-4 w-4" />;
  } else if (type.startsWith('video/')) {
    return <Video className="h-4 w-4" />;
  } else if (type.includes('text') || type.includes('document') || type.includes('csv') || type.includes('spreadsheet')) {
    return <FileText className="h-4 w-4" />;
  } else {
    return <File className="h-4 w-4" />;
  }
};

const getFileTypeColor = (type: string) => {
  if (type.startsWith('image/')) {
    return 'bg-green-100 text-green-700 border-green-300';
  } else if (type.startsWith('video/')) {
    return 'bg-purple-100 text-purple-700 border-purple-300';
  } else if (type.includes('text') || type.includes('document') || type.includes('csv') || type.includes('spreadsheet')) {
    return 'bg-blue-100 text-blue-700 border-blue-300';
  } else if (type.includes('pdf')) {
    return 'bg-red-100 text-red-700 border-red-300';
  } else {
    return 'bg-gray-100 text-gray-700 border-gray-300';
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const SupabaseFileAttachment: React.FC<SupabaseFileAttachmentProps> = ({
  attachments,
  onAttachmentsChange,
  onDeleteAttachment,
  maxFiles = 10,
  maxFileSize = 25, // 25MB for mobile photos/videos
  acceptedTypes = [],
  showPreview = true,
  disabled = false,
  enableCameraCapture = true,
  enableVideoCapture = true
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (files: FileList) => {
    if (disabled) return;

    setError(null);
    setUploading(true);

    try {
      const newAttachments = [...attachments];
      const maxSizeBytes = maxFileSize * 1024 * 1024;

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Check file count limit
        if (newAttachments.length >= maxFiles) {
          setError(`Maximum ${maxFiles} files allowed`);
          break;
        }

        // Check file size
        if (file.size > maxSizeBytes) {
          setError(`File "${file.name}" is too large. Maximum size is ${maxFileSize}MB`);
          continue;
        }

        // Check file type if restrictions exist
        if (acceptedTypes.length > 0 && !acceptedTypes.some(type => file.type.includes(type))) {
          setError(`File type "${file.type}" is not allowed`);
          continue;
        }

        // Add file to attachments (will be uploaded when task is saved)
        newAttachments.push({
          name: file.name,
          size: file.size,
          type: file.type,
          file: file // Store the actual file for upload later
        });
      }

      onAttachmentsChange(newAttachments);
    } catch (error) {
      console.error('Error handling files:', error);
      setError('Failed to handle files');
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDeleteAttachment = (index: number) => {
    const newAttachments = attachments.filter((_, i) => i !== index);
    onAttachmentsChange(newAttachments);

    // Call optional callback
    if (onDeleteAttachment) {
      onDeleteAttachment(index);
    }
  };

  const handleOpenFile = (attachment: FileAttachmentInfo) => {
    if (attachment.url) {
      // Open existing file from URL
      window.open(attachment.url, '_blank', 'noopener,noreferrer');
    } else if (attachment.file) {
      // Open new file from File object
      const url = URL.createObjectURL(attachment.file);
      window.open(url, '_blank', 'noopener,noreferrer');
      // Clean up URL after a delay
      setTimeout(() => URL.revokeObjectURL(url), 1000);
    }
  };

  const handleCameraCapture = () => {
    if (disabled || !enableCameraCapture) return;
    if (cameraInputRef.current) {
      cameraInputRef.current.click();
    }
  };

  const handleVideoCapture = () => {
    if (disabled || !enableVideoCapture) return;
    if (videoInputRef.current) {
      videoInputRef.current.click();
    }
  };

  return (
    <div className="space-y-3">
      <Label className="text-sm font-semibold flex items-center gap-2">
        <Paperclip className="h-4 w-4" />
        File Attachments
        {attachments.length > 0 && (
          <Badge variant="secondary" className="ml-2">
            {attachments.length}
          </Badge>
        )}
      </Label>

      {/* Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-3 text-center transition-colors",
          dragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary/50 hover:bg-muted/50"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => {
          if (!disabled && fileInputRef.current) {
            fileInputRef.current.click();
          }
        }}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileInputChange}
          className="hidden"
          accept={acceptedTypes.length > 0 ? acceptedTypes.join(',') : undefined}
          disabled={disabled}
        />

        {/* Hidden camera input */}
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        {/* Hidden video input */}
        <input
          ref={videoInputRef}
          type="file"
          accept="video/*"
          capture="environment"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        <div className="space-y-1">
          <Upload className={cn("h-6 w-6 mx-auto", uploading ? "animate-pulse" : "")} />
          <div>
            <p className="text-sm font-medium">
              {uploading ? 'Processing files...' : 'Drop files here or click to browse'}
            </p>
            <p className="text-xs text-muted-foreground">
              Max {maxFiles} files, {maxFileSize}MB each
              {acceptedTypes.length > 0 && ` • ${acceptedTypes.join(', ')}`}
            </p>
          </div>
        </div>
      </div>

      {/* Mobile capture buttons */}
      {(enableCameraCapture || enableVideoCapture) && (
        <div className="flex gap-2 justify-center">
          {enableCameraCapture && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleCameraCapture}
              disabled={disabled || uploading}
              className="flex items-center gap-2"
            >
              <Camera className="h-4 w-4" />
              Take Photo
            </Button>
          )}
          {enableVideoCapture && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleVideoCapture}
              disabled={disabled || uploading}
              className="flex items-center gap-2"
            >
              <Video className="h-4 w-4" />
              Record Video
            </Button>
          )}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
            className="ml-auto h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Attached Files List */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Attached Files</Label>
          <div className="space-y-2">
            {attachments.map((attachment, index) => (
              <Card key={index} className="p-3">
                <div className="flex items-center justify-between gap-3">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className={cn(
                      "p-2 rounded-lg border",
                      getFileTypeColor(attachment.type)
                    )}>
                      {getFileIcon(attachment.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate" title={attachment.name}>
                        {attachment.name}
                      </p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{formatFileSize(attachment.size)}</span>
                        <span>•</span>
                        <span>{attachment.type}</span>
                        {attachment.file && (
                          <>
                            <span>•</span>
                            <span className="text-blue-600">New</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenFile(attachment)}
                      className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      title="Open file"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAttachment(index)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      title="Delete file"
                      disabled={disabled}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
