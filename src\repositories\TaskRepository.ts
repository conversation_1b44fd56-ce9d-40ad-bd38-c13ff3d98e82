import { injectable } from 'tsyringe'
import { Database } from '@/integrations/supabase/types'
import { SupabaseClient } from '@supabase/supabase-js'
import { Task, TaskInsert, TaskUpdate } from '@/types/task'

@injectable()
export class TaskRepository {
  constructor(private client: SupabaseClient<Database>) {}

  async fetchTasks(userId: string): Promise<Task[]> {
    const { data, error } = await this.client
      .from('tasks')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data?.map(this.transformRowToTask) || []
  }

  async createTask(task: TaskInsert, userId: string): Promise<Task> {
    const dbTask = this.transformTaskToDb(task, userId)
    const { data, error } = await this.client
      .from('tasks')
      .insert(dbTask)
      .select()
      .single()

    if (error) throw error
    return this.transformRowToTask(data)
  }

  async updateTask(taskId: string, updates: TaskUpdate, userId: string): Promise<Task> {
    const dbUpdates = this.transformTaskToDb(updates, userId)
    const { data, error } = await this.client
      .from('tasks')
      .update(dbUpdates)
      .eq('id', taskId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) throw error
    return this.transformRowToTask(data)
  }

  async deleteTask(taskId: string, userId: string): Promise<void> {
    const { error } = await this.client
      .from('tasks')
      .delete()
      .eq('id', taskId)
      .eq('user_id', userId)

    if (error) throw error
  }

  private transformTaskToDb(task: Partial<Task>, userId: string): any {
    return {
      user_id: userId,
      date: task.date,
      title: task.title,
      description: task.description,
      priority: task.priority,
      status: task.status,
      completed_at: task.completedAt,
      due_date: task.dueDate,
      category: task.category,
      tags: task.tags,
      comments: task.comments,
      links: task.links,
      attachments: task.attachments?.map(att => typeof att === 'string' ? att : att.key),
      voice_notes: task.voiceNotes,
    }
  }

  private transformRowToTask(row: any): Task {
    return {
      id: row.id,
      date: row.date,
      title: row.title,
      description: row.description,
      priority: row.priority,
      status: row.status,
      completedAt: row.completed_at || undefined,
      dueDate: row.due_date || undefined,
      category: row.category || undefined,
      tags: row.tags || undefined,
      comments: row.comments || undefined,
      links: row.links || undefined,
      attachments: row.attachments || undefined,
      voiceNotes: row.voice_notes || undefined,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }
  }
}