// Unified Button component for web and mobile platforms
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

// Platform detection
const isReactNative = typeof window === 'undefined' || !!global.navigator?.product?.match(/ReactNative/);

// Button variants using CVA for consistency across platforms
const buttonVariants = cva(
  // Base styles
  'inline-flex items-center justify-center font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        success: 'bg-success text-success-foreground hover:bg-success/90 shadow',
        warning: 'bg-warning text-warning-foreground hover:bg-warning/90 shadow',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        xl: 'h-12 rounded-lg px-10 text-lg',
        icon: 'h-10 w-10',
      },
      rounded: {
        none: 'rounded-none',
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
        full: 'rounded-full',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      rounded: 'md',
    },
  }
);

export interface ButtonProps extends VariantProps<typeof buttonVariants> {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  testId?: string;
  accessibilityLabel?: string;
  type?: 'button' | 'submit' | 'reset';
  // Platform-specific props
  style?: React.CSSProperties;
  onPress?: () => void; // React Native equivalent of onClick
}

// Web implementation
const WebButton: React.FC<ButtonProps> = ({
  children,
  onClick,
  disabled,
  loading,
  className,
  testId,
  variant,
  size,
  rounded,
  type = 'button',
  style,
  ...props
}) => {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      data-testid={testId}
      className={buttonVariants({ variant, size, rounded, className })}
      style={style}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-3 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
};

// React Native implementation placeholder
const NativeButton: React.FC<ButtonProps> = (props) => {
  // This would be implemented with React Native components
  // For now, we'll use a div as placeholder
  return (
    <div
      onClick={props.onClick || props.onPress}
      style={{
        padding: 12,
        backgroundColor: '#007bff',
        color: 'white',
        borderRadius: 8,
        textAlign: 'center',
        cursor: 'pointer',
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

// Unified Button export
export const Button: React.FC<ButtonProps> = (props) => {
  if (isReactNative) {
    return <NativeButton {...props} />;
  }
  return <WebButton {...props} />;
};

export { buttonVariants };