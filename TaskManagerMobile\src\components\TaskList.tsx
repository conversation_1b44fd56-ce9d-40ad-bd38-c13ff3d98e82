import React from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { Task } from '../types/task';
import { format } from 'date-fns';

interface TaskListProps {
  selectedDate: Date;
  tasks: Task[];
  onCreateTask: () => void;
}

export function TaskList({ selectedDate, tasks, onCreateTask }: TaskListProps) {
  const selectedDateKey = format(selectedDate, 'yyyy-MM-dd');
  const tasksForDate = tasks.filter(task => 
    task.date === selectedDateKey
  );

  const renderTask = ({ item }: { item: Task }) => (
    <View style={styles.taskItem}>
      <Text style={styles.taskTitle}>{item.title}</Text>
      <Text style={styles.taskDescription}>{item.description}</Text>
      <Text style={styles.taskPriority}>Priority: {item.priority}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Tasks for {format(selectedDate, 'MMM d, yyyy')}</Text>
      {tasksForDate.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No tasks for this date</Text>
          <TouchableOpacity style={styles.createButton} onPress={onCreateTask}>
            <Text style={styles.createButtonText}>+ Create Task</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={tasksForDate}
          renderItem={renderTask}
          keyExtractor={(item) => item.id}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { padding: 16 },
  header: { fontSize: 18, fontWeight: 'bold', marginBottom: 12 },
  taskItem: { backgroundColor: '#f5f5f5', padding: 12, marginBottom: 8, borderRadius: 8 },
  taskTitle: { fontSize: 16, fontWeight: '600' },
  taskDescription: { fontSize: 14, color: '#666', marginTop: 4 },
  taskPriority: { fontSize: 12, color: '#888', marginTop: 4 },
  emptyState: { alignItems: 'center', padding: 20 },
  emptyText: { fontSize: 16, color: '#666', marginBottom: 12 },
  createButton: { backgroundColor: '#3B82F6', padding: 12, borderRadius: 8 },
  createButtonText: { color: 'white', fontWeight: '600' },
});

