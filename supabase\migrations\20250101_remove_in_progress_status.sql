-- Migration to remove 'in-progress' status and convert existing tasks to 'pending'
-- This migration ensures data consistency after removing 'in-progress' from the application
-- Based on actual public.tasks table schema

-- Step 1: Update all existing 'in-progress' tasks to 'pending'
UPDATE public.tasks 
SET status = 'pending'::public.task_status, 
    updated_at = NOW()
WHERE status = 'in-progress'::public.task_status;

-- Step 2: Drop the task_analytics view that depends on the status column
DROP VIEW IF EXISTS public.task_analytics;

-- Step 3: Remove the default constraint temporarily
ALTER TABLE public.tasks ALTER COLUMN status DROP DEFAULT;

-- Step 4: Create new enum without 'in-progress'
CREATE TYPE public.task_status_new AS ENUM ('pending', 'completed', 'cancelled', 'on-hold');

-- Step 5: Update the tasks table to use the new enum
ALTER TABLE public.tasks 
ALTER COLUMN status TYPE public.task_status_new 
USING status::text::public.task_status_new;

-- Step 6: Drop the old enum and rename the new one
DROP TYPE public.task_status;
ALTER TYPE public.task_status_new RENAME TO task_status;

-- Step 7: Re-add the default constraint with the new enum
ALTER TABLE public.tasks ALTER COLUMN status SET DEFAULT 'pending'::public.task_status;

-- Step 8: Recreate the task_analytics view with updated enum references
CREATE OR REPLACE VIEW public.task_analytics AS
SELECT
  user_id,
  count(*) as total_tasks,
  count(*) filter (
    where
      status = 'completed'::task_status
  ) as completed_tasks,
  count(*) filter (
    where
      status = 'pending'::task_status
  ) as pending_tasks,
  count(*) filter (
    where
      status = 'on-hold'::task_status
  ) as on_hold_tasks,
  count(*) filter (
    where
      status = 'cancelled'::task_status
  ) as cancelled_tasks,
  count(*) filter (
    where
      assignment_status <> 'unassigned'::text
  ) as assigned_tasks,
  count(*) filter (
    where
      due_date < CURRENT_DATE
      and status <> 'completed'::task_status
  ) as overdue_tasks,
  count(*) filter (
    where
      priority = 'high'::task_priority
  ) as high_priority_tasks,
  avg(completion_percentage) as avg_completion_percentage,
  sum(estimated_hours) as total_estimated_hours,
  sum(actual_hours) as total_actual_hours
from
  tasks
group by
  user_id;

-- Step 9: Update any other tables that might reference the old enum
-- (Add additional ALTER statements here if other tables use task_status)

-- Step 10: Log the migration (optional - only if migration_log table exists)
-- INSERT INTO migration_log (migration_name, executed_at, description) 
-- VALUES (
--   '20250101_remove_in_progress_status', 
--   NOW(), 
--   'Removed in-progress status and converted existing in-progress tasks to pending'
-- ) ON CONFLICT DO NOTHING;