import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { Capacitor } from '@capacitor/core';
import { Haptics, ImpactStyle } from '@capacitor/haptics';

export interface NativeFile {
  blob: Blob;
  name: string;
  type: string;
  size: number;
}

export class NativeFileService {
  
  // Camera methods
  static async takePhoto(): Promise<NativeFile | null> {
    if (!Capacitor.isNativePlatform()) {
      return this.webPhotoCapture();
    }

    try {
      // Add haptic feedback
      await Haptics.impact({ style: ImpactStyle.Light });

      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera,
        saveToGallery: true
      });

      if (image.dataUrl) {
        const response = await fetch(image.dataUrl);
        const blob = await response.blob();
        
        return {
          blob,
          name: `photo_${Date.now()}.jpg`,
          type: 'image/jpeg',
          size: blob.size
        };
      }

      return null;
    } catch (error) {
      console.error('Error taking photo:', error);
      throw new Error('Failed to take photo');
    }
  }

  static async selectFromGallery(): Promise<NativeFile | null> {
    if (!Capacitor.isNativePlatform()) {
      return this.webFileSelect();
    }

    try {
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Photos
      });

      if (image.dataUrl) {
        const response = await fetch(image.dataUrl);
        const blob = await response.blob();
        
        return {
          blob,
          name: `gallery_${Date.now()}.jpg`,
          type: 'image/jpeg',
          size: blob.size
        };
      }

      return null;
    } catch (error) {
      console.error('Error selecting from gallery:', error);
      throw new Error('Failed to select image');
    }
  }

  // Web fallbacks
  private static async webPhotoCapture(): Promise<NativeFile | null> {
    return new Promise((resolve, reject) => {
      // Create file input
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.capture = 'camera';
      
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          resolve({
            blob: file,
            name: file.name || `photo_${Date.now()}.jpg`,
            type: file.type,
            size: file.size
          });
        } else {
          resolve(null);
        }
      };
      
      input.onerror = () => reject(new Error('Failed to capture photo'));
      input.click();
    });
  }

  private static async webFileSelect(): Promise<NativeFile | null> {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt';
      input.multiple = false;
      
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          resolve({
            blob: file,
            name: file.name,
            type: file.type,
            size: file.size
          });
        } else {
          resolve(null);
        }
      };
      
      input.onerror = () => reject(new Error('Failed to select file'));
      input.click();
    });
  }

  // File system operations
  static async saveFile(data: string, fileName: string, directory = Directory.Documents): Promise<string> {
    if (!Capacitor.isNativePlatform()) {
      // Web fallback - download file
      const blob = new Blob([data], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      return fileName;
    }

    try {
      const result = await Filesystem.writeFile({
        path: fileName,
        data: data,
        directory: directory,
        encoding: Encoding.UTF8
      });

      console.log('File saved:', result.uri);
      return result.uri;
    } catch (error) {
      console.error('Error saving file:', error);
      throw new Error('Failed to save file');
    }
  }

  static async readFile(path: string, directory = Directory.Documents): Promise<string> {
    if (!Capacitor.isNativePlatform()) {
      throw new Error('File reading not supported on web');
    }

    try {
      const result = await Filesystem.readFile({
        path: path,
        directory: directory,
        encoding: Encoding.UTF8
      });

      return result.data as string;
    } catch (error) {
      console.error('Error reading file:', error);
      throw new Error('Failed to read file');
    }
  }

  static async deleteFile(path: string, directory = Directory.Documents): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      throw new Error('File deletion not supported on web');
    }

    try {
      await Filesystem.deleteFile({
        path: path,
        directory: directory
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error('Failed to delete file');
    }
  }

  // File sharing
  static async shareFile(filePath: string, title = 'Share File'): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      // Web fallback - copy to clipboard or show share dialog
      if (navigator.share) {
        try {
          await navigator.share({
            title: title,
            url: filePath
          });
        } catch (error) {
          console.log('Error sharing:', error);
        }
      } else {
        // Copy to clipboard
        await navigator.clipboard.writeText(filePath);
        console.log('File path copied to clipboard');
      }
      return;
    }

    // Native sharing would require @capacitor/share plugin
    console.log('Native sharing not implemented yet');
  }

  // File info
  static async getFileInfo(path: string, directory = Directory.Documents): Promise<any> {
    if (!Capacitor.isNativePlatform()) {
      throw new Error('File info not supported on web');
    }

    try {
      const result = await Filesystem.stat({
        path: path,
        directory: directory
      });

      return {
        name: result.uri.split('/').pop(),
        size: result.size,
        modifiedTime: result.mtime,
        type: result.type
      };
    } catch (error) {
      console.error('Error getting file info:', error);
      throw new Error('Failed to get file info');
    }
  }

  // Utility methods
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static getFileExtension(fileName: string): string {
    return fileName.split('.').pop()?.toLowerCase() || '';
  }

  static isImageFile(fileName: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    return imageExtensions.includes(this.getFileExtension(fileName));
  }

  static isVideoFile(fileName: string): boolean {
    const videoExtensions = ['mp4', 'webm', 'ogg', 'mov', 'avi'];
    return videoExtensions.includes(this.getFileExtension(fileName));
  }

  static isAudioFile(fileName: string): boolean {
    const audioExtensions = ['mp3', 'wav', 'ogg', 'm4a', 'aac'];
    return audioExtensions.includes(this.getFileExtension(fileName));
  }
}