import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { format, startOfYear, endOfYear, eachDayOfInterval, getDay, isSameDay, addWeeks, subWeeks } from 'date-fns';
import { Task } from '../types/task';

interface ContributionCalendarProps {
  year: number;
  tasksByDate: Record<string, Task[]>;
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  onYearChange: (year: number) => void;
  onCreateTask: () => void;
}

export function ContributionCalendar({
  year,
  tasksByDate,
  selectedDate,
  onDateSelect,
  onYearChange,
  onCreateTask
}: ContributionCalendarProps) {
  const yearStart = startOfYear(new Date(year, 0, 1));
  const yearEnd = endOfYear(new Date(year, 0, 1));
  
  // Create a 53-week grid to cover the entire year
  const weeks = [];
  let currentWeek = new Date(yearStart);
  currentWeek.setDate(currentWeek.getDate() - getDay(currentWeek)); // Start from Sunday
  
  for (let week = 0; week < 53; week++) {
    const weekDays = [];
    for (let day = 0; day < 7; day++) {
      const date = new Date(currentWeek);
      date.setDate(date.getDate() + day);
      weekDays.push(date);
    }
    weeks.push(weekDays);
    currentWeek = addWeeks(currentWeek, 1);
  }

  const getTaskIntensity = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const tasks = tasksByDate[dateKey] || [];
    const taskCount = tasks.length;
    
    if (taskCount === 0) return 0;
    if (taskCount <= 2) return 1;
    if (taskCount <= 4) return 2;
    if (taskCount <= 6) return 3;
    return 4;
  };

  const getTaskPriorityColor = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const tasks = tasksByDate[dateKey] || [];
    
    if (tasks.length === 0) return '#ebedf0';
    
    const hasHigh = tasks.some(task => task.priority === 'high');
    const hasMedium = tasks.some(task => task.priority === 'medium');
    
    if (hasHigh) return '#dc2626'; // red-600
    if (hasMedium) return '#f59e0b'; // amber-500
    return '#16a34a'; // green-600
  };

  const getIntensityOpacity = (intensity: number) => {
    switch (intensity) {
      case 0: return 0.1;
      case 1: return 0.3;
      case 2: return 0.5;
      case 3: return 0.7;
      case 4: return 1.0;
      default: return 0.1;
    }
  };

  const isCurrentYear = (date: Date) => {
    return date.getFullYear() === year;
  };

  const months = ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => onYearChange(year - 1)} style={styles.yearButton}>
          <Text style={styles.yearButtonText}>‹</Text>
        </TouchableOpacity>
        <Text style={styles.yearText}>{year}</Text>
        <TouchableOpacity onPress={() => onYearChange(year + 1)} style={styles.yearButton}>
          <Text style={styles.yearButtonText}>›</Text>
        </TouchableOpacity>
      </View>
      
      {/* Month labels */}
      <View style={styles.monthLabels}>
        {months.map((month, index) => (
          <Text key={index} style={styles.monthLabel}>{month}</Text>
        ))}
      </View>

      {/* Calendar grid */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scrollContainer}>
        <View style={styles.calendar}>
          {/* Day labels */}
          <View style={styles.dayLabels}>
            {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
              <Text key={index} style={styles.dayLabel}>{day}</Text>
            ))}
          </View>
          
          {/* Week columns */}
          <View style={styles.weekContainer}>
            {weeks.map((week, weekIndex) => (
              <View key={weekIndex} style={styles.week}>
                {week.map((day, dayIndex) => {
                  const intensity = getTaskIntensity(day);
                  const baseColor = getTaskPriorityColor(day);
                  const opacity = getIntensityOpacity(intensity);
                  const isSelected = isSameDay(day, selectedDate);
                  const isThisYear = isCurrentYear(day);
                  
                  return (
                    <TouchableOpacity
                      key={dayIndex}
                      style={[
                        styles.day,
                        {
                          backgroundColor: `${baseColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
                          borderColor: isSelected ? '#3B82F6' : 'transparent',
                          borderWidth: isSelected ? 2 : 0,
                          opacity: isThisYear ? 1 : 0.3,
                        }
                      ]}
                      onPress={() => isThisYear && onDateSelect(day)}
                      disabled={!isThisYear}
                    >
                      {/* Task count indicator for high-intensity days */}
                      {intensity >= 3 && (
                        <View style={styles.taskCountIndicator}>
                          <Text style={styles.taskCountText}>
                            {tasksByDate[format(day, 'yyyy-MM-dd')]?.length || 0}
                          </Text>
                        </View>
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Legend */}
      <View style={styles.legend}>
        <Text style={styles.legendTitle}>Task Intensity & Priority</Text>
        <View style={styles.legendRow}>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, { backgroundColor: '#ebedf0' }]} />
            <Text style={styles.legendText}>No tasks</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, { backgroundColor: '#16a34a80' }]} />
            <Text style={styles.legendText}>Low priority</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, { backgroundColor: '#f59e0b80' }]} />
            <Text style={styles.legendText}>Medium priority</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, { backgroundColor: '#dc262680' }]} />
            <Text style={styles.legendText}>High priority</Text>
          </View>
        </View>
        <Text style={styles.legendNote}>
          Intensity shown by opacity • Numbers show task count for busy days
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  yearButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f3f4f6',
  },
  yearButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#3B82F6',
  },
  yearText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  monthLabels: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
    paddingHorizontal: 20,
  },
  monthLabel: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  scrollContainer: {
    marginBottom: 16,
  },
  calendar: {
    flexDirection: 'row',
  },
  dayLabels: {
    marginRight: 8,
    justifyContent: 'space-between',
    paddingVertical: 2,
  },
  dayLabel: {
    fontSize: 10,
    color: '#6b7280',
    textAlign: 'center',
    height: 12,
    lineHeight: 12,
  },
  weekContainer: {
    flexDirection: 'row',
  },
  week: {
    flexDirection: 'column',
    marginRight: 2,
  },
  day: {
    width: 12,
    height: 12,
    marginBottom: 2,
    borderRadius: 2,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  taskCountIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#1f2937',
    borderRadius: 6,
    minWidth: 12,
    height: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 2,
  },
  taskCountText: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
  },
  legend: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  legendTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  legendRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  legendSquare: {
    width: 12,
    height: 12,
    borderRadius: 2,
    marginRight: 4,
  },
  legendText: {
    fontSize: 11,
    color: '#6b7280',
  },
  legendNote: {
    fontSize: 10,
    color: '#9ca3af',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});