import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import {
  Calendar,
  CheckSquare,
  FileText,
  FolderOpen,
  Inbox,
  Search,
  Plus,
  BarChart3,
  Clock,
  Star,
  Users,
  Archive,
  Trash2,
  AlertCircle,
  Wifi,
  RefreshCw
} from 'lucide-react'

interface EmptyStateProps {
  variant?: 'default' | 'search' | 'error' | 'offline' | 'loading'
  type?: 'tasks' | 'calendar' | 'files' | 'analytics' | 'inbox' | 'archive' | 'trash' | 'favorites'
  title?: string
  description?: string
  action?: {
    label: string
    onClick: () => void
    icon?: React.ReactNode
  }
  illustration?: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

// Predefined illustrations based on type
const getIllustration = (type: EmptyStateProps['type'], variant: EmptyStateProps['variant']) => {
  const iconProps = { className: "w-16 h-16 mx-auto text-muted-foreground/50" }
  
  if (variant === 'error') {
    return <AlertCircle {...iconProps} className="w-16 h-16 mx-auto text-red-400" />
  }
  
  if (variant === 'offline') {
    return <Wifi {...iconProps} className="w-16 h-16 mx-auto text-orange-400" />
  }
  
  if (variant === 'loading') {
    return <RefreshCw {...iconProps} className="w-16 h-16 mx-auto text-blue-400 animate-spin" />
  }
  
  if (variant === 'search') {
    return <Search {...iconProps} />
  }
  
  switch (type) {
    case 'tasks':
      return <CheckSquare {...iconProps} />
    case 'calendar':
      return <Calendar {...iconProps} />
    case 'files':
      return <FileText {...iconProps} />
    case 'analytics':
      return <BarChart3 {...iconProps} />
    case 'inbox':
      return <Inbox {...iconProps} />
    case 'archive':
      return <Archive {...iconProps} />
    case 'trash':
      return <Trash2 {...iconProps} />
    case 'favorites':
      return <Star {...iconProps} />
    default:
      return <FolderOpen {...iconProps} />
  }
}

// Predefined content based on type and variant
const getContent = (type: EmptyStateProps['type'], variant: EmptyStateProps['variant']) => {
  if (variant === 'error') {
    return {
      title: 'Something went wrong',
      description: 'An error occurred while loading your data. Please try again.'
    }
  }
  
  if (variant === 'offline') {
    return {
      title: 'You\'re offline',
      description: 'Check your internet connection and try again.'
    }
  }
  
  if (variant === 'loading') {
    return {
      title: 'Loading...',
      description: 'Please wait while we fetch your data.'
    }
  }
  
  if (variant === 'search') {
    return {
      title: 'No results found',
      description: 'Try adjusting your search terms or filters.'
    }
  }
  
  switch (type) {
    case 'tasks':
      return {
        title: 'No tasks yet',
        description: 'Create your first task to get started with organizing your work.'
      }
    case 'calendar':
      return {
        title: 'No events scheduled',
        description: 'Your calendar is empty. Add some tasks or events to get started.'
      }
    case 'files':
      return {
        title: 'No files uploaded',
        description: 'Upload files to attach them to your tasks and keep everything organized.'
      }
    case 'analytics':
      return {
        title: 'No data available',
        description: 'Complete some tasks to see your productivity analytics and insights.'
      }
    case 'inbox':
      return {
        title: 'Inbox is empty',
        description: 'All caught up! No new notifications or items to review.'
      }
    case 'archive':
      return {
        title: 'No archived items',
        description: 'Completed tasks and old items will appear here when archived.'
      }
    case 'trash':
      return {
        title: 'Trash is empty',
        description: 'Deleted items will appear here. They can be restored or permanently deleted.'
      }
    case 'favorites':
      return {
        title: 'No favorites yet',
        description: 'Mark important tasks as favorites to quickly access them here.'
      }
    default:
      return {
        title: 'Nothing here yet',
        description: 'This area is empty. Add some content to get started.'
      }
  }
}

export function EmptyState({
  variant = 'default',
  type = 'tasks',
  title,
  description,
  action,
  illustration,
  className,
  size = 'md'
}: EmptyStateProps) {
  const defaultContent = getContent(type, variant)
  const finalTitle = title || defaultContent.title
  const finalDescription = description || defaultContent.description
  const finalIllustration = illustration || getIllustration(type, variant)
  
  const sizeClasses = {
    sm: 'py-8 px-4',
    md: 'py-12 px-6',
    lg: 'py-16 px-8'
  }
  
  const titleSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  }
  
  const descriptionSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }
  
  return (
    <Card className={cn('border-dashed', className)}>
      <CardContent className={cn('flex flex-col items-center justify-center text-center', sizeClasses[size])}>
        {/* Illustration */}
        <div className="mb-6">
          {finalIllustration}
        </div>
        
        {/* Title */}
        <h3 className={cn('font-semibold text-foreground mb-2', titleSizeClasses[size])}>
          {finalTitle}
        </h3>
        
        {/* Description */}
        <p className={cn('text-muted-foreground mb-6 max-w-md', descriptionSizeClasses[size])}>
          {finalDescription}
        </p>
        
        {/* Action Button */}
        {action && (
          <Button onClick={action.onClick} className="flex items-center gap-2">
            {action.icon}
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

// Predefined empty state variants for common use cases
export const EmptyTasksState = (props: Omit<EmptyStateProps, 'type'>) => (
  <EmptyState 
    {...props} 
    type="tasks" 
    action={props.action || {
      label: 'Create Task',
      onClick: () => {},
      icon: <Plus className="w-4 h-4" />
    }}
  />
)

export const EmptyCalendarState = (props: Omit<EmptyStateProps, 'type'>) => (
  <EmptyState 
    {...props} 
    type="calendar"
    action={props.action || {
      label: 'Add Event',
      onClick: () => {},
      icon: <Plus className="w-4 h-4" />
    }}
  />
)

export const EmptySearchState = (props: Omit<EmptyStateProps, 'variant'>) => (
  <EmptyState {...props} variant="search" />
)

export const ErrorState = (props: Omit<EmptyStateProps, 'variant'>) => (
  <EmptyState 
    {...props} 
    variant="error"
    action={props.action || {
      label: 'Try Again',
      onClick: () => window.location.reload(),
      icon: <RefreshCw className="w-4 h-4" />
    }}
  />
)

export const OfflineState = (props: Omit<EmptyStateProps, 'variant'>) => (
  <EmptyState 
    {...props} 
    variant="offline"
    action={props.action || {
      label: 'Retry',
      onClick: () => window.location.reload(),
      icon: <RefreshCw className="w-4 h-4" />
    }}
  />
)

export const LoadingState = (props: Omit<EmptyStateProps, 'variant'>) => (
  <EmptyState {...props} variant="loading" />
)
