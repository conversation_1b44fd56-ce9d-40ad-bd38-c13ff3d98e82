import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  User, 
  MessageSquare, 
  FileText, 
  Mic,
  Link,
  Edit,
  UserPlus,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Task } from '@/types/task';
import { useSupabaseTasks } from '@/hooks/useSupabaseTasks';
import { format } from 'date-fns';

export default function TaskDetailPage() {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { tasks, updateTask, addComment } = useSupabaseTasks();
  
  const [task, setTask] = useState<Task | null>(null);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (taskId) {
      const foundTask = tasks.find(t => t.id === taskId);
      setTask(foundTask || null);
    }
  }, [taskId, tasks]);

  const handleAddComment = async () => {
    if (!task || !newComment.trim()) return;

    setLoading(true);
    try {
      await addComment(task.id, newComment);
      setNewComment('');
      toast({
        title: 'Success',
        description: 'Comment added successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add comment',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (newStatus: Task['status']) => {
    if (!task) return;

    try {
      await updateTask(task.id, { 
        status: newStatus,
        completedAt: newStatus === 'completed' ? new Date().toISOString() : undefined
      });
      toast({
        title: 'Success',
        description: `Task marked as ${newStatus}`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update task status',
        variant: 'destructive',
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'default';

      case 'cancelled': return 'destructive';
      case 'on-hold': return 'outline';
      default: return 'outline';
    }
  };

  if (!task) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-muted-foreground">Task not found</p>
          <Button onClick={() => navigate(-1)} className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <h1 className="text-2xl font-bold">Task Details</h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/tasks/${task.id}/edit`)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>

        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          {/* Main Task Info */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <CardTitle className="text-xl">{task.title}</CardTitle>
                <div className="flex gap-2">
                  <Badge variant={getPriorityColor(task.priority)}>
                    {task.priority}
                  </Badge>
                  <Badge variant={getStatusColor(task.status)}>
                    {task.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">{task.description}</p>
              
              <div className="grid gap-3 sm:grid-cols-2">
              {task.dueDate && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span>Due: {format(new Date(task.dueDate), 'PPP')}</span>
                </div>
              )}
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span>Created: {format(new Date(task.createdAt), 'PPP')}</span>
                </div>
                {task.category && (
                  <div className="flex items-center gap-2 text-sm">
                    <FileText className="w-4 h-4 text-muted-foreground" />
                    <span>Category: {task.category}</span>
                  </div>
                )}
              </div>

              {task.tags && task.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-4">
                  {task.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {task.status !== 'completed' && (
                  <Button
                    size="sm"
                    onClick={() => handleStatusUpdate('completed')}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Mark Complete
                  </Button>
                )}
                {task.status === 'pending' && (
                  <Button
                    size="sm"
                    variant="outline"
        
                  >
                    Start Task
                  </Button>
                )}
                {task.status !== 'cancelled' && task.status !== 'completed' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusUpdate('cancelled')}
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Comments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="w-5 h-5" />
                Comments
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {task.comments && task.comments.length > 0 ? (
                <div className="space-y-3">
                  {task.comments.map((comment) => (
                    <div key={comment.id} className="border-l-2 border-muted pl-4">
                      <div className="flex justify-between items-start">
                        <p className="text-sm">{comment.text}</p>
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(comment.timestamp), 'PPp')}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        By User
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">No comments yet</p>
              )}
              
              <div className="space-y-2">
                <Textarea
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  rows={3}
                />
                <Button
                  size="sm"
                  onClick={handleAddComment}
                  disabled={loading || !newComment.trim()}
                >
                  {loading ? 'Adding...' : 'Add Comment'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">


          {/* Attachments */}
          {((task.attachments && task.attachments.length > 0) || 
            (task.voiceNotes && task.voiceNotes.length > 0) || 
            (task.links && task.links.length > 0)) && (
            <Card>
              <CardHeader>
                <CardTitle>Attachments</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {task.attachments?.map((attachment, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <FileText className="w-4 h-4 text-muted-foreground" />
                    <span>{typeof attachment === 'string' ? `Attachment ${index + 1}` : attachment.name}</span>
                  </div>
                ))}
                {task.voiceNotes?.map((voiceNote, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Mic className="w-4 h-4 text-muted-foreground" />
                    <span>Voice Note {index + 1}</span>
                  </div>
                ))}
                {task.links?.map((link, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Link className="w-4 h-4 text-muted-foreground" />
                    <a href={link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                      {link}
                    </a>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}