import React, { useState } from 'react';
import { format, startOfYear, isSameDay, isToday } from 'date-fns';
import { ChevronLeft, ChevronRight, Settings, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { TasksByDate } from '@/types/task';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-screen-size';
import { useTouchGestures } from '@/hooks/use-touch-gestures';


interface ContributionCalendarProps {
  year: number;
  tasksByDate: TasksByDate;
  selectedDate: Date | null;
  onDateSelect: (date: Date) => void;
  onYearChange: (year: number) => void;
  onSettingsClick?: () => void;
  onSignOut?: () => void;
  isDatePickerOpen?: boolean;
  onDatePickerOpenChange?: (open: boolean) => void;
  onQuickAdd?: () => void;
  isCompact?: boolean;
}

const MONTHS = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const DAYS_MONDAY_START = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
const DAYS_SUNDAY_START = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const DAYS_SATURDAY_START = ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri'];

export const ContributionCalendar: React.FC<ContributionCalendarProps> = ({
  year,
  tasksByDate,
  selectedDate,
  onDateSelect,
  onYearChange,
  onSettingsClick,
  onSignOut,
  isDatePickerOpen = false,
  onDatePickerOpenChange,
  onQuickAdd,
  isCompact = false,
}) => {
  const { isMobile, isTablet } = useScreenSize();
  const startDate = startOfYear(new Date(year, 0, 1));

  const { touchHandlers } = useTouchGestures({
    onSwipeLeft: () => {
      if (isMobile) {
        onYearChange(year + 1);
      }
    },
    onSwipeRight: () => {
      if (isMobile) {
        onYearChange(year - 1);
      }
    },
    swipeThreshold: 100,
  });

  const getWeekStartsOn = (): 'sunday' | 'monday' | 'saturday' => {
    try {
      const savedSettings = localStorage.getItem('app-settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.weekStartsOn || 'sunday';
      }
    } catch (error) {
      console.warn('Error reading week start setting:', error);
    }
    return 'sunday';
  };

  const weekStartsOn = getWeekStartsOn();

  const DAYS = weekStartsOn === 'monday' ? DAYS_MONDAY_START :
               weekStartsOn === 'saturday' ? DAYS_SATURDAY_START :
               DAYS_SUNDAY_START;

  const firstDay = startDate.getDay();
  let daysFromWeekStart: number;

  if (weekStartsOn === 'monday') {
    daysFromWeekStart = firstDay === 0 ? 6 : firstDay - 1;
  } else if (weekStartsOn === 'saturday') {
    daysFromWeekStart = (firstDay + 1) % 7;
  } else {
    daysFromWeekStart = firstDay;
  }

  const startWeek = new Date(startDate);
  startWeek.setDate(startDate.getDate() - daysFromWeekStart);

  // Create month-based structure with only dates belonging to each month
  const months: { month: number; weeks: Date[][] }[] = [];
  
  for (let monthIndex = 0; monthIndex < 12; monthIndex++) {
    const monthStart = new Date(year, monthIndex, 1);
    const monthEnd = new Date(year, monthIndex + 1, 0);
    
    // Calculate the start of the first week for this month
    const firstDay = monthStart.getDay();
    let daysFromWeekStartForMonth: number;
    
    if (weekStartsOn === 'monday') {
      daysFromWeekStartForMonth = firstDay === 0 ? 6 : firstDay - 1;
    } else if (weekStartsOn === 'saturday') {
      daysFromWeekStartForMonth = (firstDay + 1) % 7;
    } else {
      daysFromWeekStartForMonth = firstDay;
    }
    
    const monthWeeks: Date[][] = [];
    let currentDate = new Date(monthStart);
    
    // Generate weeks, but only include dates that belong to this month
    while (currentDate.getMonth() === monthIndex) {
      const currentWeek: Date[] = [];
      
      // Add empty slots for days before the month starts (first week only)
      if (monthWeeks.length === 0) {
        for (let i = 0; i < daysFromWeekStartForMonth; i++) {
          // Add null or placeholder for empty slots
          currentWeek.push(new Date(0)); // Use epoch date as placeholder
        }
      }
      
      // Add actual dates for this month
      for (let day = currentWeek.length; day < 7 && currentDate.getMonth() === monthIndex; day++) {
        currentWeek.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      // Fill remaining slots in the last week with placeholders if needed
      while (currentWeek.length < 7) {
        currentWeek.push(new Date(0)); // Use epoch date as placeholder
      }
      
      monthWeeks.push(currentWeek);
    }
    
    months.push({ month: monthIndex, weeks: monthWeeks });
  }

  const getTaskIntensity = (date: Date): number => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const tasks = tasksByDate[dateKey] || [];
    return tasks.length;
  };

  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);

  const getTileColor = (date: Date): string => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const isCurrentYear = date.getFullYear() === year;

    if (!isCurrentYear) {
      return 'bg-muted hover:bg-muted/80 border border-muted/60';
    }

    const tasks = tasksByDate[dateKey] || [];
    if (tasks.length === 0) {
      return 'bg-muted hover:bg-muted/80 border border-muted/70';
    }

    if (tasks.length > 1) {
      return 'bg-gradient-to-br from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 shadow-amber-600/30';
    }

    const highestPriority = tasks.reduce((acc, task) => {
      if (task.priority === 'high') return 'high';
      if (task.priority === 'medium' && acc !== 'high') return 'medium';
      return acc;
    }, 'low');

    if (highestPriority === 'high') {
      return 'bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-red-500/30';
    } else if (highestPriority === 'medium') {
      return 'bg-gradient-to-br from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 shadow-yellow-400/30';
    } else {
      return 'bg-gradient-to-br from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-green-600/30';
    }
  };

  const getMultiTaskIndicator = (date: Date): boolean => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const tasks = tasksByDate[dateKey] || [];
    return tasks.length > 1;
  };



  return (
    <TooltipProvider>
      <div
        className={cn(
          "w-full touch-manipulation relative z-[30] isolate overflow-y-hidden",
          isMobile ? "px-2" : ""
        )}
        {...(isMobile ? {
          onTouchStart: (e: React.TouchEvent<HTMLDivElement>) => {
            const nativeEvent = e.nativeEvent as unknown as TouchEvent;
            touchHandlers.onTouchStart(nativeEvent);
          },
          onTouchMove: (e: React.TouchEvent<HTMLDivElement>) => {
            const nativeEvent = e.nativeEvent as unknown as TouchEvent;
            touchHandlers.onTouchMove(nativeEvent);
          },
          onTouchEnd: (e: React.TouchEvent<HTMLDivElement>) => {
            const nativeEvent = e.nativeEvent as unknown as TouchEvent;
            touchHandlers.onTouchEnd(nativeEvent);
          },
        } : {})}
      >
      <div className="flex items-center justify-between mb-2">
        <h2 className={cn(
          "text-2xl font-bold",
          year === new Date().getFullYear() && "text-green-600 dark:text-green-400"
        )}>
          Task Calendar {year}
        </h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onYearChange(year - 1)}
            className={cn(
              "touch-manipulation focus-ring bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700",
              isMobile && "btn-touch px-3 py-2"
            )}
            aria-label={`Go to year ${year - 1}`}
          >
            <ChevronLeft className="h-4 w-4" />
            {year - 1}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onYearChange(year + 1)}
            className={cn(
              "touch-manipulation focus-ring bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700",
              isMobile && "btn-touch px-3 py-2"
            )}
            aria-label={`Go to year ${year + 1}`}
          >
            {year + 1}
            <ChevronRight className="h-4 w-4" />
          </Button>
          

          

        </div>
      </div>



      <div className="flex overflow-y-hidden">
        {/* Fixed weekdays column */}
        <div className={cn("flex flex-col flex-shrink-0 sticky left-0 z-[10]", isMobile ? "gap-[0.424rem] mr-4 pl-6 -mt-[0.712rem]" : isCompact ? "gap-[0.424rem] mr-6 pl-6 -mt-[0.712rem]" : "gap-[0.552rem] mr-6 pl-6 -mt-[0.776rem]")}>
          {/* Month header spacer - matches the month label height */}
          <div className="h-6 mb-1"></div>
          {DAYS.map((day, dayIndex) => (
            <div
              key={dayIndex}
              className={cn(
                  "text-xs text-muted-foreground flex items-center justify-center",
                  "min-w-[16px]",
                  isMobile ? "w-6 h-6" : isTablet ? "w-4 h-4" : "w-3 h-3"
                )}
            >
              {day}
            </div>
          ))}
        </div>

        {/* Scrollable months container */}
        <div className={cn("overflow-x-auto overflow-y-hidden scrollbar-thin scrollbar-thumb-blue-400 dark:scrollbar-thumb-blue-500 hover:scrollbar-thumb-blue-500 dark:hover:scrollbar-thumb-blue-400 pb-[calc(0.5rem+6pt)] relative hide-vertical-scrollbar", !isCompact && "pr-2")}>
          <div className={cn("flex min-w-max", isMobile ? "gap-3" : isCompact ? "gap-2" : "gap-4")}>
            <div className={cn("flex flex-shrink-0", isMobile ? "gap-2" : isCompact ? "gap-1.5" : "gap-3")}>
          {months.map((monthData, monthIndex) => (
             <div key={monthIndex} className="flex flex-col">
               {/* Month label */}
                <div className="h-6 flex items-center justify-center mb-1">
                  <button
                    className={cn(
                      "text-xs font-medium px-2 py-0.5 rounded transition-all duration-200 cursor-pointer",
                      selectedMonth === monthData.month
                        ? "text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/40"
                        : "text-muted-foreground hover:text-primary hover:bg-green-50 dark:hover:bg-green-900/20"
                    )}
                    onClick={() => {
                      const firstDayOfMonth = new Date(year, monthData.month, 1);
                      setSelectedMonth(monthData.month);
                      onDateSelect(firstDayOfMonth);
                    }}
                  >
                    {MONTHS[monthData.month]}
                  </button>
                </div>
               
               {/* Month weeks */}
               <div className={cn("flex", isMobile ? "gap-1" : isCompact ? "gap-1" : "gap-1.5")}>
                 {monthData.weeks.map((week, weekIndex) => (
                   <div 
                     key={weekIndex} 
                     className={cn("flex flex-col", isMobile ? "gap-[0.424rem]" : isCompact ? "gap-[0.424rem]" : "gap-[0.552rem]")}
                     data-week-index={`${monthIndex}-${weekIndex}`}
                   >
                    
                    {week.map((date, dayIndex) => {
                      // Skip placeholder dates (epoch dates)
                      if (date.getFullYear() === 1970) {
                        return (
                          <div 
                             key={dayIndex} 
                             className={cn(
                               isMobile ? "w-6 h-6" : isTablet ? "w-4 h-4" : "w-3 h-3"
                             )}
                           />
                        );
                      }

                      const isSelected = selectedDate && isSameDay(date, selectedDate);
                      const isCurrentDay = isToday(date);
                      const taskCount = getTaskIntensity(date);
                      const isSelectedMonthDate = selectedMonth !== null && date.getMonth() === selectedMonth;
                      const hasMultipleTasks = getMultiTaskIndicator(date);
                      const isCurrentMonth = date.getMonth() === monthData.month;

                      return (
                        <Tooltip key={dayIndex}>
                          <TooltipTrigger asChild>
                            <button
                              onClick={() => {
                                if (isCurrentMonth) {
                                  setSelectedMonth(date.getMonth());
                                  onDateSelect(date);
                                }
                              }}
                              aria-label={isCurrentMonth ? `${format(date, 'MMMM d, yyyy')} - ${taskCount} task${taskCount !== 1 ? 's' : ''}` : undefined}
                              aria-pressed={isCurrentMonth ? isSelected : false}
                              tabIndex={isCurrentMonth ? 0 : -1}
                              onKeyDown={(e) => {
                                if (isCurrentMonth && (e.key === 'Enter' || e.key === ' ')) {
                                  e.preventDefault();
                                  setSelectedMonth(date.getMonth());
                                  onDateSelect(date);
                                }
                              }}
                              className={cn(
                                 isMobile ? "w-6 h-6 text-xs" : isTablet ? "w-4 h-4 text-xs" : "w-3 h-3 text-xs",
                                "rounded-lg transition-all duration-300 ease-in-out relative",
                                isCurrentMonth ? "cursor-pointer" : "cursor-default",
                                isCurrentMonth && "hover:scale-150 hover:z-[50]",
                                getTileColor(date),
                                !isCurrentMonth && "opacity-30", // Dim dates from other months
                                isCurrentMonth && isSelected && [
                                  "scale-125 shadow-lg z-[50]",
                                  "ring-2 ring-green-500 ring-offset-1"
                                ],
                                isCurrentMonth && isCurrentDay && [
                                  "ring-2 ring-green-500 ring-offset-0",
                                  "bg-green-100/80 dark:bg-green-900/40",
                                  !selectedDate && "animate-pulse",
                                  "flex items-center justify-center font-bold"
                                ],
                                isCurrentMonth && isSelectedMonthDate && [
                                  "before:absolute before:inset-0 before:bg-green-100/60 dark:before:bg-green-900/40 before:rounded-lg before:pointer-events-none",
                                  "hover:before:bg-green-200/70 dark:hover:before:bg-green-800/50"
                                ],
                                isCurrentMonth && "hover:ring-2 hover:ring-blue-400 hover:ring-offset-1"
                              )}
                            >
                              {isCurrentDay && (
                                <span className="text-green-800 dark:text-green-200 font-extrabold text-xs z-10 relative">
                                  T
                                </span>
                              )}

                              {hasMultipleTasks && (
                                <span className="absolute top-0.5 right-0.5 w-1 h-1 bg-white rounded-full shadow-sm z-10"></span>
                              )}
                            </button>
                          </TooltipTrigger>
                          {isCurrentMonth && (
                            <TooltipContent side="top" className="z-[60] bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
                              {format(date, 'MMM d, yyyy')} - {taskCount} task{taskCount !== 1 ? 's' : ''}
                              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rotate-45"></div>
                            </TooltipContent>
                          )}
                        </Tooltip>
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>
          ))}
            </div>
          </div>
        </div>
      </div>

      <div className={cn("flex items-center justify-center text-xs text-muted-foreground gap-4 flex-wrap", isMobile ? "mt-1 mb-1" : "mt-2 mb-2")}>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm bg-green-100 dark:bg-green-900/40 ring-2 ring-green-500 ring-offset-0 flex items-center justify-center">
            <span className="text-green-800 dark:text-green-200 font-extrabold text-[8px]">T</span>
          </div>
          <span>Today's Date</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm bg-gradient-to-br from-green-600 to-green-700 shadow-sm shadow-green-600/30" />
          <span>Low Priority</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm bg-gradient-to-br from-yellow-400 to-yellow-500 shadow-sm shadow-yellow-400/30" />
          <span>Medium Priority</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm bg-gradient-to-br from-red-500 to-red-600 shadow-sm shadow-red-500/30" />
          <span>High Priority</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm bg-gradient-to-br from-amber-600 to-amber-700 shadow-sm shadow-amber-600/30 relative">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
          </div>
          <span>Multiple Tasks</span>
        </div>
      </div>


    </div>
    </TooltipProvider>
  );
};