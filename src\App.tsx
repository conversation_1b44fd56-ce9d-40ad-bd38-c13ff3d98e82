import React, { useState } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { AuthProvider } from '@/contexts/AuthContext';
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import TestTour from "./pages/TestTour";
import Auth from "./pages/Auth";
import SimpleAuth from "./pages/SimpleAuth";

import Subscription from "./pages/Subscription";
import NotificationSettings from "./pages/NotificationSettings";

// Removed NotificationCenter and MobileNotificationCenter as part of Friends/Teams removal
import { Onboarding } from './pages/Onboarding';
import TaskDetailPage from './pages/TaskDetail';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // Disable automatic refetch when window gains focus
      refetchOnMount: true, // Keep refetch on component mount
      refetchOnReconnect: true, // Keep refetch on network reconnect
      staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    },
  },
});

const App = () => {
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
               <AuthProvider>
                 <Toaster />
                 <Routes>
                   <Route path="/" element={<Index />} />
                   <Route path="/auth" element={<Auth />} />
                   <Route path="/simple-auth" element={<SimpleAuth />} />
                   <Route path="/onboarding" element={<Onboarding />} />
                   <Route path="/settings/notifications" element={<NotificationSettings />} />
           
                   <Route path="/test-tour" element={<TestTour />} />
                   <Route path="/subscription" element={<Subscription />} />

                   <Route path="/tasks/:taskId" element={<TaskDetailPage />} />
                   <Route path="*" element={<NotFound />} />
                 </Routes>
               </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

export default App;