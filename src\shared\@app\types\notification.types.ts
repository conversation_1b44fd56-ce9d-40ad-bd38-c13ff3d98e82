// Notification-related types
export interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
}

export interface NotificationPayload {
  title: string;
  body: string;
  data?: {
    taskId?: string;
    type?: string;
    url?: string;
  };
  icon?: string;
  badge?: string;
  tag?: string;
  silent?: boolean;
}

export interface PushNotificationRegistration {
  userId: string;
  deviceToken: string;
  platform: 'web' | 'ios' | 'android';
  endpoint?: string;
  keys?: {
    p256dh: string;
    auth: string;
  };
}