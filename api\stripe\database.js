const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY // Use service role key for server-side operations
);

/**
 * Updates user subscription information in the database
 * @param {string} userId - The user's ID
 * @param {Object} subscriptionData - Subscription data to update
 */
async function updateUserSubscription(userId, subscriptionData) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...subscriptionData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating user subscription:', error);
      throw error;
    }

    console.log(`Successfully updated subscription for user ${userId}`);
    return data;
  } catch (error) {
    console.error('Database error updating subscription:', error);
    throw error;
  }
}

/**
 * Gets user by Stripe customer ID
 * @param {string} stripeCustomerId - The Stripe customer ID
 * @returns {Object|null} User object or null if not found
 */
async function getUserByStripeCustomerId(stripeCustomerId) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('stripe_customer_id', stripeCustomerId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error('Error getting user by Stripe customer ID:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Database error getting user by Stripe customer ID:', error);
    throw error;
  }
}

/**
 * Gets user by ID
 * @param {string} userId - The user's ID
 * @returns {Object|null} User object or null if not found
 */
async function getUserById(userId) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error('Error getting user by ID:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Database error getting user by ID:', error);
    throw error;
  }
}

/**
 * Updates user storage usage
 * @param {string} userId - The user's ID
 * @param {number} bytesUsed - Total bytes used
 */
async function updateUserStorageUsage(userId, bytesUsed) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .update({
        storage_used_bytes: bytesUsed,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating user storage usage:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Database error updating storage usage:', error);
    throw error;
  }
}

/**
 * Gets user's file attachments for storage calculation
 * @param {string} userId - The user's ID
 * @returns {Array} Array of file attachments
 */
async function getUserFileAttachments(userId) {
  try {
    const { data, error } = await supabase
      .from('file_attachments')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error('Error getting user file attachments:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Database error getting file attachments:', error);
    throw error;
  }
}

/**
 * Gets files that should be deleted based on retention policy
 * @param {string} userId - The user's ID
 * @param {number} retentionDays - Number of days to retain files
 * @returns {Array} Array of files to delete
 */
async function getFilesToDelete(userId, retentionDays) {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const { data, error } = await supabase
      .from('file_attachments')
      .select('*')
      .eq('user_id', userId)
      .lt('uploaded_at', cutoffDate.toISOString());

    if (error) {
      console.error('Error getting files to delete:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Database error getting files to delete:', error);
    throw error;
  }
}

/**
 * Deletes file attachment record from database
 * @param {string} fileId - The file attachment ID
 */
async function deleteFileAttachment(fileId) {
  try {
    const { error } = await supabase
      .from('file_attachments')
      .delete()
      .eq('id', fileId);

    if (error) {
      console.error('Error deleting file attachment:', error);
      throw error;
    }
  } catch (error) {
    console.error('Database error deleting file attachment:', error);
    throw error;
  }
}

/**
 * Sends trial ending email notification
 * @param {string} email - User's email address
 */
async function sendTrialEndingEmail(email) {
  try {
    // This is a placeholder - you'll need to implement your email service
    // You could use SendGrid, Mailgun, AWS SES, etc.
    console.log(`Sending trial ending email to: ${email}`);
    
    // Example implementation:
    // await emailService.send({
    //   to: email,
    //   template: 'trial-ending',
    //   data: {
    //     upgradeUrl: `${process.env.FRONTEND_URL}/subscription`,
    //   },
    // });
  } catch (error) {
    console.error('Error sending trial ending email:', error);
  }
}

/**
 * Sends subscription updated email notification
 * @param {string} email - User's email address
 * @param {string} status - New subscription status
 */
async function sendSubscriptionUpdatedEmail(email, status) {
  try {
    // This is a placeholder - you'll need to implement your email service
    console.log(`Sending subscription updated email to: ${email}, status: ${status}`);
    
    // Example implementation:
    // await emailService.send({
    //   to: email,
    //   template: 'subscription-updated',
    //   data: {
    //     status: status,
    //     manageUrl: `${process.env.FRONTEND_URL}/subscription/manage`,
    //   },
    // });
  } catch (error) {
    console.error('Error sending subscription updated email:', error);
  }
}

/**
 * Initializes a new user with default subscription settings
 * @param {string} userId - The user's ID
 * @param {Object} userData - User data including email, name, etc.
 */
async function initializeUserSubscription(userId, userData) {
  try {
    const trialEndsAt = new Date();
    trialEndsAt.setDate(trialEndsAt.getDate() + 7); // 7-day trial

    const { data, error } = await supabase
      .from('profiles')
      .update({
        subscription_tier: 'pro', // Start with Pro trial
        subscription_status: 'trial',
        trial_ends_at: trialEndsAt.toISOString(),
        storage_used_bytes: 0,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (error) {
      console.error('Error initializing user subscription:', error);
      throw error;
    }

    console.log(`Initialized subscription for user ${userId}`);
    return data;
  } catch (error) {
    console.error('Database error initializing user subscription:', error);
    throw error;
  }
}

module.exports = {
  updateUserSubscription,
  getUserByStripeCustomerId,
  getUserById,
  updateUserStorageUsage,
  getUserFileAttachments,
  getFilesToDelete,
  deleteFileAttachment,
  sendTrialEndingEmail,
  sendSubscriptionUpdatedEmail,
  initializeUserSubscription,
};
