import { injectable } from 'tsyringe'
import { logger } from '@/lib/logger'

@injectable()
export class RetryService {
  async withRetry<T>(
    operation: () => Promise<T>,
    maxAttempts = 3,
    delayMs = 1000
  ): Promise<T> {
    let attempt = 1
    let lastError: Error | null = null

    while (attempt <= maxAttempts) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error
        logger.warn(`Attempt ${attempt} failed: ${lastError.message}`)
        
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, delayMs * attempt))
        }
        attempt++
      }
    }

    logger.error(`All ${maxAttempts} attempts failed`)
    throw lastError || new Error('Unknown error occurred during retry')
  }
}