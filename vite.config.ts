import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { VitePWA } from "vite-plugin-pwa";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { analyzer } from 'vite-bundle-analyzer';

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => ({
  server: {
    host: "::",
    port: 8080,
    cors: true,
    hmr: {
      clientPort: 8080,
    },
    watch: {
      ignored: ['**/TaskManagerMobile/**']
    }
  },
  // Build optimizations
  build: {
    target: 'es2020',
    minify: 'esbuild',
    cssMinify: true,
    reportCompressedSize: false, // Faster builds
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // React ecosystem
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          // UI libraries
          'ui-vendor': [
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-toast',
            'lucide-react'
          ],
          // Data management
          'data-vendor': [
            '@supabase/supabase-js',
            '@tanstack/react-query',
            'zustand'
          ],
          // Authentication - using Supabase Auth
          // Analytics and charts
          'analytics-vendor': ['recharts'],
          // Utilities
          'utils-vendor': ['date-fns', 'clsx', 'tailwind-merge']
        }
      }
    },
    // Enable source maps in development
    sourcemap: mode === 'development'
  },
  // Performance optimizations
  optimizeDeps: {
    include: [
      'react',
      'react-dom', 
      'react-router-dom',
      '@supabase/supabase-js',
      'zustand',
      'date-fns',
      'lucide-react'
    ]
  },
  plugins: [
    react({
      jsxImportSource: 'react'
    }),
    VitePWA({
      registerType: 'autoUpdate',
      manifest: {
        name: "Professional Task Management",
        short_name: "TaskManager",
        description: "A professional task management application with calendar integration and voice notes",
        theme_color: "#1f2937",
        icons: [
          {
            src: "/icon-192x192.png",
            sizes: "192x192",
            type: "image/png",
            purpose: "any"
          },
          {
            src: "/icon-512x512.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "any"
          }
        ]
      },
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,json}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          }
        ]
      },
      devOptions: {
        enabled: mode === 'development',
        type: 'module',
        navigateFallback: 'index.html'
      }
    }),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
