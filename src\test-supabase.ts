import { supabase } from './lib/supabase'

// Test the Supabase connection
async function testSupabaseConnection() {
  console.log('Testing Supabase connection...')
  
  // Test basic connection
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('count')
      .limit(1)
    
    if (error) {
      console.error('Supabase connection error:', error)
    } else {
      console.log('Supabase connection successful')
    }
  } catch (error) {
    console.error('Failed to connect to Supabase:', error)
  }
  
  // Test table structure
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .limit(1)
    
    if (error) {
      console.error('Error querying tasks table:', error)
    } else {
      console.log('Tasks table structure looks good')
    }
  } catch (error) {
    console.error('Failed to query tasks table:', error)
  }
  
  // Test profiles table
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .limit(1)
    
    if (error) {
      console.error('Error querying profiles table:', error)
    } else {
      console.log('Profiles table structure looks good')
    }
  } catch (error) {
    console.error('Failed to query profiles table:', error)
  }
}

// Run test
testSupabaseConnection()
