import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { OnboardingService } from '@/services/onboardingService';

export const useOnboarding = () => {
  const { user, loading } = useAuth();
  const isSignedIn = !!user;
  const userId = user?.id;
  const isLoaded = !loading;
  const navigate = useNavigate();

  useEffect(() => {
    // Only check after auth is loaded
    if (!isLoaded) return;

    // If user is signed in, check if they need onboarding
    if (isSignedIn && userId) {
      const hasCompletedOnboarding = OnboardingService.hasCompletedOnboarding(userId);
      
      if (!hasCompletedOnboarding) {
        // Check if we're not already on the onboarding page
        if (window.location.pathname !== '/onboarding') {
          navigate('/onboarding');
        }
      }
    }
  }, [isSignedIn, userId, isLoaded, navigate]);

  return {
    isSignedIn,
    userId,
    isLoaded,
  };
};

export default useOnboarding;
