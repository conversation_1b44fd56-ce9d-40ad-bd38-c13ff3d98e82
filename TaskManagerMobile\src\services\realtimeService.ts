import { supabase } from '../lib/supabase';

export class RealtimeService {
  private static channels: Map<string, any> = new Map();

  static async initializeRealtimeSync(userId: string) {
    try {
      // Subscribe to tasks changes
      const tasksChannel = supabase
        .channel(`tasks-${userId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'tasks',
            filter: `user_id=eq.${userId}`,
          },
          (payload) => {
            this.handleTaskChange(payload);
          }
        )
        .subscribe();



      // Subscribe to voice notes changes
      const voiceNotesChannel = supabase
        .channel(`voice-notes-${userId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'voice_notes',
            filter: `user_id=eq.${userId}`,
          },
          (payload) => {
            this.handleVoiceNoteChange(payload);
          }
        )
        .subscribe();

      // Subscribe to attachments changes
      const attachmentsChannel = supabase
        .channel(`attachments-${userId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'task_attachments',
            filter: `user_id=eq.${userId}`,
          },
          (payload) => {
            this.handleAttachmentChange(payload);
          }
        )
        .subscribe();

      this.channels.set('tasks', tasksChannel);
      this.channels.set('voice-notes', voiceNotesChannel);
      this.channels.set('attachments', attachmentsChannel);

      console.log('Real-time sync initialized');
    } catch (error) {
      console.error('Error initializing real-time sync:', error);
    }
  }

  static handleTaskChange(payload: any) {
    // Emit event for task changes
    const event = new CustomEvent('task-change', { detail: payload });
    window.dispatchEvent(event);
  }



  static handleVoiceNoteChange(payload: any) {
    // Emit event for voice note changes
    const event = new CustomEvent('voice-note-change', { detail: payload });
    window.dispatchEvent(event);
  }

  static handleAttachmentChange(payload: any) {
    // Emit event for attachment changes
    const event = new CustomEvent('attachment-change', { detail: payload });
    window.dispatchEvent(event);
  }

  static disconnectAll() {
    this.channels.forEach((channel, name) => {
      supabase.removeChannel(channel);
    });
    this.channels.clear();
    console.log('All real-time connections disconnected');
  }

  static subscribeToTaskChanges(callback: (payload: any) => void) {
    const listener = (event: CustomEvent) => callback(event.detail);
    window.addEventListener('task-change', listener as EventListener);
    return () => window.removeEventListener('task-change', listener as EventListener);
  }



  static subscribeToVoiceNoteChanges(callback: (payload: any) => void) {
    const listener = (event: CustomEvent) => callback(event.detail);
    window.addEventListener('voice-note-change', listener as EventListener);
    return () => window.removeEventListener('voice-note-change', listener as EventListener);
  }

  static subscribeToAttachmentChanges(callback: (payload: any) => void) {
    const listener = (event: CustomEvent) => callback(event.detail);
    window.addEventListener('attachment-change', listener as EventListener);
    return () => window.removeEventListener('attachment-change', listener as EventListener);
  }
}

// React Native event emitter alternative
export class RealtimeServiceRN {
  private static listeners: Map<string, Function[]> = new Map();

  static emit(event: string, data: any) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback(data));
  }

  static on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
    
    return () => {
      const callbacks = this.listeners.get(event) || [];
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    };
  }
}