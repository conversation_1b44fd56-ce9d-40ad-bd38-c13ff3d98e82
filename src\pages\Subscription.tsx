import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, CreditCard, Shield, Zap } from 'lucide-react';
import { SubscriptionPlans } from '@/components/subscription/SubscriptionPlans';
import { StorageUsageCalculator } from '@/components/subscription/StorageUsageCalculator';
import { useSubscriptionStore } from '@/store/subscriptionStore';
import { useSubscriptionService } from '@/services/subscriptionService';
import { useToast } from '@/hooks/use-toast';

const Subscription: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { subscription, isProUser } = useSubscriptionStore();
  const { redirectToCheckout, redirectToCustomerPortal } = useSubscriptionService();
  const { toast } = useToast();

  const handlePlanSelect = async (planId: string, billingCycle: 'monthly' | 'yearly') => {
    if (planId === 'free') {
      // Handle downgrade to free
      if (subscription?.stripeCustomerId) {
        // Redirect to customer portal to cancel subscription
        try {
          await redirectToCustomerPortal(subscription.stripeCustomerId);
        } catch (error) {
          toast({
            title: 'Error',
            description: 'Failed to open billing portal',
            variant: 'destructive'
          });
          console.error('Error opening customer portal:', error);
        }
      }
      return;
    }

    setIsLoading(true);
    try {
      await redirectToCheckout(planId, billingCycle);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to redirect to checkout',
        variant: 'destructive'
      });
      console.error('Error redirecting to checkout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleManageBilling = async () => {
    if (!subscription?.stripeCustomerId) {
      toast({
        title: 'Error',
        description: 'No billing information found',
        variant: 'destructive'
      });
      return;
    }

    try {
      await redirectToCustomerPortal(subscription.stripeCustomerId);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to open billing portal',
        variant: 'destructive'
      });
      console.error('Error opening customer portal:', error);
    }
  };

  const handleUpgradeClick = () => {
    // Scroll to plans section
    const plansSection = document.getElementById('subscription-plans');
    if (plansSection) {
      plansSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleCleanupClick = () => {
    // Navigate to file cleanup page or show cleanup modal
    navigate('/files/cleanup');
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to App
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Subscription & Billing</h1>
          <p className="text-muted-foreground">
            Manage your subscription and monitor your usage
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left column - Plans */}
        <div className="lg:col-span-2">
          <div id="subscription-plans">
            <SubscriptionPlans
              onPlanSelect={handlePlanSelect}
              currentPlanId={subscription?.tier || 'free'}
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Right column - Storage & Management */}
        <div className="space-y-6">
          {/* Storage Usage */}
          <StorageUsageCalculator
            onUpgradeClick={handleUpgradeClick}
            onCleanupClick={handleCleanupClick}
          />

          {/* Billing Management */}
          {subscription?.stripeCustomerId && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Billing Management
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Manage your payment methods, download invoices, and update billing information.
                </p>
                <Button
                  onClick={handleManageBilling}
                  className="w-full"
                  variant="outline"
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Manage Billing
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Security & Trust */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security & Privacy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Zap className="h-4 w-4 text-green-500" />
                <span>256-bit SSL encryption</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Zap className="h-4 w-4 text-green-500" />
                <span>PCI DSS compliant</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Zap className="h-4 w-4 text-green-500" />
                <span>GDPR compliant</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Zap className="h-4 w-4 text-green-500" />
                <span>SOC 2 Type II certified</span>
              </div>
            </CardContent>
          </Card>

          {/* Money Back Guarantee */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>30-day money-back guarantee</strong>
              <br />
              Try our Pro plan risk-free. If you're not satisfied, we'll refund your money.
            </AlertDescription>
          </Alert>
        </div>
      </div>

      {/* FAQ Section */}
      <Card className="mt-12">
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Can I cancel anytime?</h4>
              <p className="text-sm text-muted-foreground">
                Yes, you can cancel your subscription at any time. You'll continue to have access to Pro features until the end of your billing period.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">What happens to my data if I cancel?</h4>
              <p className="text-sm text-muted-foreground">
                Your data remains safe. Free tier limitations will apply, but you won't lose any existing tasks or files.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Can I upgrade or downgrade my plan?</h4>
              <p className="text-sm text-muted-foreground">
                Yes, you can change your plan at any time. Changes take effect immediately, and billing is prorated.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Are there any setup fees?</h4>
              <p className="text-sm text-muted-foreground">
                No, there are no setup fees or hidden costs. You only pay for your subscription plan.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Subscription;
