# Enhanced Features Integration Guide

This guide provides comprehensive instructions for integrating all enhanced features into your Task Manager application.

## Overview

The enhanced features include:
1. **Advanced Filtering & Search** - Full-text search, multi-criteria filtering, saved presets
2. **Recurring Tasks** - Daily/weekly/monthly/yearly patterns with custom recurrence
3. **Enhanced Analytics** - Color-coded visualizations and flexible reporting
4. **File Management** - Preview and deletion for images, audio, and PDFs
5. **Advanced Calendar** - Configurable week start and visual indicators
6. **Offline Capabilities** - AsyncStorage caching with sync queue
7. **Enhanced Export** - CSV/JSON/TXT formats with comprehensive reports
8. **Push Notifications** - Mobile and web push notifications
9. **Home Screen Widgets** - iOS/Android widgets and web widgets

## Installation

### Dependencies

Install the required dependencies:

```bash
# Core dependencies
npm install @react-native-async-storage/async-storage
npm install react-native-calendars
npm install react-native-document-picker
npm install react-native-fs
npm install react-native-image-picker
npm install react-native-sound
npm install react-native-video
npm install react-native-pdf
npm install react-native-share
npm install xlsx
npm install papaparse

# For push notifications (optional)
npm install expo-notifications
npm install expo-device

# For widgets (optional)
npm install react-native-widget-extension
```

### Database Schema Updates

Add the following columns to your `profiles` table:

```sql
ALTER TABLE profiles ADD COLUMN notification_settings JSONB DEFAULT '{}';
ALTER TABLE profiles ADD COLUMN push_token TEXT;
ALTER TABLE profiles ADD COLUMN widget_enabled BOOLEAN DEFAULT true;
```

Add the following columns to your `tasks` table:

```sql
ALTER TABLE tasks ADD COLUMN recurring_task_id UUID REFERENCES recurring_tasks(id);
ALTER TABLE tasks ADD COLUMN file_urls TEXT[] DEFAULT '{}';
ALTER TABLE tasks ADD COLUMN voice_notes TEXT[] DEFAULT '{}';
```

Create the `recurring_tasks` table:

```sql
CREATE TABLE recurring_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'pending',
  priority TEXT DEFAULT 'medium',
  category TEXT,
  pattern JSONB NOT NULL,
  frequency INTEGER DEFAULT 1,
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE,
  next_occurrence TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Integration Steps

### 1. Advanced Filtering & Search

**Files to integrate:**
- `src/components/AdvancedSearchFilter.tsx`
- `src/services/searchService.ts`

**Usage:**
```typescript
import { AdvancedSearchFilter } from './components/AdvancedSearchFilter';

// In your task list component
<AdvancedSearchFilter
  visible={showFilter}
  onClose={() => setShowFilter(false)}
  onApplyFilters={handleApplyFilters}
  savedFilters={savedFilters}
/>
```

### 2. Recurring Tasks

**Files to integrate:**
- `src/services/recurringTaskService.ts`
- `src/components/RecurringTaskManager.tsx`

**Usage:**
```typescript
import { RecurringTaskManager } from './components/RecurringTaskManager';
import { RecurringTaskService } from './services/recurringTaskService';

// Create recurring task
const recurringTaskId = await RecurringTaskService.createRecurringTask(
  taskData,
  RecurringTaskService.createWeeklyPattern([1, 3, 5]), // Mon, Wed, Fri
  1,
  new Date('2024-12-31')
);

// In task creation/editing
<RecurringTaskManager
  task={currentTask}
  visible={showRecurringModal}
  onClose={() => setShowRecurringModal(false)}
  onSave={handleRecurringSave}
/>
```

### 3. Enhanced Analytics

**Files to integrate:**
- `src/screens/EnhancedAnalyticsScreen.tsx`

**Usage:**
```typescript
import { EnhancedAnalyticsScreen } from './screens/EnhancedAnalyticsScreen';

// Add to navigation
<Stack.Screen name="Analytics" component={EnhancedAnalyticsScreen} />
```

### 4. File Management

**Files to integrate:**
- `src/components/FilePreview.tsx`
- `src/services/fileService.ts`

**Usage:**
```typescript
import { FilePreview } from './components/FilePreview';

// In task detail view
<FilePreview
  files={task.files || []}
  onDelete={handleFileDelete}
  onAdd={handleFileAdd}
/>
```

### 5. Advanced Calendar

**Files to integrate:**
- `src/components/AdvancedCalendar.tsx`

**Usage:**
```typescript
import { AdvancedCalendar } from './components/AdvancedCalendar';

<AdvancedCalendar
  tasks={tasks}
  onTaskPress={handleTaskPress}
  weekStartsOn="monday"
/>
```

### 6. Offline Capabilities

**Files to integrate:**
- `src/services/offlineService.ts`

**Usage:**
```typescript
import { offlineService } from './services/offlineService';

// Initialize offline service
await offlineService.initialize();

// Sync when online
await offlineService.syncPendingChanges();
```

### 7. Enhanced Export

**Files to integrate:**
- `src/services/enhancedExportService.ts`

**Usage:**
```typescript
import { enhancedExportService } from './services/enhancedExportService';

// Export tasks
await enhancedExportService.exportToCSV(tasks, 'my-tasks');
await enhancedExportService.exportToJSON(tasks, 'my-tasks');
await enhancedExportService.exportToTXT(tasks, 'my-tasks');
```

### 8. Push Notifications

**Files to integrate:**
- `src/services/pushNotificationService.ts`
- `src/screens/NotificationSettingsScreen.tsx`

**Usage:**
```typescript
import { pushNotificationService } from './services/pushNotificationService';
import { NotificationSettingsScreen } from './screens/NotificationSettingsScreen';

// Initialize notifications
await pushNotificationService.initialize();

// Schedule task reminder
await pushNotificationService.scheduleTaskReminder(task, 30);

// Add settings screen
<Stack.Screen name="NotificationSettings" component={NotificationSettingsScreen} />
```

### 9. Home Screen Widgets

**Files to integrate:**
- `src/services/widgetService.ts`

**Usage:**
```typescript
import { widgetService } from './services/widgetService';

// Initialize widget service
await widgetService.initialize();

// Update widget data
await widgetService.updateWidget();

// Get widget data
const widgetData = await widgetService.getWidgetData();
```

## Platform-Specific Setup

### iOS Setup

1. **Push Notifications:**
   - Enable Push Notifications in Xcode capabilities
   - Add the following to `Info.plist`:
   ```xml
   <key>UIBackgroundModes</key>
   <array>
     <string>remote-notification</string>
   </array>
   ```

2. **Widgets:**
   - Add a new Widget Extension target in Xcode
   - Configure widget sizes in `WidgetConfiguration`

### Android Setup

1. **Push Notifications:**
   - Add Firebase configuration to `android/app/google-services.json`
   - Add to `android/app/build.gradle`:
   ```gradle
   implementation 'com.google.firebase:firebase-messaging:23.0.0'
   ```

2. **Widgets:**
   - Create widget provider in `android/app/src/main/java/.../widget/`
   - Add widget configuration to `AndroidManifest.xml`

### Web Setup

1. **Push Notifications:**
   - Create `public/sw.js` for service worker
   - Configure web push notifications

2. **Widgets:**
   - Create PWA with service worker
   - Add widget-like components

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Push notifications
EXPO_PUSH_TOKEN_KEY=your_push_token_key
VAPID_PUBLIC_KEY=your_vapid_public_key

# File storage
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Analytics
ANALYTICS_ENABLED=true
```

### Navigation Updates

Add the new screens to your navigation:

```typescript
// In your navigation file
import { EnhancedAnalyticsScreen } from './screens/EnhancedAnalyticsScreen';
import { NotificationSettingsScreen } from './screens/NotificationSettingsScreen';

<Stack.Screen name="Analytics" component={EnhancedAnalyticsScreen} />
<Stack.Screen name="NotificationSettings" component={NotificationSettingsScreen} />
```

## Testing

### Unit Tests

```bash
# Run tests for all services
npm test src/services/*.test.ts

# Run tests for components
npm test src/components/*.test.tsx
```

### Integration Tests

```bash
# Test offline functionality
npm test src/services/offlineService.test.ts

# Test push notifications
npm test src/services/pushNotificationService.test.ts
```

## Troubleshooting

### Common Issues

1. **Push notifications not working:**
   - Check device permissions
   - Verify push token is saved to database
   - Test with Expo push notification tool

2. **Widgets not updating:**
   - Ensure widget service is initialized
   - Check network connectivity
   - Verify widget configuration

3. **Offline sync issues:**
   - Check AsyncStorage permissions
   - Verify sync queue implementation
   - Test with airplane mode

4. **File upload issues:**
   - Check file size limits
   - Verify file type restrictions
   - Test with different file formats

## Performance Optimization

### Caching Strategy

- Use AsyncStorage for offline data
- Implement request caching
- Use React Query for data fetching

### Memory Management

- Implement proper cleanup in useEffect
- Use FlatList for large lists
- Optimize image loading

## Security Considerations

- Validate all file uploads
- Sanitize user input
- Use secure storage for sensitive data
- Implement proper authentication

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review the example implementations
3. Submit issues to the project repository