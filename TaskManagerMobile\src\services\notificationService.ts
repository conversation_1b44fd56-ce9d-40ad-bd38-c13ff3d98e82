import { supabase } from '../config/supabase';
import { Task } from '../types/task';

export interface UnifiedNotification {
  id: string;
  user_id: string;
  type: 'task_reminder' | 'task_overdue' | 'task_assigned' | 'task_completed' | 'daily_digest' | 'weekly_report' | 'system_update';
  title: string;
  message: string;
  data?: {
    task_id?: string;
    assigned_by?: string;
    assigned_to?: string;
    action_url?: string;
    [key: string]: any;
  };
  channels: ('email' | 'push' | 'in_app' | 'sms')[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  sent_at?: string;
  delivered_channels: string[];
  created_at: string;
  updated_at: string;
}

export interface NotificationSettings {
  enabled: boolean;
  preferences: {
    email: boolean;
    push: boolean;
    inApp: boolean;
    sms: boolean;
  };
  taskReminders: boolean;
  overdueAlerts: boolean;
  assignmentNotifications: boolean;
  completionNotifications: boolean;
  dailyDigest: boolean;
  weeklyReport: boolean;
  systemUpdates: boolean;
  reminderTime: number;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
  digestTime: string;
  weeklyReportDay: number;
}

export class UnifiedNotificationService {
  private static instance: UnifiedNotificationService;
  private settings: NotificationSettings | null = null;
  private realtimeChannel: any = null;

  private constructor() {}

  static getInstance(): UnifiedNotificationService {
    if (!UnifiedNotificationService.instance) {
      UnifiedNotificationService.instance = new UnifiedNotificationService();
    }
    return UnifiedNotificationService.instance;
  }

  /**
   * Initialize the notification service for a user
   */
  async initialize(userId: string): Promise<void> {
    await this.loadSettings(userId);
    await this.setupRealtimeSubscription(userId);
  }

  /**
   * Load user notification settings
   */
  private async loadSettings(userId: string): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('notification_preferences')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (data?.notification_preferences) {
        this.settings = data.notification_preferences as any;
      } else {
        this.settings = this.getDefaultSettings();
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
      this.settings = this.getDefaultSettings();
    }
  }

  /**
   * Get default notification settings
   */
  private getDefaultSettings(): NotificationSettings {
    return {
      enabled: true,
      preferences: {
        email: true,
        push: true,
        inApp: true,
        sms: false,
      },
      taskReminders: true,
      overdueAlerts: true,
      assignmentNotifications: true,
      completionNotifications: true,
      dailyDigest: false,
      weeklyReport: false,
      systemUpdates: true,
      reminderTime: 15,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
      },
      digestTime: '09:00',
      weeklyReportDay: 1,
    };
  }

  /**
   * Update notification settings
   */
  async updateSettings(userId: string, newSettings: Partial<NotificationSettings>): Promise<void> {
    this.settings = { ...this.settings!, ...newSettings };

    const { error } = await supabase
      .from('profiles')
      .update({ notification_preferences: this.settings })
      .eq('id', userId);

    if (error) throw error;
  }

  /**
   * Get current settings
   */
  getSettings(): NotificationSettings | null {
    return this.settings;
  }

  /**
   * Setup real-time subscription for notifications
   */
  private async setupRealtimeSubscription(userId: string): Promise<void> {
    if (this.realtimeChannel) {
      await supabase.removeChannel(this.realtimeChannel);
    }

    this.realtimeChannel = supabase
      .channel(`notifications_${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'unified_notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          this.handleRealtimeNotification(payload.new as UnifiedNotification);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'unified_notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          this.handleNotificationUpdate(payload.new as UnifiedNotification);
        }
      )
      .subscribe();
  }

  /**
   * Handle real-time notification
   */
  private handleRealtimeNotification(notification: UnifiedNotification): void {
    console.log('New notification received:', notification);
    
    // Show local notification if app is in background
    if (this.settings?.preferences.push && notification.channels.includes('push')) {
      // Implementation would use Expo Notifications
      console.log('Would show push notification:', notification.title);
    }
  }

  /**
   * Handle notification update
   */
  private handleNotificationUpdate(notification: UnifiedNotification): void {
    console.log('Notification updated:', notification);
  }

  /**
   * Send notification (mobile version - simplified)
   */
  async sendNotification(
    userId: string,
    type: UnifiedNotification['type'],
    title: string,
    message: string,
    data?: UnifiedNotification['data'],
    priority: UnifiedNotification['priority'] = 'medium'
  ): Promise<string> {
    if (!this.settings?.enabled) return '';

    const channels = this.getChannelsForNotification(type);
    if (channels.length === 0) return '';

    const notification: Omit<UnifiedNotification, 'id' | 'created_at' | 'updated_at'> = {
      user_id: userId,
      type,
      title,
      message,
      data,
      channels,
      priority,
      read: false,
      delivered_channels: ['in_app'],
    };

    const { data: created, error } = await supabase
      .from('unified_notifications')
      .insert(notification)
      .select()
      .single();

    if (error) throw error;
    return created.id;
  }

  /**
   * Determine channels for notification type
   */
  private getChannelsForNotification(type: UnifiedNotification['type']): string[] {
    if (!this.settings) return [];

    const channels: string[] = [];
    const { preferences } = this.settings;

    if (preferences.inApp) {
      channels.push('in_app');
    }

    switch (type) {
      case 'task_reminder':
        if (this.settings.taskReminders) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
        }
        break;
      case 'task_overdue':
        if (this.settings.overdueAlerts) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
        }
        break;
      case 'task_assigned':
        if (this.settings.assignmentNotifications) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
        }
        break;
      case 'task_completed':
        if (this.settings.completionNotifications) {
          if (preferences.email) channels.push('email');
          if (preferences.push) channels.push('push');
        }
        break;
    }

    return channels;
  }
  /**
   * Get notifications for user
   */
  async getNotifications(userId: string, limit: number = 50): Promise<UnifiedNotification[]> {
    const { data, error } = await supabase
      .from('unified_notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return (data || []) as UnifiedNotification[];
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('unified_notifications')
      .update({ read: true })
      .eq('id', notificationId);

    if (error) throw error;
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(userId: string): Promise<void> {
    const { error } = await supabase
      .from('unified_notifications')
      .update({ read: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) throw error;
  }

  /**
   * Get unread count
   */
  async getUnreadCount(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('unified_notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) throw error;
    return count || 0;
  }

  /**
   * Schedule task notifications
   */
  async scheduleTaskNotifications(task: Task): Promise<void> {
    if (!this.settings?.enabled || !this.settings?.taskReminders) return;

    if (task.dueDate && task.status !== 'completed') {
      const dueTime = new Date(task.dueDate).getTime();
      const reminderTime = dueTime - (this.settings.reminderTime * 60 * 1000);
      const now = Date.now();

      // Schedule reminder notification
      if (reminderTime > now) {
        setTimeout(async () => {
          await this.sendNotification(
            task.user_id,
            'task_reminder',
            'Task Reminder',
            `"${task.title}" is due in ${this.settings!.reminderTime} minutes`,
            { task_id: task.id },
            'medium'
          );
        }, reminderTime - now);
      }

      // Schedule overdue notification
      if (dueTime > now) {
        setTimeout(async () => {
          // Check if task is still not completed
          const { data: currentTask } = await supabase
            .from('tasks')
            .select('status')
            .eq('id', task.id)
            .single();

          if (currentTask && currentTask.status !== 'completed') {
            await this.sendNotification(
              task.user_id,
              'task_overdue',
              'Task Overdue',
              `"${task.title}" is now overdue`,
              { task_id: task.id },
              'high'
            );
          }
        }, dueTime - now + 60000);
      }
    }
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.realtimeChannel) {
      supabase.removeChannel(this.realtimeChannel);
    }
  }
}

// Export singleton instance
export const unifiedNotificationService = UnifiedNotificationService.getInstance();