import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext';
import { TaskService, FileAttachmentService } from '@/services/taskService'
import { StandardizedRecurringService } from '@/services/standardizedRecurringService'
import { Task, TasksByDate } from '@/types/task'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/lib/supabase'

export const useSupabaseTasks = () => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Process recurring tasks and fetch all tasks
  const fetchTasks = useCallback(async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)
      
      // Process due recurring tasks first
      await StandardizedRecurringService.processDueRecurringTasks()
      
      // Then fetch all tasks including newly generated ones
      const fetchedTasks = await TaskService.fetchTasks(user.id, supabase)
      setTasks(fetchedTasks)
    } catch (err: any) {
      console.error('Error fetching tasks:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [user])

  // Migrate from localStorage on first load
  useEffect(() => {
    const migrateAndFetch = async () => {
      if (!user) return

      // Check if migration has already been attempted for this user
      const migrationKey = `migration-completed-${user.id}`
      const migrationCompleted = localStorage.getItem(migrationKey)
      
      if (!migrationCompleted) {
        try {
          // Check if there's data in localStorage to migrate
          const localTasks = localStorage.getItem('scheduled-tasks')
          if (localTasks) {
            const tasks = JSON.parse(localTasks)
            // Only migrate if there are actual tasks to migrate
            if (tasks && tasks.length > 0) {
              await TaskService.migrateFromLocalStorage(user.id)
              toast({
                title: 'Data Migrated',
                description: 'Your tasks have been migrated to cloud storage',
              })
            }
          }
          // Mark migration as completed for this user
          localStorage.setItem(migrationKey, 'true')
        } catch (err: any) {
          console.error('Migration error:', err)
          toast({
            title: 'Migration Warning',
            description: 'Some tasks may not have been migrated properly',
            variant: 'destructive',
          })
        }
      }

      // Fetch tasks after migration attempt - call directly to avoid dependency issues
      try {
        setLoading(true)
        setError(null)
        
        // Process due recurring tasks first
        await StandardizedRecurringService.processDueRecurringTasks()
        
        // Then fetch all tasks including newly generated ones
        const fetchedTasks = await TaskService.fetchTasks(user.id, supabase)
        setTasks(fetchedTasks)
      } catch (err: any) {
        console.error('Error fetching tasks:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    migrateAndFetch()
  }, [user])

  // Periodic recurring task processing
  useEffect(() => {
    if (!user) return

    // Process recurring tasks immediately on mount
    const processRecurringTasks = async () => {
      try {
        const generatedTasks = await StandardizedRecurringService.processDueRecurringTasks()
        if (generatedTasks.length > 0) {
          console.log(`Generated ${generatedTasks.length} recurring task instances`)
          // Refresh tasks to include newly generated ones
          const updatedTasks = await TaskService.fetchTasks(user.id, supabase)
          setTasks(updatedTasks)
        }
      } catch (error) {
        console.error('Error processing recurring tasks:', error)
      }
    }

    // Process immediately
    processRecurringTasks()

    // Set up periodic processing every 30 minutes
    const intervalId = setInterval(processRecurringTasks, 30 * 60 * 1000)

    return () => {
      clearInterval(intervalId)
    }
  }, [user])

  // Optimized real-time subscription with minimal re-renders
  useEffect(() => {
    if (!user?.id) return // Only proceed if user has an ID

    let timeoutId: NodeJS.Timeout | null = null
    let subscription: any = null
    let isComponentMounted = true

    const setupSubscription = () => {
      // Don't set up subscription if component is unmounted
      if (!isComponentMounted) return
      
      // Clean up existing subscription
      if (subscription) {
        subscription.unsubscribe()
      }

      // Single subscription for both tasks and file_attachments changes
      subscription = supabase
        .channel(`user_${user.id}_changes`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'tasks',
            filter: `user_id=eq.${user.id}`,
          },
          (payload) => {
            // Only process if component is still mounted
            if (!isComponentMounted) return
            
            // Debounce updates to prevent rapid re-renders
            if (timeoutId) clearTimeout(timeoutId)
            timeoutId = setTimeout(async () => {
              if (!isComponentMounted) return
              
              try {
                // For INSERT and DELETE, refetch all tasks
                if (payload.eventType === 'INSERT' || payload.eventType === 'DELETE') {
                  const updatedTasks = await TaskService.fetchTasks(user.id, supabase)
                  if (isComponentMounted) {
                    setTasks(updatedTasks)
                  }
                } else if (payload.eventType === 'UPDATE' && payload.new) {
                  // For UPDATE, only update the specific task to preserve attachment metadata
                  const updatedTask = TaskService.transformTask(payload.new as any)
                  if (isComponentMounted) {
                    setTasks(prev => prev.map(task => 
                      task.id === updatedTask.id ? updatedTask : task
                    ))
                  }
                }
              } catch (err) {
                console.error('Error updating tasks:', err)
              }
            }, 200) // Reduced debounce for more responsive updates
          }
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'file_attachments',
            filter: `user_id=eq.${user.id}`,
          },
          (payload) => {
            // Only process if component is still mounted
            if (!isComponentMounted) return
            
            // Debounce updates to prevent rapid re-renders
            if (timeoutId) clearTimeout(timeoutId)
            timeoutId = setTimeout(async () => {
              if (!isComponentMounted) return
              
              try {
                // Only refetch if it's an INSERT or DELETE of attachments
                if (payload.eventType === 'INSERT' || payload.eventType === 'DELETE') {
                  const updatedTasks = await TaskService.fetchTasks(user.id, supabase)
                  if (isComponentMounted) {
                    setTasks(updatedTasks)
                  }
                }
                // Ignore UPDATE events for file_attachments to reduce refreshes
              } catch (err) {
                console.error('Error updating tasks after file change:', err)
              }
            }, 300) // Reduced debounce for file attachment changes
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('Real-time subscription established for user:', user.id)
          }
        })
    }

    // Delay subscription setup to avoid conflicts during auth state changes
    const delayId = setTimeout(setupSubscription, 2000) // Increased delay to prevent conflicts during login
    
    return () => {
      isComponentMounted = false
      if (delayId) clearTimeout(delayId)
      if (timeoutId) clearTimeout(timeoutId)
      if (subscription) {
        subscription.unsubscribe()
      }
    }
  }, [user?.id]) // Only depend on user.id to prevent unnecessary re-subscriptions

  // Create task
  const createTask = useCallback(async (taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {
    console.log('=== useSupabaseTasks.createTask DEBUG ===');
    console.log('User in hook:', user);
    console.log('User ID in hook:', user?.id);
    console.log('Task data in hook:', taskData);
    
    if (!user) {
      console.error('No user found when creating task')
      throw new Error('No authenticated user found')
    }

    try {
      console.log('Calling TaskService.createTask...');
      const newTask = await TaskService.createTask(taskData, user.id, supabase)
      console.log('TaskService.createTask returned:', newTask);
      setTasks(prev => [newTask, ...prev])
      toast({
        title: 'Task Created',
        description: 'Your task has been created successfully',
      })
      return newTask
    } catch (err: any) {
      console.error('Error creating task:', err)
      toast({
        title: 'Error',
        description: 'Failed to create task',
        variant: 'destructive',
      })
      throw err
    }
  }, [user])

  // Update task
  const updateTask = useCallback(async (taskId: string, updates: Partial<Task>) => {
    if (!user) return

    try {
      const updatedTask = await TaskService.updateTask(taskId, updates, user.id, supabase)
      setTasks(prev => prev.map(task => task.id === taskId ? updatedTask : task));
      return updatedTask
    } catch (err: any) {
      console.error('Error updating task:', err)
      toast({
        title: 'Error',
        description: 'Failed to update task',
        variant: 'destructive',
      })
      throw err
    }
  }, [user])

  // Delete task
  const deleteTask = useCallback(async (taskId: string) => {
    if (!user) return

    try {
      await TaskService.deleteTask(taskId, user.id, supabase)
      setTasks(prev => prev.filter(task => task.id !== taskId))
      toast({
        title: 'Task Deleted',
        description: 'Your task has been deleted successfully',
      })
    } catch (err: any) {
      console.error('Error deleting task:', err)
      toast({
        title: 'Error',
        description: 'Failed to delete task',
        variant: 'destructive',
      })
      throw err
    }
  }, [user])

  // Upload file attachment
  const uploadFile = useCallback(async (file: File, taskId: string) => {
    if (!user) {
      console.error('No current user found')
      return
    }

    try {
      const fileUrl = await FileAttachmentService.uploadFile(file, user.id, taskId, supabase)
      return fileUrl
    } catch (err: any) {
      console.error('Error uploading file:', err)
      toast({
        title: 'Error',
        description: `Failed to upload file: ${err.message}`,
        variant: 'destructive',
      })
      throw err
    }
  }, [user])

  // Upload multiple files for a task
  const uploadFiles = useCallback(async (files: File[], taskId: string) => {
    if (!user) return []

    const uploadPromises = files.map(file => 
      FileAttachmentService.uploadFile(file, user.id, taskId, supabase)
    )

    try {
      const uploadedUrls = await Promise.all(uploadPromises)
      return uploadedUrls
    } catch (err: any) {
      console.error('Error uploading files:', err)
      toast({
        title: 'Error',
        description: 'Failed to upload some files',
        variant: 'destructive',
      })
      throw err
    }
  }, [user])

  // Group tasks by date
  const tasksByDate: TasksByDate = TaskService.groupTasksByDate(tasks)

  // Get tasks for specific date
  const getTasksForDate = useCallback((date: Date): Task[] => {
    const dateKey = date.toISOString().split('T')[0]
    return tasksByDate[dateKey] || []
  }, [tasksByDate])

  // Update task status
  const updateTaskStatus = useCallback(async (taskId: string, status: Task['status']) => {
    const updates: Partial<Task> = { status }
    
    // Add completion timestamp if marking as completed
    if (status === 'completed') {
      updates.completedAt = new Date().toISOString()
    } else {
      updates.completedAt = undefined
    }

    const result = await updateTask(taskId, updates)
    
    return result
  }, [updateTask])

  // Add comment to task
  const addComment = useCallback(async (taskId: string, comment: string) => {
    const task = tasks.find(t => t.id === taskId)
    if (!task) {
      return;
    }

    const newComment = {
      id: Date.now().toString(),
      text: comment,
      timestamp: new Date().toISOString(),
    }

    const updatedComments = [...(task.comments || []), newComment]
    
    const result = await updateTask(taskId, { comments: updatedComments })
    return result
  }, [tasks, updateTask])

  // Update task priority
  const updateTaskPriority = useCallback(async (taskId: string, priority: Task['priority']) => {
    return updateTask(taskId, { priority })
  }, [updateTask])

  // Update task category
  const updateTaskCategory = useCallback(async (taskId: string, category: string) => {
    return updateTask(taskId, { category })
  }, [updateTask])

  return {
    tasks,
    tasksByDate,
    loading,
    error,
    createTask,
    updateTask,
    deleteTask,
    uploadFile,
    uploadFiles,
    getTasksForDate,
    updateTaskStatus,
    addComment,
    updateTaskPriority,
    updateTaskCategory,
    refetch: fetchTasks,
  }
}