import { supabase } from '@/lib/supabase'
import { Task, TasksByDate } from '@/types/task'
import { Database } from '@/integrations/supabase/types'
import { SupabaseClient } from '@supabase/supabase-js'
import { normalizeTaskStatus } from '@/lib/statusUtils'

type TaskRow = Database['public']['Tables']['tasks']['Row']
type TaskInsert = Database['public']['Tables']['tasks']['Insert']
type TaskUpdate = Database['public']['Tables']['tasks']['Update']

export class TaskService {
  // Fetch all tasks for the current user
  static async fetchTasks(userId: string, client: SupabaseClient<Database> = supabase): Promise<Task[]> {
    const { data, error } = await client
      .from('tasks')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching tasks:', error)
      throw error
    }

    const tasks = data?.map(this.transformRowToTask) || []
    
    // Fetch file attachments for each task from file_attachments table only
    const tasksWithAttachments = await Promise.all(
      tasks.map(async (task) => {
        try {
          const fileAttachments = await FileAttachmentService.getTaskAttachments(task.id, userId, client)
          const signedUrls = await Promise.all(fileAttachments.map(async att => {
            // Generate signed URL for each attachment
            const { data: signedUrlData, error: signedUrlError } = await client.storage
              .from('task-attachments')
              .createSignedUrl(att.storage_path, 31536000) // 1 year in seconds
            
            if (signedUrlError) {
              console.error('Error creating signed URL for attachment:', signedUrlError)
              return att.filename // fallback to filename
            }
            
            return signedUrlData.signedUrl
          }))
          
          // Only use file attachments from the file_attachments table
          // Ignore attachments stored in the tasks table to avoid duplication
          return {
            ...task,
            attachments: signedUrls.length > 0 ? signedUrls : undefined
          }
        } catch (err) {
          console.error(`Error fetching attachments for task ${task.id}:`, err)
          return {
            ...task,
            attachments: undefined // Clear attachments on error to avoid showing stale data
          }
        }
      })
    )

    return tasksWithAttachments
  }

  // Create a new task
  static async createTask(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>, userId: string, client: SupabaseClient<Database> = supabase): Promise<Task> {
    console.log('TaskService.createTask called with:', { task, userId })
    
    const taskInsert: TaskInsert = {
      user_id: userId,
      date: task.date,
      title: task.title,
      description: task.description,
      priority: task.priority,
      status: task.status || 'pending',
      completed_at: task.completedAt || null,
      due_date: task.dueDate || null,
      category: task.category || null,
      tags: task.tags || null,
      comments: task.comments || null,
      links: task.links || null,
      attachments: task.attachments?.map(att => typeof att === 'string' ? att : att.key) || null,
      voice_notes: task.voiceNotes || null,
      recurring_pattern: task.recurringPattern ? { pattern: task.recurringPattern } : null,
    }

    console.log('Inserting task with data:', taskInsert)

    const { data, error } = await client
      .from('tasks')
      .insert(taskInsert)
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating task:', error)
      throw error
    }

    console.log('Task created in Supabase:', data)
    const transformedTask = this.transformRowToTask(data)
    console.log('Transformed task:', transformedTask)
    return transformedTask
  }

  // Update an existing task
  static async updateTask(taskId: string, updates: Partial<Task>, userId: string, client: SupabaseClient<Database> = supabase): Promise<Task> {
    const taskUpdate: TaskUpdate = {}
    
    // Only include fields that are actually being updated
    if (updates.date !== undefined) taskUpdate.date = updates.date
    if (updates.title !== undefined) taskUpdate.title = updates.title
    if (updates.description !== undefined) taskUpdate.description = updates.description
    if (updates.priority !== undefined) taskUpdate.priority = updates.priority
    if (updates.status !== undefined) taskUpdate.status = updates.status
    if (updates.completedAt !== undefined) taskUpdate.completed_at = updates.completedAt
    if (updates.completedAt === undefined && 'completedAt' in updates) taskUpdate.completed_at = null
    
    if (updates.dueDate !== undefined) taskUpdate.due_date = updates.dueDate || null
    if (updates.category !== undefined) taskUpdate.category = updates.category || null
    if (updates.tags !== undefined) taskUpdate.tags = updates.tags || null
    if (updates.comments !== undefined) taskUpdate.comments = updates.comments || null
    if (updates.links !== undefined) taskUpdate.links = updates.links || null
    if (updates.attachments !== undefined) taskUpdate.attachments = updates.attachments?.map(att => typeof att === 'string' ? att : att.key) || null
    if (updates.voiceNotes !== undefined) taskUpdate.voice_notes = updates.voiceNotes || null
    if (updates.recurringPattern !== undefined) taskUpdate.recurring_pattern = updates.recurringPattern ? { pattern: updates.recurringPattern } : null

    const { data, error } = await client
      .from('tasks')
      .update(taskUpdate)
      .eq('id', taskId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating task:', error)
      throw error
    }

    return this.transformRowToTask(data)
  }

  // Delete a task and all associated attachments
  static async deleteTask(taskId: string, userId: string, client: SupabaseClient<Database> = supabase): Promise<void> {
    try {
      // First, get all attachments for this task
      const attachments = await FileAttachmentService.getTaskAttachments(taskId, userId, client)
      
      // Delete all attachments from storage and database
      for (const attachment of attachments) {
        try {
          await FileAttachmentService.deleteFile(attachment.storage_path, userId, client)
        } catch (error) {
          console.error(`Error deleting attachment ${attachment.filename}:`, error)
          // Continue with other deletions even if one fails
        }
      }
      
      // Delete the task from database
      const { error } = await client
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', userId)

      if (error) {
        console.error('Error deleting task:', error)
        throw error
      }
    } catch (error) {
      console.error('Error in deleteTask:', error)
      throw error
    }
  }

  // Migrate tasks from localStorage to Supabase
  static async migrateFromLocalStorage(userId: string): Promise<void> {
    try {
      // Get tasks from localStorage
      const localTasks = localStorage.getItem('scheduled-tasks')
      if (!localTasks) return

      const tasks: Task[] = JSON.parse(localTasks)
      if (!Array.isArray(tasks) || tasks.length === 0) return

      // Check if migration already happened
      const { data: existingTasks } = await supabase
        .from('tasks')
        .select('id')
        .eq('user_id', userId)
        .limit(1)

      if (existingTasks && existingTasks.length > 0) {
        console.log('Migration already completed')
        return
      }

      // Migrate each task
      const migrationPromises = tasks.map(task => {
        const taskInsert: TaskInsert = {
          user_id: userId,
          date: task.date,
          title: task.title,
          description: task.description,
          priority: task.priority,
          status: task.status || 'pending',
          completed_at: task.completedAt || null,
          due_date: task.dueDate || null,
          category: task.category || null,
          tags: task.tags || null,
          comments: task.comments || null,
          links: task.links || null,
          attachments: task.attachments?.map(att => typeof att === 'string' ? att : att.key) || null,
          voice_notes: task.voiceNotes || null,
          created_at: task.createdAt,
          updated_at: task.updatedAt,
        }
        
        return supabase.from('tasks').insert(taskInsert)
      })

      await Promise.all(migrationPromises)

      // Clear localStorage after successful migration
      localStorage.removeItem('scheduled-tasks')
      
      console.log(`Successfully migrated ${tasks.length} tasks from localStorage to Supabase`)
    } catch (error) {
      console.error('Error migrating tasks:', error)
      throw error
    }
  }

  // Public method to transform database row to Task interface (for real-time updates)
  static transformTask(row: TaskRow): Task {
    return this.transformRowToTask(row);
  }

  // Transform database row to Task interface
  private static transformRowToTask(row: TaskRow): Task {
    const completedAt = row.completed_at === null ? undefined : row.completed_at
    
    // Handle recurring_pattern from JSONB to string
    let recurringPattern: 'daily' | 'weekly' | 'monthly' | 'yearly' | undefined;
    if (row.recurring_pattern) {
      if (typeof row.recurring_pattern === 'string') {
        recurringPattern = row.recurring_pattern as 'daily' | 'weekly' | 'monthly' | 'yearly';
      } else if (typeof row.recurring_pattern === 'object' && row.recurring_pattern !== null) {
        // If it's stored as an object, try to extract the pattern
        const patternObj = row.recurring_pattern as any;
        if (patternObj.pattern) {
          recurringPattern = patternObj.pattern;
        }
      }
    }
    
    return {
      id: row.id,
      date: row.date,
      title: row.title,
      description: row.description,
      priority: row.priority,
      status: normalizeTaskStatus(row.status),
      completedAt: completedAt,
      dueDate: row.due_date || undefined,
      category: row.category || undefined,
      tags: row.tags || undefined,
      comments: row.comments as Task['comments'] || undefined,
      links: row.links || undefined,
      attachments: row.attachments?.map(att => att) || undefined,
      voiceNotes: row.voice_notes || undefined,
      recurringPattern,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }
  }

  // Real-time subscription for tasks with debouncing
  static subscribeToTasks(userId: string, callback: (tasks: Task[]) => void) {
    let debounceTimeout: NodeJS.Timeout | null = null;
    
    return supabase
      .channel('tasks')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tasks',
          filter: `user_id=eq.${userId}`,
        },
        () => {
          // Debounce refetches to prevent rapid updates
          if (debounceTimeout) clearTimeout(debounceTimeout);
          debounceTimeout = setTimeout(async () => {
            try {
              // Fetch tasks directly without triggering loading states
              const tasks = await this.fetchTasks(userId, supabase);
              callback(tasks);
            } catch (error) {
              console.error('Error in real-time task subscription:', error);
            }
          }, 1000); // 1 second debounce for responsive updates without excessive refreshes
        }
      )
      .subscribe()
  }

  // Group tasks by date
  static groupTasksByDate(tasks: Task[]): TasksByDate {
    return tasks.reduce((acc, task) => {
      if (!acc[task.date]) {
        acc[task.date] = []
      }
      acc[task.date].push(task)
      return acc
    }, {} as TasksByDate)
  }
}

// File attachment service
export class FileAttachmentService {
  // Upload file to Supabase storage
  static async uploadFile(file: File, userId: string, taskId: string, client: SupabaseClient<Database> = supabase): Promise<string> {
    console.log('FileAttachmentService.uploadFile called with:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId,
      taskId,
      client: client === supabase ? 'default supabase client' : 'custom client'
    })

    // Check if file already exists to prevent duplicates
    console.log('Checking for existing file...')
    const { data: existingFiles, error: checkError } = await client
      .from('file_attachments')
      .select('storage_path')
      .eq('task_id', taskId)
      .eq('filename', file.name)
      .eq('user_id', userId)

    if (checkError) {
      console.error('Error checking for existing file:', checkError)
      throw checkError
    }

    if (existingFiles && existingFiles.length > 0) {
      console.log(`File ${file.name} already exists for this task. Returning existing signed URL.`)
      // Return existing signed URL instead of uploading again
      const existingStoragePath = existingFiles[0].storage_path
      const { data: existingSignedUrl, error: existingUrlError } = await client.storage
        .from('task-attachments')
        .createSignedUrl(existingStoragePath, 31536000)
      
      if (existingUrlError) {
        console.error('Error creating signed URL for existing file:', existingUrlError)
        throw existingUrlError
      }
      
      return existingSignedUrl.signedUrl
    }

    const fileExt = file.name.split('.').pop()
    const fileName = `${userId}/${taskId}/${Date.now()}.${fileExt}`
    const filePath = `attachments/${fileName}`
    
    console.log('Generated file path:', filePath)

    // Upload file to storage
    console.log('Uploading file to storage...')
    const { data: uploadData, error: uploadError } = await client.storage
      .from('task-attachments')
      .upload(filePath, file)

    if (uploadError) {
      console.error('Error uploading file to storage:', uploadError)
      throw uploadError
    }
    
    console.log('File uploaded to storage successfully:', uploadData)

    // Get signed URL (valid for 1 year)
    console.log('Getting signed URL...')
    const { data: signedUrlData, error: signedUrlError } = await client.storage
      .from('task-attachments')
      .createSignedUrl(filePath, 31536000) // 1 year in seconds
      
    if (signedUrlError) {
      console.error('Error creating signed URL:', signedUrlError)
      throw signedUrlError
    }
    
    const signedUrl = signedUrlData.signedUrl
    console.log('Signed URL generated:', signedUrl)

    // Save file metadata to database
    console.log('Saving file metadata to database...')
    const metadataToSave = {
      user_id: userId,
      task_id: taskId,
      filename: file.name,
      file_type: file.type,
      file_size: file.size,
      storage_path: filePath,
    }
    
    console.log('Metadata to save:', metadataToSave)
    
    const { data: insertData, error: dbError } = await client
      .from('file_attachments')
      .insert(metadataToSave)
      .select()

    if (dbError) {
      console.error('Error saving file metadata to database:', dbError)
      throw dbError
    }
    
    console.log('File metadata saved successfully:', insertData)

    return signedUrl
  }

  // Delete file from storage and database
  static async deleteFile(filePath: string, userId: string, client: SupabaseClient<Database> = supabase): Promise<void> {
    // Delete from storage
    const { error: storageError } = await client.storage
      .from('task-attachments')
      .remove([filePath])

    if (storageError) {
      console.error('Error deleting file from storage:', storageError)
      throw storageError
    }

    // Delete from database
    const { error: dbError } = await client
      .from('file_attachments')
      .delete()
      .eq('storage_path', filePath)
      .eq('user_id', userId)

    if (dbError) {
      console.error('Error deleting file metadata:', dbError)
      throw dbError
    }
  }

  // Get file attachments for a task
  static async getTaskAttachments(taskId: string, userId: string, client: SupabaseClient<Database> = supabase) {
    const { data, error } = await client
      .from('file_attachments')
      .select('*')
      .eq('task_id', taskId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching task attachments:', error)
      throw error
    }

    return data || []
  }

  // Delete a specific attachment by filename
  static async deleteTaskAttachment(taskId: string, filename: string, userId: string, client: SupabaseClient<Database> = supabase): Promise<void> {
    console.log('Deleting attachment:', { taskId, filename, userId })
    
    try {
      // First, get ALL records for this file (in case of duplicates)
      const { data: fileRecords, error: fetchError } = await client
        .from('file_attachments')
        .select('id, storage_path')
        .eq('task_id', taskId)
        .eq('filename', filename)
        .eq('user_id', userId)

      if (fetchError) {
        console.error('Error fetching file records:', fetchError)
        throw fetchError
      }

      if (!fileRecords || fileRecords.length === 0) {
        console.log('No file records found to delete')
        return
      }

      console.log(`Found ${fileRecords.length} file record(s) to delete`)

      // Delete from storage (use the first record's storage path, they should all be the same)
      const storagePath = fileRecords[0].storage_path
      if (storagePath) {
        console.log('Deleting from storage:', storagePath)
        const { error: storageError } = await client.storage
          .from('task-attachments')
          .remove([storagePath])

        if (storageError) {
          console.error('Error deleting from storage:', storageError)
          // Continue with database deletion even if storage fails
        } else {
          console.log('Successfully deleted from storage')
        }
      }

      // Delete ALL database records for this file (to handle duplicates)
      console.log('Deleting all duplicate records from database')
      const { error: dbError } = await client
        .from('file_attachments')
        .delete()
        .eq('task_id', taskId)
        .eq('filename', filename)
        .eq('user_id', userId)

      if (dbError) {
        console.error('Error deleting from database:', dbError)
        throw dbError
      }

      console.log(`Successfully deleted ${fileRecords.length} attachment record(s) from database`)
    } catch (error) {
      console.error('Error in deleteTaskAttachment:', error)
      throw error
    }
  }

  // Clean up duplicate attachments for a specific task
  static async cleanupDuplicateAttachments(taskId: string, userId: string, client: SupabaseClient<Database> = supabase): Promise<void> {
    console.log('Cleaning up duplicate attachments for task:', taskId)
    
    try {
      // Get all attachments for this task
      const { data: attachments, error: fetchError } = await client
        .from('file_attachments')
        .select('id, filename, uploaded_at')
        .eq('task_id', taskId)
        .eq('user_id', userId)
        .order('filename', { ascending: true })
        .order('uploaded_at', { ascending: true })

      if (fetchError) {
        console.error('Error fetching attachments:', fetchError)
        throw fetchError
      }

      if (!attachments || attachments.length === 0) {
        console.log('No attachments found for this task')
        return
      }

      // Group by filename and identify duplicates
      const fileGroups: Record<string, typeof attachments> = {}
      attachments.forEach(att => {
        if (!fileGroups[att.filename]) {
          fileGroups[att.filename] = []
        }
        fileGroups[att.filename].push(att)
      })

      // Find and delete duplicates (keep the oldest one)
      const duplicateIds: string[] = []
      Object.entries(fileGroups).forEach(([filename, files]) => {
        if (files.length > 1) {
          console.log(`Found ${files.length} duplicates for file: ${filename}`)
          // Keep the first (oldest) file, mark others for deletion
          const toDelete = files.slice(1)
          duplicateIds.push(...toDelete.map(f => f.id))
        }
      })

      if (duplicateIds.length > 0) {
        console.log(`Deleting ${duplicateIds.length} duplicate records`)
        const { error: deleteError } = await client
          .from('file_attachments')
          .delete()
          .in('id', duplicateIds)

        if (deleteError) {
          console.error('Error deleting duplicates:', deleteError)
          throw deleteError
        }

        console.log(`Successfully cleaned up ${duplicateIds.length} duplicate attachments`)
      } else {
        console.log('No duplicates found for this task')
      }
    } catch (error) {
      console.error('Error in cleanupDuplicateAttachments:', error)
      throw error
    }
  }

  // Clean up all duplicate attachments for a user
  static async cleanupAllDuplicateAttachments(userId: string, client: SupabaseClient<Database> = supabase): Promise<void> {
    console.log('Cleaning up all duplicate attachments for user:', userId)
    
    try {
      // Get all unique task IDs for this user
      const { data: tasks, error: tasksError } = await client
        .from('file_attachments')
        .select('task_id')
        .eq('user_id', userId)

      if (tasksError) {
        console.error('Error fetching user tasks:', tasksError)
        throw tasksError
      }

      if (!tasks || tasks.length === 0) {
        console.log('No attachments found for this user')
        return
      }

      const uniqueTaskIds = [...new Set(tasks.map(t => t.task_id))]
      console.log(`Found ${uniqueTaskIds.length} tasks with attachments`)

      // Clean up duplicates for each task
      for (const taskId of uniqueTaskIds) {
        await this.cleanupDuplicateAttachments(taskId, userId, client)
      }

      console.log('Finished cleaning up all duplicate attachments')
    } catch (error) {
      console.error('Error in cleanupAllDuplicateAttachments:', error)
      throw error
    }
  }
}

// Voice Note service
export class VoiceNoteService {
  // Upload voice note to Supabase storage
  static async uploadVoiceNote(audioBlob: Blob, userId: string, taskId: string, client: SupabaseClient<Database> = supabase): Promise<string> {
    console.log('VoiceNoteService.uploadVoiceNote called with:', {
      audioSize: audioBlob.size,
      audioType: audioBlob.type,
      userId,
      taskId,
    })

    const timestamp = Date.now()
    const fileName = `${userId}/${taskId}/${timestamp}.wav`
    const filePath = `voice-notes/${fileName}`
    
    console.log('Generated voice note path:', filePath)

    // Upload voice note to storage
    console.log('Uploading voice note to storage...')
    const { data: uploadData, error: uploadError } = await client.storage
      .from('task-attachments')
      .upload(filePath, audioBlob, {
        contentType: 'audio/wav',
        cacheControl: '3600',
        upsert: false
      })

    if (uploadError) {
      console.error('Error uploading voice note to storage:', uploadError)
      throw uploadError
    }
    
    console.log('Voice note uploaded to storage successfully:', uploadData)

    // Get signed URL (valid for 1 year)
    console.log('Getting signed URL...')
    const { data: signedUrlData, error: signedUrlError } = await client.storage
      .from('task-attachments')
      .createSignedUrl(filePath, 31536000) // 1 year in seconds
      
    if (signedUrlError) {
      console.error('Error creating signed URL:', signedUrlError)
      throw signedUrlError
    }
    
    const signedUrl = signedUrlData.signedUrl
    console.log('Signed URL generated:', signedUrl)

    // Save voice note metadata to database (reuse file_attachments table)
    console.log('Saving voice note metadata to database...')
    const metadataToSave = {
      user_id: userId,
      task_id: taskId,
      filename: `voice-note-${timestamp}.wav`,
      file_type: 'audio/wav',
      file_size: audioBlob.size,
      storage_path: filePath,
    }
    
    console.log('Voice note metadata to save:', metadataToSave)
    
    const { data: insertData, error: dbError } = await client
      .from('file_attachments')
      .insert(metadataToSave)
      .select()

    if (dbError) {
      console.error('Error saving voice note metadata to database:', dbError)
      throw dbError
    }
    
    console.log('Voice note metadata saved successfully:', insertData)

    return signedUrl
  }

  // Delete voice note by URL
  static async deleteVoiceNote(voiceNoteUrl: string, taskId: string, userId: string, client: SupabaseClient<Database> = supabase): Promise<void> {
    console.log('Deleting voice note:', { voiceNoteUrl, taskId, userId })
    
    try {
      // Extract filename from URL to find the record
      const urlParts = voiceNoteUrl.split('/')
      const filename = urlParts[urlParts.length - 1]
      
      // Find the voice note record in database
      const { data: voiceNoteRecord, error: fetchError } = await client
        .from('file_attachments')
        .select('storage_path')
        .eq('task_id', taskId)
        .eq('user_id', userId)
        .eq('file_type', 'audio/wav')
        .like('filename', '%voice-note%')
        .single()

      if (fetchError) {
        console.error('Error fetching voice note record:', fetchError)
        throw fetchError
      }

      if (voiceNoteRecord && voiceNoteRecord.storage_path) {
        // Delete from storage
        console.log('Deleting voice note from storage:', voiceNoteRecord.storage_path)
        const { error: storageError } = await client.storage
          .from('task-attachments')
          .remove([voiceNoteRecord.storage_path])

        if (storageError) {
          console.error('Error deleting voice note from storage:', storageError)
          // Continue with database deletion even if storage fails
        } else {
          console.log('Successfully deleted voice note from storage')
        }
      }

      // Delete from database
      console.log('Deleting voice note from database')
      const { error: dbError } = await client
        .from('file_attachments')
        .delete()
        .eq('task_id', taskId)
        .eq('user_id', userId)
        .eq('file_type', 'audio/wav')
        .like('filename', '%voice-note%')

      if (dbError) {
        console.error('Error deleting voice note from database:', dbError)
        throw dbError
      }

      console.log('Successfully deleted voice note from database')
    } catch (error) {
      console.error('Error in deleteVoiceNote:', error)
      throw error
    }
  }

  // Get voice notes for a task
  static async getTaskVoiceNotes(taskId: string, userId: string, client: SupabaseClient<Database> = supabase) {
    const { data, error } = await client
      .from('file_attachments')
      .select('*')
      .eq('task_id', taskId)
      .eq('user_id', userId)
      .eq('file_type', 'audio/wav')
      .like('filename', '%voice-note%')

    if (error) {
      console.error('Error fetching voice notes:', error)
      throw error
    }

    return data || []
  }
}
