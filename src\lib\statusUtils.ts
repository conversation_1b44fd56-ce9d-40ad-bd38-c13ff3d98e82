/**
 * Utility functions for handling task status normalization
 * Ensures backward compatibility by converting legacy 'in-progress' status to 'pending'
 */

import { TaskStatus } from '@/shared/@app/types/task.types';

/**
 * Normalizes task status to ensure compatibility with the current 4-status system
 * Converts legacy 'in-progress' status to 'pending'
 * 
 * @param status - The status to normalize
 * @returns Normalized status that matches current system
 */
export function normalizeTaskStatus(status: string | TaskStatus): TaskStatus {
  // Convert legacy 'in-progress' status to 'pending'
  if (status === 'in-progress') {
    return 'pending';
  }
  
  // Validate that the status is one of the current valid statuses
  const validStatuses: TaskStatus[] = ['pending', 'completed', 'cancelled', 'on-hold'];
  
  if (validStatuses.includes(status as TaskStatus)) {
    return status as TaskStatus;
  }
  
  // Default to 'pending' for any invalid status
  console.warn(`Invalid task status '${status}' encountered, defaulting to 'pending'`);
  return 'pending';
}

/**
 * Normalizes an array of tasks by ensuring all statuses are valid
 * 
 * @param tasks - Array of tasks to normalize
 * @returns Array of tasks with normalized statuses
 */
export function normalizeTaskStatuses<T extends { status: string | TaskStatus }>(tasks: T[]): T[] {
  return tasks.map(task => ({
    ...task,
    status: normalizeTaskStatus(task.status)
  }));
}

/**
 * Checks if a status is a legacy status that needs conversion
 * 
 * @param status - The status to check
 * @returns True if the status is legacy and needs conversion
 */
export function isLegacyStatus(status: string): boolean {
  return status === 'in-progress';
}