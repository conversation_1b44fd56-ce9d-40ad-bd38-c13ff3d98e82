import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface UnifiedUser {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  createdAt: string;
  emailVerified: boolean;
}

interface UnifiedProfile {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  timezone?: string;
  notificationPreferences?: {
    push: boolean;
    email: boolean;
    reminder: boolean;

  };
  workHours?: {
    start: string;
    end: string;
    days: number[];
  };

  widgetEnabled?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface UnifiedAuthContextType {
  user: UnifiedUser | null;
  profile: UnifiedProfile | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, userData?: { fullName?: string }) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UnifiedProfile>) => Promise<{ error: any }>;
  refreshProfile: () => Promise<void>;
}

const UnifiedAuthContext = createContext<UnifiedAuthContextType | undefined>(undefined);

export const useUnifiedAuth = () => {
  const context = useContext(UnifiedAuthContext);
  if (context === undefined) {
    throw new Error('useUnifiedAuth must be used within a UnifiedAuthProvider');
  }
  return context;
};

export const UnifiedAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UnifiedUser | null>(null);
  const [profile, setProfile] = useState<UnifiedProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  const mapSupabaseUserToUnified = (supabaseUser: User): UnifiedUser => ({
    id: supabaseUser.id,
    email: supabaseUser.email || '',
    fullName: supabaseUser.user_metadata?.full_name,
    avatarUrl: supabaseUser.user_metadata?.avatar_url,
    createdAt: supabaseUser.created_at,
    emailVerified: supabaseUser.email_confirmed_at !== null,
  });

  const fetchUserProfile = async (userId: string): Promise<UnifiedProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }

      return {
        id: data.id,
        email: data.email,
        fullName: data.full_name,
        avatarUrl: data.avatar_url,
        timezone: data.timezone,
        notificationPreferences: data.notification_preferences as {
          push: boolean;
          email: boolean;
          reminder: boolean;

        },
        workHours: data.work_hours as {
          start: string;
          end: string;
          days: number[];
        },

        widgetEnabled: data.widget_enabled,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      };
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      return null;
    }
  };

  const createUserProfile = async (user: UnifiedUser): Promise<void> => {
    try {
      const { error } = await supabase.from('profiles').upsert({
        id: user.id,
        email: user.email,
        full_name: user.fullName,
        avatar_url: user.avatarUrl,
        timezone: 'UTC',
        notification_preferences: {
          push: true,
          email: true,
          reminder: true,

        },
        work_hours: {
          start: '09:00',
          end: '17:00',
          days: [1, 2, 3, 4, 5],
        },

        widget_enabled: true,
      });

      if (error) {
        console.error('Error creating profile:', error);
      }
    } catch (error) {
      console.error('Error in createUserProfile:', error);
    }
  };

  const signUp = async (email: string, password: string, userData?: { fullName?: string }) => {
    try {
      const redirectUrl = `${window.location.origin}/`;
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            full_name: userData?.fullName || '',
          },
        },
      });

      if (error) return { error };

      // Create profile for new user
      if (data.user && !error) {
        const unifiedUser = mapSupabaseUserToUnified(data.user);
        await createUserProfile(unifiedUser);
      }

      return { error: null };
    } catch (error) {
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  const signOut = async () => {
    try {
      // Clear local state first
      setUser(null);
      setProfile(null);
      setSession(null);

      // Clear auth storage
      Object.keys(localStorage).forEach((key) => {
        if (key.startsWith('supabase.auth.') || key.includes('sb-')) {
          localStorage.removeItem(key);
        }
      });

      // Sign out from Supabase
      await supabase.auth.signOut({ scope: 'global' });
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const updateProfile = async (updates: Partial<UnifiedProfile>) => {
    if (!user) return { error: new Error('No user logged in') };

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: updates.fullName,
          avatar_url: updates.avatarUrl,
          timezone: updates.timezone,
          notification_preferences: updates.notificationPreferences,
          work_hours: updates.workHours,

          widget_enabled: updates.widgetEnabled,
        })
        .eq('id', user.id);

      if (!error) {
        // Refresh profile data
        await refreshProfile();
      }

      return { error };
    } catch (error) {
      return { error };
    }
  };

  const refreshProfile = async () => {
    if (!user) return;

    const updatedProfile = await fetchUserProfile(user.id);
    if (updatedProfile) {
      setProfile(updatedProfile);
    }
  };

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        
        if (session?.user) {
          const unifiedUser = mapSupabaseUserToUnified(session.user);
          setUser(unifiedUser);
          
          // Defer profile fetching to prevent deadlocks
          setTimeout(async () => {
            const userProfile = await fetchUserProfile(session.user.id);
            if (userProfile) {
              setProfile(userProfile);
            } else {
              // Create profile if it doesn't exist
              await createUserProfile(unifiedUser);
              const newProfile = await fetchUserProfile(session.user.id);
              if (newProfile) {
                setProfile(newProfile);
              }
            }
          }, 0);
        } else {
          setUser(null);
          setProfile(null);
        }
        
        setLoading(false);
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      
      if (session?.user) {
        const unifiedUser = mapSupabaseUserToUnified(session.user);
        setUser(unifiedUser);
        
        setTimeout(async () => {
          const userProfile = await fetchUserProfile(session.user.id);
          if (userProfile) {
            setProfile(userProfile);
          }
        }, 0);
      }
      
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    refreshProfile,
  };

  return (
    <UnifiedAuthContext.Provider value={value}>
      {children}
    </UnifiedAuthContext.Provider>
  );
};