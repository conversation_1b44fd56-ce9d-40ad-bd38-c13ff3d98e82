import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../lib/supabase';
import { Task } from '../types/task';

interface OfflineQueueItem {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
}

interface OfflineState {
  isOnline: boolean;
  lastSync: number;
  pendingSync: OfflineQueueItem[];
}

export class OfflineService {
  private static instance: OfflineService;
  private isOnline = true;
  private syncInterval: NodeJS.Timeout | null = null;

  static getInstance(): OfflineService {
    if (!OfflineService.instance) {
      OfflineService.instance = new OfflineService();
    }
    return OfflineService.instance;
  }

  async initialize() {
    // Simulate network status check
    this.isOnline = true; // Default to online
    
    // Start periodic sync
    this.startPeriodicSync();
  }

  private startPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.syncPendingChanges();
    }, 30000); // Sync every 30 seconds
  }

  async saveTaskOffline(task: Task) {
    try {
      const offlineTasks = await this.getOfflineTasks();
      offlineTasks[task.id] = task;
      await AsyncStorage.setItem('offline_tasks', JSON.stringify(offlineTasks));
    } catch (error) {
      console.error('Error saving task offline:', error);
    }
  }

  async getOfflineTasks(): Promise<Record<string, Task>> {
    try {
      const data = await AsyncStorage.getItem('offline_tasks');
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Error getting offline tasks:', error);
      return {};
    }
  }

  async addToQueue(type: 'create' | 'update' | 'delete', data: any) {
    try {
      const queue = await this.getQueue();
      const item: OfflineQueueItem = {
        id: Date.now().toString(),
        type,
        data,
        timestamp: Date.now(),
      };
      queue.push(item);
      await AsyncStorage.setItem('offline_queue', JSON.stringify(queue));
    } catch (error) {
      console.error('Error adding to queue:', error);
    }
  }

  async getQueue(): Promise<OfflineQueueItem[]> {
    try {
      const data = await AsyncStorage.getItem('offline_queue');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error getting queue:', error);
      return [];
    }
  }

  async clearQueue() {
    try {
      await AsyncStorage.removeItem('offline_queue');
    } catch (error) {
      console.error('Error clearing queue:', error);
    }
  }

  async syncPendingChanges() {
    try {
      const queue = await this.getQueue();
      if (queue.length === 0) return;

      console.log(`Syncing ${queue.length} pending changes...`);

      for (const item of queue) {
        try {
          await this.processQueueItem(item);
        } catch (error) {
          console.error('Error processing queue item:', error);
          continue;
        }
      }

      // Clear processed items
      await this.clearQueue();
      
      // Update last sync time
      await AsyncStorage.setItem('last_sync', Date.now().toString());

      console.log('Sync completed');
    } catch (error) {
      console.error('Error syncing pending changes:', error);
    }
  }

  private async processQueueItem(item: OfflineQueueItem) {
    switch (item.type) {
      case 'create':
        await this.createTaskOnline(item.data);
        break;
      case 'update':
        await this.updateTaskOnline(item.data);
        break;
      case 'delete':
        await this.deleteTaskOnline(item.data);
        break;
    }
  }

  private async createTaskOnline(taskData: any) {
    const { data, error } = await supabase
      .from('tasks')
      .insert(taskData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  private async updateTaskOnline(taskData: any) {
    const { error } = await supabase
      .from('tasks')
      .update(taskData)
      .eq('id', taskData.id);

    if (error) throw error;
  }

  private async deleteTaskOnline(taskData: any) {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskData.id);

    if (error) throw error;
  }

  async getOfflineState(): Promise<OfflineState> {
    try {
      const lastSync = await AsyncStorage.getItem('last_sync');
      const pendingSync = await this.getQueue();

      return {
        isOnline: this.isOnline,
        lastSync: lastSync ? parseInt(lastSync) : 0,
        pendingSync,
      };
    } catch (error) {
      console.error('Error getting offline state:', error);
      return {
        isOnline: this.isOnline,
        lastSync: 0,
        pendingSync: [],
      };
    }
  }

  async cacheTasks(tasks: Task[]) {
    try {
      await AsyncStorage.setItem('cached_tasks', JSON.stringify(tasks));
      await AsyncStorage.setItem('cache_timestamp', Date.now().toString());
    } catch (error) {
      console.error('Error caching tasks:', error);
    }
  }

  async getCachedTasks(): Promise<Task[]> {
    try {
      const data = await AsyncStorage.getItem('cached_tasks');
      const timestamp = await AsyncStorage.getItem('cache_timestamp');
      
      if (!data || !timestamp) return [];
      
      const cacheAge = Date.now() - parseInt(timestamp);
      const maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (cacheAge > maxCacheAge) {
        await AsyncStorage.removeItem('cached_tasks');
        await AsyncStorage.removeItem('cache_timestamp');
        return [];
      }
      
      return JSON.parse(data);
    } catch (error) {
      console.error('Error getting cached tasks:', error);
      return [];
    }
  }

  async clearCache() {
    try {
      await AsyncStorage.multiRemove([
        'cached_tasks',
        'cache_timestamp',
        'offline_tasks',
        'offline_queue',
      ]);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  isNetworkAvailable(): boolean {
    return this.isOnline;
  }

  async forceSync() {
    await this.syncPendingChanges();
  }

  destroy() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Simulate network status changes for testing
  setNetworkStatus(isOnline: boolean) {
    this.isOnline = isOnline;
    if (isOnline) {
      this.syncPendingChanges();
    }
  }
}

// Export singleton instance
export const offlineService = OfflineService.getInstance();