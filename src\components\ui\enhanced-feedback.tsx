import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

// Success animation component
interface SuccessAnimationProps {
  size?: number;
  className?: string;
}

export const SuccessAnimation: React.FC<SuccessAnimationProps> = ({ 
  size = 64, 
  className 
}) => {
  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{
        type: "spring",
        stiffness: 260,
        damping: 20,
        delay: 0.1
      }}
      className={cn("relative", className)}
      style={{ width: size, height: size }}
    >
      <motion.div
        className="absolute inset-0 bg-green-500 rounded-full"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20
        }}
      />
      <motion.svg
        className="absolute inset-0 w-full h-full text-white"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{
          duration: 0.6,
          delay: 0.2,
          ease: "easeInOut"
        }}
      >
        <motion.path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M5 13l4 4L19 7"
        />
      </motion.svg>
    </motion.div>
  );
};

// Pulse loader for buttons
interface PulseLoaderProps {
  className?: string;
}

export const PulseLoader: React.FC<PulseLoaderProps> = ({ className }) => {
  return (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-2 bg-current rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2
          }}
        />
      ))}
    </div>
  );
};

// Progress bar with animation
interface AnimatedProgressProps {
  value: number;
  max: number;
  label?: string;
  className?: string;
  showPercentage?: boolean;
}

export const AnimatedProgress: React.FC<AnimatedProgressProps> = ({
  value,
  max,
  label,
  className,
  showPercentage = true
}) => {
  const percentage = Math.round((value / max) * 100);
  
  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className="flex justify-between text-sm font-medium">
          <span>{label}</span>
          {showPercentage && <span>{percentage}%</span>}
        </div>
      )}
      <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
        <motion.div
          className="bg-gradient-primary h-full rounded-full shadow-sm"
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ 
            duration: 1,
            ease: "easeOut",
            type: "spring",
            stiffness: 100
          }}
        />
      </div>
    </div>
  );
};

// Floating action feedback
interface ActionFeedbackProps {
  action: string;
  isVisible: boolean;
  position: { x: number; y: number };
}

export const ActionFeedback: React.FC<ActionFeedbackProps> = ({
  action,
  isVisible,
  position
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: -10 }}
          transition={{ duration: 0.2 }}
          className="fixed z-50 pointer-events-none"
          style={{ left: position.x, top: position.y }}
        >
          <div className="glass-card px-3 py-1 rounded-full text-sm font-medium text-foreground shadow-lg border">
            {action}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Smart loading button
interface SmartLoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  success?: boolean;
  error?: boolean;
  loadingText?: string;
  successText?: string;
  errorText?: string;
  children: React.ReactNode;
}

export const SmartLoadingButton: React.FC<SmartLoadingButtonProps> = ({
  loading = false,
  success = false,
  error = false,
  loadingText = "Loading...",
  successText = "Success!",
  errorText = "Error",
  children,
  className,
  ...props
}) => {
  const getContent = () => {
    if (loading) return loadingText;
    if (success) return successText;
    if (error) return errorText;
    return children;
  };

  const getVariant = () => {
    if (success) return "bg-green-500 text-white";
    if (error) return "bg-red-500 text-white";
    return "btn-premium";
  };

  return (
    <button
      className={cn(
        "relative inline-flex items-center justify-center gap-2 px-4 py-2 rounded-md font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
        getVariant(),
        className
      )}
      disabled={loading}
      {...props}
    >
      <AnimatePresence mode="wait">
        {loading && (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center gap-2"
          >
            <Loader2 className="w-4 h-4 animate-spin" />
            {loadingText}
          </motion.div>
        )}
        {success && (
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="flex items-center gap-2"
          >
            <CheckCircle className="w-4 h-4" />
            {successText}
          </motion.div>
        )}
        {error && (
          <motion.div
            key="error"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="flex items-center gap-2"
          >
            <AlertCircle className="w-4 h-4" />
            {errorText}
          </motion.div>
        )}
        {!loading && !success && !error && (
          <motion.div
            key="default"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </button>
  );
};