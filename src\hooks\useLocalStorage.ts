import { useState, useEffect } from 'react';

/**
 * Custom hook for managing localStorage with automatic JSON serialization
 * @param key - The localStorage key
 * @param initialValue - The initial value if nothing is stored
 * @returns [value, setValue] tuple similar to useState
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
  // Get value from localStorage on initial load
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window === 'undefined') {
        return initialValue;
      }
      
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to localStorage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

/**
 * Utility functions for task storage management
 */
export const taskStorageUtils = {
  // Clear all task data (useful for reset/debugging)
  clearAllTasks: () => {
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem('scheduled-tasks');
    }
  },

  // Export tasks as JSON (for backup)
  exportTasks: () => {
    if (typeof window !== 'undefined') {
      const tasks = window.localStorage.getItem('scheduled-tasks');
      return tasks ? JSON.parse(tasks) : [];
    }
    return [];
  },

  // Import tasks from JSON (for restore)
  importTasks: (tasks: any[]) => {
    if (typeof window !== 'undefined') {
      window.localStorage.setItem('scheduled-tasks', JSON.stringify(tasks));
    }
  },

  // Get storage size info
  getStorageInfo: () => {
    if (typeof window === 'undefined') return { size: 0, sizeFormatted: '0 B' };
    
    const tasks = window.localStorage.getItem('scheduled-tasks') || '[]';
    const sizeInBytes = new Blob([tasks]).size;
    
    // Format size
    const formatSize = (bytes: number) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return {
      size: sizeInBytes,
      sizeFormatted: formatSize(sizeInBytes),
      taskCount: JSON.parse(tasks).length
    };
  }
};
