import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Task, TasksByDate } from '@/types/task'

interface TaskState {
  // Task data
  tasks: Task[]
  tasksByDate: TasksByDate
  selectedTask: Task | null
  
  // UI state
  isLoading: boolean
  error: string | null
  
  // Filter/search state
  searchQuery: string
  selectedCategory: string
  selectedPriority: string
  selectedStatus: string
  
  // Actions
  setTasks: (tasks: Task[]) => void
  addTask: (task: Task) => void
  updateTask: (taskId: string, updates: Partial<Task>) => void
  deleteTask: (taskId: string) => void
  setSelectedTask: (task: Task | null) => void
  
  // UI actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Filter actions
  setSearchQuery: (query: string) => void
  setSelectedCategory: (category: string) => void
  setSelectedPriority: (priority: string) => void
  setSelectedStatus: (status: string) => void
  
  // Computed getters
  getTasksForDate: (date: Date) => Task[]
  getFilteredTasks: () => Task[]
  getTaskById: (id: string) => Task | undefined
  
  // Reset actions
  resetFilters: () => void
  clearError: () => void
}

// Helper function to group tasks by date
const groupTasksByDate = (tasks: Task[]): TasksByDate => {
  return tasks.reduce((acc, task) => {
    const date = new Date(task.dueDate).toISOString().split('T')[0]
    if (!acc[date]) {
      acc[date] = []
    }
    acc[date].push(task)
    return acc
  }, {} as TasksByDate)
}

export const useTaskStore = create<TaskState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        tasks: [],
        tasksByDate: {},
        selectedTask: null,
        isLoading: false,
        error: null,
        searchQuery: '',
        selectedCategory: '',
        selectedPriority: '',
        selectedStatus: '',
        
        // Task actions
        setTasks: (tasks) => set((state) => ({
          tasks,
          tasksByDate: groupTasksByDate(tasks)
        })),
        
        addTask: (task) => set((state) => {
          const newTasks = [task, ...state.tasks]
          return {
            tasks: newTasks,
            tasksByDate: groupTasksByDate(newTasks)
          }
        }),
        
        updateTask: (taskId, updates) => set((state) => {
          const updatedTasks = state.tasks.map(task =>
            task.id === taskId ? { ...task, ...updates } : task
          )
          return {
            tasks: updatedTasks,
            tasksByDate: groupTasksByDate(updatedTasks),
            selectedTask: state.selectedTask?.id === taskId 
              ? { ...state.selectedTask, ...updates }
              : state.selectedTask
          }
        }),
        
        deleteTask: (taskId) => set((state) => {
          const filteredTasks = state.tasks.filter(task => task.id !== taskId)
          return {
            tasks: filteredTasks,
            tasksByDate: groupTasksByDate(filteredTasks),
            selectedTask: state.selectedTask?.id === taskId ? null : state.selectedTask
          }
        }),
        
        setSelectedTask: (task) => set({ selectedTask: task }),
        
        // UI actions
        setLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        
        // Filter actions
        setSearchQuery: (searchQuery) => set({ searchQuery }),
        setSelectedCategory: (selectedCategory) => set({ selectedCategory }),
        setSelectedPriority: (selectedPriority) => set({ selectedPriority }),
        setSelectedStatus: (selectedStatus) => set({ selectedStatus }),
        
        // Computed getters
        getTasksForDate: (date) => {
          const dateKey = date.toISOString().split('T')[0]
          return get().tasksByDate[dateKey] || []
        },
        
        getFilteredTasks: () => {
          const state = get()
          let filtered = state.tasks
          
          // Apply search filter
          if (state.searchQuery) {
            const query = state.searchQuery.toLowerCase()
            filtered = filtered.filter(task =>
              task.title.toLowerCase().includes(query) ||
              task.description.toLowerCase().includes(query) ||
              task.category.toLowerCase().includes(query)
            )
          }
          
          // Apply category filter
          if (state.selectedCategory) {
            filtered = filtered.filter(task => task.category === state.selectedCategory)
          }
          
          // Apply priority filter
          if (state.selectedPriority) {
            filtered = filtered.filter(task => task.priority === state.selectedPriority)
          }
          
          // Apply status filter
          if (state.selectedStatus) {
            filtered = filtered.filter(task => task.status === state.selectedStatus)
          }
          
          return filtered
        },
        
        getTaskById: (id) => {
          return get().tasks.find(task => task.id === id)
        },
        
        // Reset actions
        resetFilters: () => set({
          searchQuery: '',
          selectedCategory: '',
          selectedPriority: '',
          selectedStatus: ''
        }),
        
        clearError: () => set({ error: null })
      }),
      {
        name: 'task-store',
        partialize: (state) => ({
          // Only persist certain parts of the state
          tasks: state.tasks,
          tasksByDate: state.tasksByDate,
          searchQuery: state.searchQuery,
          selectedCategory: state.selectedCategory,
          selectedPriority: state.selectedPriority,
          selectedStatus: state.selectedStatus
        })
      }
    ),
    {
      name: 'task-store'
    }
  )
)
