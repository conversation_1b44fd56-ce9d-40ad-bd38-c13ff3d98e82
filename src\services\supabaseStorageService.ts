import { supabase } from '@/lib/supabase';
import { Task } from '@/types/task';
import { TaskService } from './taskService';

interface StorageInfo {
  taskCount: number;
  totalSize: number;
  sizeFormatted: string;
  attachmentCount: number;
  voiceNoteCount: number;
}

export class SupabaseStorageService {
  // Get comprehensive storage information
  static async getStorageInfo(userId: string): Promise<StorageInfo> {
    try {
      // Get task count
      const { data: tasks, error: taskError } = await supabase
        .from('tasks')
        .select('id')
        .eq('user_id', userId);

      if (taskError) throw taskError;

      // Get file attachments info
      const { data: attachments, error: attachmentError } = await supabase
        .from('file_attachments')
        .select('file_size, file_type')
        .eq('user_id', userId);

      if (attachmentError) throw attachmentError;

      // Calculate total size and counts
      const totalSize = attachments?.reduce((sum, file) => sum + file.file_size, 0) || 0;
      const attachmentCount = attachments?.filter(file => !file.file_type.includes('audio')).length || 0;
      const voiceNoteCount = attachments?.filter(file => file.file_type.includes('audio')).length || 0;

      return {
        taskCount: tasks?.length || 0,
        totalSize,
        sizeFormatted: this.formatBytes(totalSize),
        attachmentCount,
        voiceNoteCount,
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return {
        taskCount: 0,
        totalSize: 0,
        sizeFormatted: '0 B',
        attachmentCount: 0,
        voiceNoteCount: 0,
      };
    }
  }

  // Export all user data
  static async exportUserData(userId: string): Promise<any> {
    try {
      // Get all tasks
      const tasks = await TaskService.fetchTasks(userId);

      // Get all file attachments metadata
      const { data: attachments, error: attachmentError } = await supabase
        .from('file_attachments')
        .select('*')
        .eq('user_id', userId);

      if (attachmentError) throw attachmentError;

      // Get profile data
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code !== 'PGRST116') throw profileError;

      // Get app settings from localStorage
      const appSettings = localStorage.getItem('app-settings');
      const settings = appSettings ? JSON.parse(appSettings) : {};

      return {
        version: '1.0',
        exportDate: new Date().toISOString(),
        userId,
        profile: profile || null,
        tasks,
        attachments: attachments || [],
        settings,
        summary: {
          taskCount: tasks.length,
          attachmentCount: attachments?.length || 0,
          totalSize: attachments?.reduce((sum, file) => sum + file.file_size, 0) || 0,
        },
      };
    } catch (error) {
      console.error('Error exporting user data:', error);
      throw error;
    }
  }

  // Import user data (tasks only, attachments would need to be re-uploaded)
  static async importUserData(userId: string, importData: any): Promise<void> {
    try {
      if (!importData.tasks || !Array.isArray(importData.tasks)) {
        throw new Error('Invalid import data: tasks array not found');
      }

      // Import tasks
      const importPromises = importData.tasks.map((task: Task) => {
        // Remove ID to create new records
        const { id, ...taskData } = task;
        return TaskService.createTask(taskData, userId);
      });

      await Promise.all(importPromises);

      // Import settings if available
      if (importData.settings) {
        localStorage.setItem('app-settings', JSON.stringify(importData.settings));
      }
    } catch (error) {
      console.error('Error importing user data:', error);
      throw error;
    }
  }

  // Clear all user data
  static async clearAllUserData(userId: string): Promise<void> {
    try {
      // Delete all file attachments from storage
      const { data: attachments } = await supabase
        .from('file_attachments')
        .select('storage_path')
        .eq('user_id', userId);

      if (attachments && attachments.length > 0) {
        const filePaths = attachments.map(att => att.storage_path);
        await supabase.storage
          .from('task-attachments')
          .remove(filePaths);
      }

      // Delete all file attachment records
      await supabase
        .from('file_attachments')
        .delete()
        .eq('user_id', userId);

      // Delete all tasks
      await supabase
        .from('tasks')
        .delete()
        .eq('user_id', userId);

      // Clear local settings
      localStorage.removeItem('app-settings');
    } catch (error) {
      console.error('Error clearing user data:', error);
      throw error;
    }
  }

  // Get storage usage by type
  static async getStorageBreakdown(userId: string): Promise<{
    tasks: number;
    attachments: number;
    voiceNotes: number;
    images: number;
    documents: number;
    other: number;
  }> {
    try {
      const { data: attachments, error } = await supabase
        .from('file_attachments')
        .select('file_size, file_type')
        .eq('user_id', userId);

      if (error) throw error;

      const breakdown = {
        tasks: 0, // Task data is minimal
        attachments: 0,
        voiceNotes: 0,
        images: 0,
        documents: 0,
        other: 0,
      };

      attachments?.forEach(file => {
        if (file.file_type.startsWith('audio/')) {
          breakdown.voiceNotes += file.file_size;
        } else if (file.file_type.startsWith('image/')) {
          breakdown.images += file.file_size;
        } else if (
          file.file_type.includes('pdf') ||
          file.file_type.includes('document') ||
          file.file_type.includes('text')
        ) {
          breakdown.documents += file.file_size;
        } else {
          breakdown.other += file.file_size;
        }
      });

      breakdown.attachments = breakdown.images + breakdown.documents + breakdown.other;

      return breakdown;
    } catch (error) {
      console.error('Error getting storage breakdown:', error);
      return {
        tasks: 0,
        attachments: 0,
        voiceNotes: 0,
        images: 0,
        documents: 0,
        other: 0,
      };
    }
  }

  // Format bytes to human readable format
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Clean up orphaned files (files without corresponding tasks)
  static async cleanupOrphanedFiles(userId: string): Promise<number> {
    try {
      // Get all file attachments
      const { data: attachments, error: attachmentError } = await supabase
        .from('file_attachments')
        .select('id, task_id, storage_path')
        .eq('user_id', userId);

      if (attachmentError) throw attachmentError;

      // Get all existing task IDs
      const { data: tasks, error: taskError } = await supabase
        .from('tasks')
        .select('id')
        .eq('user_id', userId);

      if (taskError) throw taskError;

      const existingTaskIds = new Set(tasks?.map(task => task.id) || []);
      const orphanedFiles = attachments?.filter(attachment => 
        !existingTaskIds.has(attachment.task_id)
      ) || [];

      // Delete orphaned files
      if (orphanedFiles.length > 0) {
        // Delete from storage
        const filePaths = orphanedFiles.map(file => file.storage_path);
        await supabase.storage
          .from('task-attachments')
          .remove(filePaths);

        // Delete from database
        const fileIds = orphanedFiles.map(file => file.id);
        await supabase
          .from('file_attachments')
          .delete()
          .in('id', fileIds);
      }

      return orphanedFiles.length;
    } catch (error) {
      console.error('Error cleaning up orphaned files:', error);
      return 0;
    }
  }
}
