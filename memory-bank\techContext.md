# Technical Context

## Technology Stack

### Frontend (Web)
- **Framework**: React 18.3.1 with TypeScript 5.5.3
- **Build Tool**: Vite 5.4.1
- **Styling**: Tailwind CSS 3.4.11 with custom animations
- **State Management**: Zustand 5.0.6 + React Query 5.56.2
- **UI Components**: Radix UI primitives + shadcn/ui
- **Routing**: React Router 6.26.2
- **Forms**: React Hook Form 7.53.0 with Zod validation
- **Charts**: Recharts 2.12.7 for analytics visualization

### Mobile Development
- **Framework**: React Native with Expo
- **Platform**: Capacitor 7.4.2 for native functionality
- **Key Capacitor Plugins**:
  - Camera, Filesystem, Geolocation
  - Push Notifications, Local Notifications
  - Device, Network, Haptics
  - App Launcher for deep linking

### Backend & Infrastructure
- **Database**: Supabase (PostgreSQL + Real-time)
- **Authentication**: Supabase Auth (Email/Password, OAuth providers)
- **Payments**: Stripe integration
- **File Storage**: Supabase Storage
- **Caching**: Redis 5.6.0
- **Logging**: Winston 3.17.0

### Development Tools
- **Testing**: Vitest 1.0.4 + React Testing Library
- **E2E Testing**: Playwright
- **Linting**: ESLint 9.9.0 with Airbnb config
- **Code Quality**: Prettier, Husky pre-commit hooks
- **Bundle Analysis**: Vite Bundle Analyzer

## Key Dependencies Analysis

### Core Libraries
- **@dnd-kit/**: Drag and drop functionality for task reordering
- **framer-motion**: Smooth animations and transitions
- **react-day-picker**: Calendar component for date selection
- **embla-carousel-react**: Touch-friendly carousel components
- **cmdk**: Command palette interface (⌘K)

### Internationalization
- **i18next**: Internationalization framework
- **react-i18next**: React bindings for i18next
- **i18next-browser-languagedetector**: Automatic language detection

### Utility Libraries
- **class-variance-authority**: Component variant management
- **tailwind-merge**: Intelligent Tailwind class merging
- **clsx**: Conditional className utility
- **date-fns**: Date manipulation and formatting
- **zod**: Runtime type validation

## Development Environment Requirements

### Node.js & Package Manager
- **Node.js**: 18+ (required)
- **Package Manager**: npm or bun (bun.lockb detected)

### Environment Variables Required
```bash
# Supabase Configuration
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=

# Clerk Authentication
VITE_CLERK_PUBLISHABLE_KEY=

# Stripe (Optional)
VITE_STRIPE_PUBLISHABLE_KEY=
```

### Mobile Environment Variables
```bash
# For TaskManagerMobile/
EXPO_PUBLIC_SUPABASE_URL=
EXPO_PUBLIC_SUPABASE_ANON_KEY=
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=
```

## Architecture Patterns

### Component Architecture
- **Atomic Design**: Components organized by complexity
- **Compound Components**: Using Radix UI patterns
- **Custom Hooks**: Business logic extracted into reusable hooks
- **Service Layer**: API calls abstracted into services

### State Management
- **Zustand**: Global state for app-wide data
- **React Query**: Server state management with caching
- **Local Storage**: Persistent user preferences
- **URL State**: Navigation and filter state

### File Structure
```
src/
├── components/          # Reusable UI components
├── pages/              # Route-based components
├── hooks/              # Custom React hooks
├── services/           # API and business logic
├── stores/             # Zustand state stores
├── lib/                # Utility functions
├── types/              # TypeScript definitions
└── integrations/       # Third-party integrations
```

## Performance Considerations

### Bundle Optimization
- Code splitting with lazy loading
- Dynamic imports for heavy components
- Tree shaking enabled
- Image optimization with lazy loading

### Runtime Performance
- React.memo for expensive components
- useMemo/useCallback for calculations
- Virtual scrolling for large lists
- Debounced search and filtering

### Caching Strategy
- React Query for API response caching
- Service Worker for offline support
- Redis for server-side caching
- Browser storage for user preferences