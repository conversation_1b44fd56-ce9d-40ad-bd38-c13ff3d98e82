import { useState, useEffect, useCallback } from 'react';

interface OnboardingState {
  hasSeenWelcome: boolean;
  hasCreatedFirstTask: boolean;
  hasUsedCalendar: boolean;

  currentStep: string | null;
  completedSteps: string[];
  skippedOnboarding: boolean;
}

const ONBOARDING_STORAGE_KEY = 'taskmanager-onboarding';

const defaultOnboardingState: OnboardingState = {
  hasSeenWelcome: false,
  hasCreatedFirstTask: false,
  hasUsedCalendar: false,

  currentStep: null,
  completedSteps: [],
  skippedOnboarding: false
};

export const useAdvancedOnboarding = () => {
  const [onboardingState, setOnboardingState] = useState<OnboardingState>(() => {
    if (typeof window === 'undefined') return defaultOnboardingState;
    
    try {
      const stored = localStorage.getItem(ONBOARDING_STORAGE_KEY);
      return stored ? JSON.parse(stored) : defaultOnboardingState;
    } catch {
      return defaultOnboardingState;
    }
  });

  // Save to localStorage whenever state changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify(onboardingState));
    }
  }, [onboardingState]);

  const updateOnboardingState = useCallback((updates: Partial<OnboardingState>) => {
    setOnboardingState(prev => ({ ...prev, ...updates }));
  }, []);

  const markStepCompleted = useCallback((step: string) => {
    setOnboardingState(prev => ({
      ...prev,
      completedSteps: [...new Set([...prev.completedSteps, step])],
      currentStep: null
    }));
  }, []);

  const setCurrentStep = useCallback((step: string | null) => {
    setOnboardingState(prev => ({ ...prev, currentStep: step }));
  }, []);

  const skipOnboarding = useCallback(() => {
    setOnboardingState(prev => ({
      ...prev,
      skippedOnboarding: true,
      currentStep: null
    }));
  }, []);

  const resetOnboarding = useCallback(() => {
    setOnboardingState(defaultOnboardingState);
  }, []);

  // Derived state - Don't show welcome to skip choice action screen
  const shouldShowWelcome = false; // Disabled to go directly to calendar
  const shouldShowTaskCreationHint = onboardingState.hasSeenWelcome && !onboardingState.hasCreatedFirstTask;
  const isOnboardingComplete = onboardingState.hasSeenWelcome && 
    onboardingState.hasCreatedFirstTask && 
    (onboardingState.skippedOnboarding || onboardingState.completedSteps.length >= 3);

  return {
    onboardingState,
    updateOnboardingState,
    markStepCompleted,
    setCurrentStep,
    skipOnboarding,
    resetOnboarding,
    shouldShowWelcome,
    shouldShowTaskCreationHint,
    isOnboardingComplete
  };
};

// Contextual tips system
interface ContextualTip {
  id: string;
  title: string;
  content: string;
  trigger: 'hover' | 'click' | 'focus' | 'auto';
  position: 'top' | 'bottom' | 'left' | 'right';
  priority: 'low' | 'medium' | 'high';
  conditions?: {
    minActions?: number;
    features?: string[];
    userType?: 'new' | 'returning' | 'power';
  };
}

const contextualTips: ContextualTip[] = [
  {
    id: 'quick-add-tip',
    title: 'Quick Add',
    content: 'Use the floating + button to quickly add tasks from anywhere in the app.',
    trigger: 'auto',
    position: 'top',
    priority: 'high',
    conditions: { minActions: 0 }
  },
  {
    id: 'keyboard-shortcuts',
    title: 'Keyboard Shortcuts',
    content: 'Press Ctrl+N to quickly create a new task, or Ctrl+/ to see all shortcuts.',
    trigger: 'hover',
    position: 'bottom',
    priority: 'medium',
    conditions: { minActions: 5, userType: 'power' }
  },
  {
    id: 'voice-notes',
    title: 'Voice Notes',
    content: 'Click the microphone icon to add voice notes to your tasks.',
    trigger: 'click',
    position: 'right',
    priority: 'medium',
    conditions: { features: ['voice-enabled'] }
  },
  {
    id: 'calendar-view',
    title: 'Calendar Integration',
    content: 'Switch to calendar view to see your tasks organized by due date.',
    trigger: 'auto',
    position: 'bottom',
    priority: 'high',
    conditions: { minActions: 3, features: ['calendar'] }
  }
];

export const useContextualTips = () => {
  const [shownTips, setShownTips] = useState<string[]>([]);
  const [currentTip, setCurrentTip] = useState<ContextualTip | null>(null);
  const [userActions, setUserActions] = useState(0);
  const [userFeatures, setUserFeatures] = useState<string[]>([]);
  const [userType, setUserType] = useState<'new' | 'returning' | 'power'>('new');

  // Load shown tips from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('taskmanager-shown-tips');
        if (stored) {
          setShownTips(JSON.parse(stored));
        }
      } catch {
        // Handle error silently
      }
    }
  }, []);

  // Save shown tips to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('taskmanager-shown-tips', JSON.stringify(shownTips));
    }
  }, [shownTips]);

  const trackUserAction = useCallback(() => {
    setUserActions(prev => {
      const newCount = prev + 1;
      // Update user type based on actions
      if (newCount > 20) setUserType('power');
      else if (newCount > 5) setUserType('returning');
      return newCount;
    });
  }, []);

  const enableFeature = useCallback((feature: string) => {
    setUserFeatures(prev => [...new Set([...prev, feature])]);
  }, []);

  const getRelevantTips = useCallback(() => {
    return contextualTips.filter(tip => {
      // Skip if already shown
      if (shownTips.includes(tip.id)) return false;

      // Check conditions
      if (tip.conditions) {
        const { minActions = 0, features = [], userType: requiredUserType } = tip.conditions;
        
        if (userActions < minActions) return false;
        if (features.length > 0 && !features.some(f => userFeatures.includes(f))) return false;
        if (requiredUserType && userType !== requiredUserType) return false;
      }

      return true;
    }).sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, [shownTips, userActions, userFeatures, userType]);

  const showTip = useCallback((tipId: string) => {
    const tip = contextualTips.find(t => t.id === tipId);
    if (tip && !shownTips.includes(tipId)) {
      setCurrentTip(tip);
    }
  }, [shownTips]);

  const dismissTip = useCallback((tipId: string) => {
    setShownTips(prev => [...prev, tipId]);
    setCurrentTip(null);
  }, []);

  const showNextTip = useCallback(() => {
    const relevantTips = getRelevantTips();
    if (relevantTips.length > 0) {
      setCurrentTip(relevantTips[0]);
    }
  }, [getRelevantTips]);

  return {
    currentTip,
    relevantTips: getRelevantTips(),
    trackUserAction,
    enableFeature,
    showTip,
    dismissTip,
    showNextTip,
    userActions,
    userType
  };
};