import { supabase } from '@/integrations/supabase/client';
import { Task, TaskInsert, TaskUpdate } from '@/types/task';

interface QueuedOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  retryCount: number;
}

interface ConflictResolution {
  strategy: 'client-wins' | 'server-wins' | 'merge' | 'user-choice';
  resolvedData?: any;
}

export class UnifiedSyncService {
  private static instance: UnifiedSyncService;
  private syncQueue: QueuedOperation[] = [];
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;
  private readonly MAX_RETRIES = 3;
  private readonly SYNC_INTERVAL = 300000; // 5 minutes (reduced frequency)

  private constructor() {
    this.setupEventListeners();
    this.loadQueueFromStorage();
    this.startPeriodicSync();
  }

  static getInstance(): UnifiedSyncService {
    if (!UnifiedSyncService.instance) {
      UnifiedSyncService.instance = new UnifiedSyncService();
    }
    return UnifiedSyncService.instance;
  }

  private setupEventListeners() {
    // Online/offline detection
    window.addEventListener('online', () => {
      console.log('App came online, triggering sync...');
      this.isOnline = true;
      this.processQueue();
    });

    window.addEventListener('offline', () => {
      console.log('App went offline');
      this.isOnline = false;
    });

    // Service worker messages
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.type === 'SYNC_COMPLETE') {
          console.log('Background sync completed');
          this.processQueue();
        }
      });
    }
  }

  private loadQueueFromStorage() {
    try {
      const stored = localStorage.getItem('sync_queue');
      if (stored) {
        this.syncQueue = JSON.parse(stored);
        console.log(`Loaded ${this.syncQueue.length} operations from storage`);
      }
    } catch (error) {
      console.error('Failed to load sync queue from storage:', error);
    }
  }

  private saveQueueToStorage() {
    try {
      localStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('Failed to save sync queue to storage:', error);
    }
  }

  private startPeriodicSync() {
    setInterval(() => {
      if (this.isOnline && this.syncQueue.length > 0) {
        this.processQueue();
      }
    }, this.SYNC_INTERVAL);
  }

  // Optimistic updates for tasks
  async createTaskOptimistic(task: TaskInsert): Promise<Task> {
    const optimisticTask: Task = {
      ...task,
      id: `temp_${Date.now()}_${Math.random()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add to queue for sync
    this.queueOperation({
      id: optimisticTask.id,
      type: 'create',
      data: task,
      timestamp: Date.now(),
      retryCount: 0
    });

    if (this.isOnline) {
      this.processQueue();
    }

    return optimisticTask;
  }

  async updateTaskOptimistic(id: string, updates: TaskUpdate): Promise<void> {
    // Add to queue for sync
    this.queueOperation({
      id: `update_${id}_${Date.now()}`,
      type: 'update',
      data: { id, updates },
      timestamp: Date.now(),
      retryCount: 0
    });

    if (this.isOnline) {
      this.processQueue();
    }
  }

  async deleteTaskOptimistic(id: string): Promise<void> {
    // Add to queue for sync
    this.queueOperation({
      id: `delete_${id}_${Date.now()}`,
      type: 'delete',
      data: { id },
      timestamp: Date.now(),
      retryCount: 0
    });

    if (this.isOnline) {
      this.processQueue();
    }
  }

  private queueOperation(operation: QueuedOperation) {
    this.syncQueue.push(operation);
    this.saveQueueToStorage();
    console.log(`Queued ${operation.type} operation:`, operation.id);
  }

  private async processQueue() {
    if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`Processing ${this.syncQueue.length} queued operations...`);

    const processedOperations: string[] = [];

    for (const operation of [...this.syncQueue]) {
      try {
        await this.executeOperation(operation);
        processedOperations.push(operation.id);
        console.log(`Successfully synced operation: ${operation.id}`);
      } catch (error) {
        console.error(`Failed to sync operation ${operation.id}:`, error);
        
        // Increment retry count
        operation.retryCount++;
        
        if (operation.retryCount >= this.MAX_RETRIES) {
          console.warn(`Operation ${operation.id} exceeded max retries, removing from queue`);
          processedOperations.push(operation.id);
        }
      }
    }

    // Remove processed operations from queue
    this.syncQueue = this.syncQueue.filter(op => !processedOperations.includes(op.id));
    this.saveQueueToStorage();
    
    this.syncInProgress = false;
    console.log(`Sync completed. ${this.syncQueue.length} operations remaining in queue.`);
  }

  private async executeOperation(operation: QueuedOperation): Promise<void> {
    switch (operation.type) {
      case 'create':
        await this.syncCreateTask(operation);
        break;
      case 'update':
        await this.syncUpdateTask(operation);
        break;
      case 'delete':
        await this.syncDeleteTask(operation);
        break;
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  private async syncCreateTask(operation: QueuedOperation): Promise<void> {
    const { data, error } = await supabase
      .from('tasks')
      .insert(operation.data)
      .select()
      .single();

    if (error) {
      // Check for conflicts
      if (error.code === '23505') { // Unique constraint violation
        console.log('Conflict detected during task creation, resolving...');
        await this.handleCreateConflict(operation);
        return;
      }
      throw error;
    }

    // Notify about successful creation
    this.notifyOperationComplete('create', data);
  }

  private async syncUpdateTask(operation: QueuedOperation): Promise<void> {
    const { id, updates } = operation.data;
    
    // Get current server state
    const { data: serverTask, error: fetchError } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') { // Not found
        console.log('Task not found on server, may have been deleted');
        return;
      }
      throw fetchError;
    }

    // Check for conflicts
    const hasConflict = await this.detectUpdateConflict({
      ...serverTask,
      createdAt: serverTask.created_at,
      updatedAt: serverTask.updated_at
    } as any, updates, operation.timestamp);
    
    if (hasConflict) {
      console.log('Conflict detected during task update, resolving...');
      const resolution = await this.resolveUpdateConflict({
        ...serverTask,
        createdAt: serverTask.created_at,
        updatedAt: serverTask.updated_at
      } as any, updates);
      
      if (resolution.strategy === 'user-choice') {
        // Store conflict for user resolution
        this.storeConflictForUserResolution(operation, serverTask);
        return;
      }
      
      // Apply automatic resolution
      const finalUpdates = resolution.resolvedData || updates;
      const { error } = await supabase
        .from('tasks')
        .update(finalUpdates)
        .eq('id', id);
        
      if (error) throw error;
    } else {
      // No conflict, apply updates
      const { error } = await supabase
        .from('tasks')
        .update(updates)
        .eq('id', id);
        
      if (error) throw error;
    }

    this.notifyOperationComplete('update', { id, updates });
  }

  private async syncDeleteTask(operation: QueuedOperation): Promise<void> {
    const { id } = operation.data;
    
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', id);

    if (error && error.code !== 'PGRST116') { // Ignore "not found" errors
      throw error;
    }

    this.notifyOperationComplete('delete', { id });
  }

  private async detectUpdateConflict(serverTask: Task, localUpdates: TaskUpdate, localTimestamp: number): Promise<boolean> {
    const serverModified = new Date(serverTask.updatedAt).getTime();
    const localModified = localTimestamp;
    
    // Conflict if server was modified after local change
    return serverModified > localModified;
  }

  private async resolveUpdateConflict(serverTask: Task, localUpdates: TaskUpdate): Promise<ConflictResolution> {
    // Simple merge strategy - prefer local changes for most fields
    const mergedUpdates = {
      ...localUpdates,
      updatedAt: new Date().toISOString()
    };

    // For status changes, prefer the more advanced status
    if (localUpdates.status && serverTask.status !== localUpdates.status) {
      const statusPriority = {
        'pending': 1,
  
        'completed': 3,
        'cancelled': 0,
        'on-hold': 1
      };
      
      const serverPriority = statusPriority[serverTask.status] || 0;
      const localPriority = statusPriority[localUpdates.status] || 0;
      
      mergedUpdates.status = localPriority >= serverPriority ? localUpdates.status : serverTask.status;
    }

    return {
      strategy: 'merge',
      resolvedData: mergedUpdates
    };
  }

  private async handleCreateConflict(operation: QueuedOperation): Promise<void> {
    // For create conflicts, typically convert to update
    console.log('Converting create conflict to update operation');
    // Implementation would depend on specific conflict resolution strategy
  }

  private storeConflictForUserResolution(operation: QueuedOperation, serverData: any) {
    // Store conflict in local storage for user to resolve later
    const conflicts = JSON.parse(localStorage.getItem('user_conflicts') || '[]');
    conflicts.push({
      operation,
      serverData,
      timestamp: Date.now()
    });
    localStorage.setItem('user_conflicts', JSON.stringify(conflicts));
    
    // Notify UI about pending conflict
    window.dispatchEvent(new CustomEvent('conflict-detected', {
      detail: { operation, serverData }
    }));
  }

  private notifyOperationComplete(type: string, data: any) {
    window.dispatchEvent(new CustomEvent('sync-operation-complete', {
      detail: { type, data }
    }));
  }

  // Public methods
  forceSync(): Promise<void> {
    return this.processQueue();
  }

  getQueueStatus() {
    return {
      queueLength: this.syncQueue.length,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress
    };
  }

  clearQueue() {
    this.syncQueue = [];
    this.saveQueueToStorage();
  }
}