// Unified theme system for web and mobile platforms
export interface ThemeColors {
  // Base colors using HSL format
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  accent: string;
  accentForeground: string;
  background: string;
  foreground: string;
  muted: string;
  mutedForeground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  border: string;
  input: string;
  ring: string;
  destructive: string;
  destructiveForeground: string;
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
}

export interface ThemeSpacing {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
  '3xl': number;
  '4xl': number;
}

export interface ThemeBorderRadius {
  none: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  full: number;
}

export interface ThemeTypography {
  fontFamily: {
    sans: string[];
    mono: string[];
  };
  fontSize: {
    xs: [string, string];
    sm: [string, string];
    base: [string, string];
    lg: [string, string];
    xl: [string, string];
    '2xl': [string, string];
    '3xl': [string, string];
    '4xl': [string, string];
  };
  fontWeight: {
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
  };
}

export interface UnifiedTheme {
  colors: {
    light: ThemeColors;
    dark: ThemeColors;
  };
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
  typography: ThemeTypography;
  breakpoints: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
}

// Default theme configuration
export const defaultTheme: UnifiedTheme = {
  colors: {
    light: {
      primary: '220 90% 56%',
      primaryForeground: '0 0% 100%',
      secondary: '220 14% 96%',
      secondaryForeground: '220 9% 46%',
      accent: '220 14% 96%',
      accentForeground: '220 9% 46%',
      background: '0 0% 100%',
      foreground: '220 9% 9%',
      muted: '220 14% 96%',
      mutedForeground: '220 9% 46%',
      card: '0 0% 100%',
      cardForeground: '220 9% 9%',
      popover: '0 0% 100%',
      popoverForeground: '220 9% 9%',
      border: '220 13% 91%',
      input: '220 13% 91%',
      ring: '220 90% 56%',
      destructive: '0 84% 60%',
      destructiveForeground: '0 0% 100%',
      success: '142 76% 36%',
      successForeground: '0 0% 100%',
      warning: '38 92% 50%',
      warningForeground: '0 0% 100%',
    },
    dark: {
      primary: '220 90% 56%',
      primaryForeground: '220 9% 9%',
      secondary: '217 33% 17%',
      secondaryForeground: '0 0% 100%',
      accent: '217 33% 17%',
      accentForeground: '0 0% 100%',
      background: '220 13% 9%',
      foreground: '0 0% 100%',
      muted: '217 33% 17%',
      mutedForeground: '215 20% 65%',
      card: '220 13% 9%',
      cardForeground: '0 0% 100%',
      popover: '220 13% 9%',
      popoverForeground: '0 0% 100%',
      border: '217 33% 17%',
      input: '217 33% 17%',
      ring: '220 90% 56%',
      destructive: '0 63% 31%',
      destructiveForeground: '0 0% 100%',
      success: '142 76% 36%',
      successForeground: '0 0% 100%',
      warning: '38 92% 50%',
      warningForeground: '0 0% 100%',
    },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
    '4xl': 96,
  },
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
  typography: {
    fontFamily: {
      sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      mono: ['Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
    },
    fontSize: {
      xs: ['12px', '16px'],
      sm: ['14px', '20px'],
      base: ['16px', '24px'],
      lg: ['18px', '28px'],
      xl: ['20px', '28px'],
      '2xl': ['24px', '32px'],
      '3xl': ['30px', '36px'],
      '4xl': ['36px', '40px'],
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
};

// Theme utilities
export const createThemeVars = (colors: ThemeColors) => {
  const vars: Record<string, string> = {};
  
  Object.entries(colors).forEach(([key, value]) => {
    vars[`--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`] = value;
  });
  
  return vars;
};

export const getColorValue = (color: string) => `hsl(${color})`;

// Responsive utilities
export const createResponsiveStyle = (
  property: string,
  values: { [key: string]: string | number },
  breakpoints: UnifiedTheme['breakpoints']
) => {
  const styles: Record<string, any> = {};
  
  // Base value
  if (values.base !== undefined) {
    styles[property] = values.base;
  }
  
  // Responsive values
  Object.entries(breakpoints).forEach(([breakpoint, minWidth]) => {
    if (values[breakpoint] !== undefined) {
      styles[`@media (min-width: ${minWidth})`] = {
        [property]: values[breakpoint],
      };
    }
  });
  
  return styles;
};