import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface ExportRequest {
  format: 'csv' | 'json';
  dateRange?: {
    start: string;
    end: string;
  };
  status?: string[];
  category?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Authenticate user
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("No authorization header provided");
    }

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? ""
    );

    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    if (userError || !userData.user) {
      throw new Error("Invalid authentication token");
    }

    const { format, dateRange, status, category }: ExportRequest = await req.json();

    // Build query
    let query = supabaseClient
      .from('tasks')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (dateRange) {
      query = query
        .gte('date', dateRange.start)
        .lte('date', dateRange.end);
    }

    if (status && status.length > 0) {
      query = query.in('status', status);
    }

    if (category) {
      query = query.eq('category', category);
    }

    const { data: tasks, error } = await query;

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (format === 'csv') {
      // Generate CSV
      const csvHeader = 'Title,Description,Status,Priority,Category,Date,Due Date,Created At,Completed At\n';
      const csvRows = tasks.map(task => {
        const row = [
          task.title?.replace(/"/g, '""') || '',
          task.description?.replace(/"/g, '""') || '',
          task.status || '',
          task.priority || '',
          task.category || '',
          task.date || '',
          task.due_date || '',
          task.created_at || '',
          task.completed_at || ''
        ];
        return `"${row.join('","')}"`;
      }).join('\n');

      const csvContent = csvHeader + csvRows;

      return new Response(csvContent, {
        headers: {
          ...corsHeaders,
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="tasks_export_${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    } else {
      // Generate JSON
      const jsonContent = JSON.stringify({
        exportDate: new Date().toISOString(),
        totalTasks: tasks.length,
        filters: { dateRange, status, category },
        tasks: tasks
      }, null, 2);

      return new Response(jsonContent, {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="tasks_export_${new Date().toISOString().split('T')[0]}.json"`
        }
      });
    }

  } catch (error: any) {
    console.error("Error in export-tasks function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      }
    );
  }
};

serve(handler);