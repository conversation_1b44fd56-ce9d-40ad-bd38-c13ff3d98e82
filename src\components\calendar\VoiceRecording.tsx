import { useState, useRef, useEffect } from 'react';
import { Mic, Square, Play, Pause, Trash2, Send, FileAudio, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { VoiceNoteService } from '@/services/taskService';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';
import { Textarea } from '@/components/ui/textarea';

interface VoiceRecordingProps {
  isOpen: boolean;
  onClose: () => void;
  onVoiceNoteSaved: (voiceNoteUrl: string) => void;
  taskId?: string; // Optional taskId for immediate upload
}

export const VoiceRecording: React.FC<VoiceRecordingProps> = ({
  isOpen,
  onClose,
  onVoiceNoteSaved,
  taskId,
}) => {
  const { toast } = useToast();
  
  // Get current user from Supabase Auth
  const [currentUser, setCurrentUser] = useState<any>(null);
  
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
    };
    getCurrentUser();
  }, []);

  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioURL, setAudioURL] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [transcription, setTranscription] = useState<string>('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [showTranscription, setShowTranscription] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (!isOpen) {
      // Reset state when dialog closes
      setIsRecording(false);
      setRecordingTime(0);
      setAudioURL(null);
      setIsPlaying(false);
      setError(null);
      setTranscription('');
      setShowTranscription(false);
      audioChunks.current = [];
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
  }, [isOpen]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunks.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks.current, { type: 'audio/webm' });
        const url = URL.createObjectURL(audioBlob);
        setAudioURL(url);
      };

      mediaRecorder.start();
      setIsRecording(true);
      setError(null);

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);

    } catch (err) {
      console.error('Error starting recording:', err);
      setError('Failed to access microphone. Please check permissions.');
    }
  };

  const stopRecording = async () => {
    if (!mediaRecorderRef.current || !isRecording) return;

    setIsRecording(false);
    mediaRecorderRef.current.stop();
    mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Auto-transcribe the recording
    setTimeout(() => {
      if (audioChunks.current.length > 0) {
        transcribeAudio();
      }
    }, 1000);
  };

  const togglePlayback = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const saveVoiceNote = async () => {
    if (!audioURL || !currentUser) return;

    setIsUploading(true);
    setError(null);

    try {
      // If we have a taskId, upload to Supabase, otherwise use blob URL for pending voice notes
      if (taskId) {
        console.log('Uploading voice note to Supabase for task:', taskId);
        
        // Convert blob URL to blob
        const response = await fetch(audioURL);
        const audioBlob = await response.blob();
        
        const voiceNoteUrl = await VoiceNoteService.uploadVoiceNote(
          audioBlob,
          currentUser.id,
          taskId,
          supabase as any
        );
        
        toast({
          title: 'Voice Note Saved',
          description: 'Your voice note has been uploaded successfully.',
        });
        
        onVoiceNoteSaved(voiceNoteUrl);
      } else {
        console.log('Saving voice note as blob URL for new task');
        
        // For new tasks, just pass the blob URL
        onVoiceNoteSaved(audioURL);
        
        toast({
          title: 'Voice Note Added',
          description: 'Voice note will be saved when the task is created.',
        });
      }
      
      // Include transcription in the voice note if available
      if (transcription.trim()) {
        console.log('Voice note transcription:', transcription);
      }
      
      onClose();
    } catch (err: any) {
      console.error('Error saving voice note:', err);
      setError(err.message || 'Failed to save voice note');
      toast({
        title: 'Error',
        description: 'Failed to save voice note. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const transcribeAudio = async () => {
    if (!audioURL) return;

    setIsTranscribing(true);
    setError(null);

    try {
      // Convert blob URL to base64
      const response = await fetch(audioURL);
      const blob = await response.blob();
      
      const reader = new FileReader();
      reader.onload = async () => {
        try {
          const base64Audio = (reader.result as string).split(',')[1];
          
          const { data, error } = await supabase.functions.invoke('voice-to-text', {
            body: { audio: base64Audio }
          });

          if (error) throw error;

          setTranscription(data.text);
          setShowTranscription(true);
          toast({
            title: 'Transcription Complete',
            description: 'Your voice note has been transcribed successfully.',
          });
        } catch (error: any) {
          console.error('Transcription error:', error);
          toast({
            title: 'Transcription Failed',
            description: 'Failed to transcribe audio. Please try again.',
            variant: 'destructive',
          });
        } finally {
          setIsTranscribing(false);
        }
      };
      
      reader.readAsDataURL(blob);
    } catch (error: any) {
      console.error('Error preparing audio for transcription:', error);
      setIsTranscribing(false);
    }
  };

  const resetRecording = () => {
    setAudioURL(null);
    setRecordingTime(0);
    setError(null);
    setTranscription('');
    setShowTranscription(false);
    audioChunks.current = [];
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileAudio className="h-5 w-5" />
            Voice Recording
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {!audioURL && !isRecording && (
            <div className="text-center space-y-4">
              <div className="flex flex-col items-center space-y-3">
                <div className="w-20 h-20 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                  <Mic className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <p className="text-muted-foreground">
                  Click the button below to start recording your voice note
                </p>
              </div>
            </div>
          )}

          {isRecording && (
            <div className="text-center space-y-4">
              <Button
                onClick={stopRecording}
                disabled={isUploading}
                className="w-24 h-24 rounded-full bg-red-500 hover:bg-red-600 animate-pulse"
              >
                <Square className="h-8 w-8" />
              </Button>
              
              <div className="space-y-2">
                <p className="text-red-500 font-mono text-xl">
                  {formatTime(recordingTime)}
                </p>
                <p className="text-muted-foreground text-sm">Recording in progress...</p>
              </div>
            </div>
          )}

          {!isRecording && !audioURL && (
            <div className="text-center">
              <Button
                onClick={startRecording}
                disabled={isUploading}
                className="w-24 h-24 rounded-full bg-blue-500 hover:bg-blue-600"
              >
                <Mic className="h-8 w-8" />
              </Button>
            </div>
          )}

          {audioURL && (
            <div className="space-y-6">
              <div className="flex items-center justify-center space-x-4">
                <Button
                  onClick={togglePlayback}
                  variant="outline"
                  size="lg"
                  className="rounded-full"
                >
                  {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                </Button>
                
                <div className="flex-1 max-w-xs">
                  <div className="text-sm text-muted-foreground mb-1">
                    Duration: {formatTime(recordingTime)}
                  </div>
                  <div className="h-2 bg-gray-200 rounded-full">
                    <div 
                      className="h-2 bg-blue-500 rounded-full transition-all duration-300"
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>

                <Button
                  onClick={resetRecording}
                  variant="outline"
                  size="sm"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-col space-y-3">
                <div className="flex space-x-2">
                  <Button
                    onClick={transcribeAudio}
                    disabled={isTranscribing}
                    variant="outline"
                    className="flex-1"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    {isTranscribing ? 'Transcribing...' : 'Transcribe Audio'}
                  </Button>
                  
                  <Button
                    onClick={saveVoiceNote}
                    disabled={isUploading}
                    className="flex-1"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isUploading ? 'Saving...' : 'Save Voice Note'}
                  </Button>
                </div>

                {showTranscription && transcription && (
                  <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">Transcription:</h4>
                      <Button
                        onClick={() => setShowTranscription(false)}
                        variant="ghost"
                        size="sm"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    <Textarea
                      value={transcription}
                      onChange={(e) => setTranscription(e.target.value)}
                      className="min-h-[80px] text-sm"
                      placeholder="Transcription will appear here..."
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      You can edit the transcription before saving
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {error && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
            </div>
          )}

          <audio
            ref={audioRef}
            src={audioURL || undefined}
            onEnded={() => setIsPlaying(false)}
            className="hidden"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};