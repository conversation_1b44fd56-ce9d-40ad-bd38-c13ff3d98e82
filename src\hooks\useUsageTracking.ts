import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface UsageStats {
  tasksCreated: number;
  tasksCompleted: number;
  daysActive: number;
  lastActivity: string;
}

export const useUsageTracking = () => {
  const { user } = useAuth();
  const isSignedIn = !!user;
  const userId = user?.id;
  const [usageStats, setUsageStats] = useState<UsageStats>({
    tasksCreated: 0,
    tasksCompleted: 0,
    daysActive: 0,
    lastActivity: new Date().toISOString(),
  });

  useEffect(() => {
    if (!isSignedIn || !userId) return;

    const loadUsageStats = () => {
      const stored = localStorage.getItem(`usage_${userId}`);
      if (stored) {
        setUsageStats(JSON.parse(stored));
      }
    };

    loadUsageStats();
  }, [isSignedIn, userId]);

  const trackTaskCreated = () => {
    if (!isSignedIn || !userId) return;
    
    setUsageStats(prev => {
      const newStats = {
        ...prev,
        tasksCreated: prev.tasksCreated + 1,
        lastActivity: new Date().toISOString(),
      };
      localStorage.setItem(`usage_${userId}`, JSON.stringify(newStats));
      return newStats;
    });
  };

  const trackTaskCompleted = () => {
    if (!isSignedIn || !userId) return;
    
    setUsageStats(prev => {
      const newStats = {
        ...prev,
        tasksCompleted: prev.tasksCompleted + 1,
        lastActivity: new Date().toISOString(),
      };
      localStorage.setItem(`usage_${userId}`, JSON.stringify(newStats));
      return newStats;
    });
  };

  const trackDailyActivity = () => {
    if (!isSignedIn || !userId) return;
    
    setUsageStats(prev => {
      const today = new Date().toDateString();
      const lastActivity = new Date(prev.lastActivity).toDateString();
      
      if (today !== lastActivity) {
        const newStats = {
          ...prev,
          daysActive: prev.daysActive + 1,
          lastActivity: new Date().toISOString(),
        };
        localStorage.setItem(`usage_${userId}`, JSON.stringify(newStats));
        return newStats;
      }
      
      return prev;
    });
  };

  const shouldShowUpgradePrompt = (): boolean => {
    if (!isSignedIn || !userId) return false;
    
    const { tasksCreated, tasksCompleted, daysActive } = usageStats;
    
    // Show upgrade prompt based on usage patterns
    return (
      tasksCreated >= 10 ||           // Created 10+ tasks
      tasksCompleted >= 5 ||          // Completed 5+ tasks
      daysActive >= 3                // Used for 3+ days
    );
  };

  const getUpgradeTriggerReason = (): string => {
    const { tasksCreated, tasksCompleted, daysActive } = usageStats;
    
    if (tasksCreated >= 10) {
      return `You've created ${tasksCreated} tasks! Ready to unlock unlimited task creation?`;
    }
    
    if (tasksCompleted >= 5) {
      return `Great job completing ${tasksCompleted} tasks! Keep the momentum going with premium features.`;
    }
    
    if (daysActive >= 3) {
      return `You've been using TaskCalendar for ${daysActive} days! Upgrade to unlock advanced features.`;
    }
    
    return "Ready to unlock more features?";
  };

  return {
    usageStats,
    trackTaskCreated,
    trackTaskCompleted,
    trackDailyActivity,
    shouldShowUpgradePrompt,
    getUpgradeTriggerReason,
  };
};