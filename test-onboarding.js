// Simple test to verify onboarding functionality
// Run this in the browser console

console.log('Testing onboarding functionality...');

// Test 1: Check if OnboardingService is available
try {
  const { OnboardingService } = await import('./src/services/onboardingService.ts');
  console.log('✅ OnboardingService loaded successfully');
  
  // Test 2: Check if we can generate sample tasks
  const sampleTasks = OnboardingService.generateSampleTasks();
  console.log('✅ Sample tasks generated:', sampleTasks.length, 'tasks');
  
  // Test 3: Check if we can manage progress
  const testUserId = 'test-user-123';
  const progress = OnboardingService.getProgress(testUserId);
  console.log('✅ Progress retrieved:', progress);
  
  // Test 4: Check if we can reset onboarding
  OnboardingService.resetOnboarding(testUserId);
  console.log('✅ Onboarding reset successful');
  
  // Test 5: Check if we can mark completion
  OnboardingService.markOnboardingComplete(testUserId);
  console.log('✅ Onboarding marked complete');
  
  console.log('🎉 All onboarding tests passed!');
  
} catch (error) {
  console.error('❌ Onboarding test failed:', error);
}

// Test notification permission issue
console.log('\nTesting notification permission...');
if ('Notification' in window) {
  console.log('✅ Notification API available');
  console.log('Current permission:', Notification.permission);
  
  // Only request permission on user interaction
  console.log('✅ Permission will only be requested on user interaction');
} else {
  console.log('❌ Notification API not available');
}

// Test onboarding route
console.log('\nTesting onboarding route...');
console.log('Current URL:', window.location.href);
console.log('To test onboarding, navigate to: /onboarding');
console.log('Or run: window.location.href = "/onboarding"');
