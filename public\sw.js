const CACHE_NAME = 'task-manager-v1';
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/src/main.tsx',
  '/src/index.css',
  '/manifest.json'
];

const SYNC_QUEUE_NAME = 'task-sync-queue';

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests differently
  if (url.hostname.includes('supabase.co')) {
    event.respondWith(handleApiRequest(request));
  } else {
    // Handle static assets
    event.respondWith(
      caches.match(request)
        .then((response) => {
          // Return cached version or fetch from network
          return response || fetch(request)
            .then((fetchResponse) => {
              // Cache successful responses
              if (fetchResponse.status === 200) {
                const responseClone = fetchResponse.clone();
                caches.open(CACHE_NAME)
                  .then((cache) => cache.put(request, responseClone));
              }
              return fetchResponse;
            });
        })
        .catch(() => {
          // If both cache and network fail, return offline page for navigation requests
          if (request.mode === 'navigate') {
            return caches.match('/');
          }
        })
    );
  }
});

// Handle API requests with offline support
async function handleApiRequest(request) {
  try {
    const response = await fetch(request.clone());
    
    // Cache successful GET requests
    if (request.method === 'GET' && response.status === 200) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    console.log('Network request failed, trying cache:', request.url);
    
    // For GET requests, try to serve from cache
    if (request.method === 'GET') {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    }
    
    // For POST/PUT/DELETE requests, queue for later sync if supported
    if (['POST', 'PUT', 'DELETE'].includes(request.method)) {
      // Only queue for sync if background sync is supported
      if ('sync' in self.registration) {
        await queueRequestForSync(request);
        
        // Return a custom response indicating the request was queued
        return new Response(
          JSON.stringify({ 
            success: true, 
            queued: true, 
            message: 'Request queued for sync when online' 
          }),
          {
            status: 202,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      } else {
        // Background sync not supported, return error
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'Network unavailable and background sync not supported' 
          }),
          {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }
    }
    
    throw error;
  }
}

// Queue failed requests for background sync
async function queueRequestForSync(request) {
  const requestData = {
    url: request.url,
    method: request.method,
    headers: Object.fromEntries(request.headers.entries()),
    body: request.method !== 'GET' ? await request.text() : null,
    timestamp: Date.now()
  };
  
  // Store in IndexedDB for persistence
  const db = await openSyncDatabase();
  const transaction = db.transaction(['sync_queue'], 'readwrite');
  const store = transaction.objectStore('sync_queue');
  await store.add(requestData);
}

// Open IndexedDB for sync queue
function openSyncDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('TaskManagerSync', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('sync_queue')) {
        const store = db.createObjectStore('sync_queue', { keyPath: 'id', autoIncrement: true });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
}

// Background sync event
// Only register sync event listener if background sync is supported
if ('sync' in self.registration) {
  self.addEventListener('sync', (event) => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === SYNC_QUEUE_NAME) {
      event.waitUntil(syncQueuedRequests());
    }
  });
} else {
  console.log('Background sync not supported in this environment');
}

// Sync queued requests when back online
async function syncQueuedRequests() {
  console.log('Syncing queued requests...');
  
  try {
    const db = await openSyncDatabase();
    const transaction = db.transaction(['sync_queue'], 'readwrite');
    const store = transaction.objectStore('sync_queue');
    const requests = await store.getAll();
    
    for (const requestData of requests) {
      try {
        const response = await fetch(requestData.url, {
          method: requestData.method,
          headers: requestData.headers,
          body: requestData.body
        });
        
        if (response.ok) {
          // Remove successful requests from queue
          await store.delete(requestData.id);
          console.log('Synced request:', requestData.url);
        }
      } catch (error) {
        console.error('Failed to sync request:', requestData.url, error);
        // Keep failed requests in queue for next sync attempt
      }
    }
    
    // Notify clients about sync completion
    self.clients.matchAll().then(clients => {
      clients.forEach(client => {
        client.postMessage({
          type: 'SYNC_COMPLETE',
          syncedCount: requests.length
        });
      });
    });
    
  } catch (error) {
    console.error('Error during sync:', error);
  }
}

// Handle messages from main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'FORCE_SYNC') {
    syncQueuedRequests();
  }
});
