import { Task } from '../types/task';
import { FileSystem } from 'expo-file-system';
import { Sharing } from 'expo-sharing';

export interface ExportOptions {
  format: 'pdf' | 'csv' | 'json';
  dateRange?: { start: Date; end: Date };
  statusFilter?: string[];
  priorityFilter?: string[];
  categoryFilter?: string[];
  includeCompleted?: boolean;
  includeAnalytics?: boolean;
}

export interface AnalyticsData {
  completionRate: number;
  tasksByStatus: Record<string, number>;
  tasksByPriority: Record<string, number>;
  tasksByCategory: Record<string, number>;
  trends?: {
    taskCreationTrend: number;
    completionTrend: number;
    productivityTrend: number;
  };
  timeRange?: string;
  generatedAt: string;
}

export class ExportService {
  /**
   * Export tasks to CSV format (returns CSV string)
   */
  static exportToCSV(tasks: Task[], options: ExportOptions = { format: 'csv' }, analytics?: AnalyticsData): string {
    const filteredTasks = this.filterTasks(tasks, options);
    
    // Create CSV header with enhanced fields
    const headers = [
      'ID',
      'Title',
      'Description',
      'Priority',
      'Status',
      'Category',
      'Assignment Status',
      'Assigned To',
      'Due Date',
      'Created Date',
      'Updated Date',
      'Completed Date',
      'Completion Percentage',
      'Estimated Hours',
      'Actual Hours',
      'Tags',
      'Links',
      'Location',
      'Has Voice Note',
      'Has Attachments',
      'Comments Count'
    ];
    
    // Create CSV rows with comprehensive data
    const rows = filteredTasks.map(task => [
      `"${task.id}"`,
      `"${task.title.replace(/"/g, '""')}"`,
      `"${(task.description || '').replace(/"/g, '""')}"`,
      task.priority,
      task.status,
      task.category || 'General',
      task.assignment_status || 'unassigned',
      task.assigned_to || '',
      task.dueDate || task.due_date || '',
      task.created_at || new Date().toISOString(),
      task.updated_at || '',
      task.completed_at || '',
      task.completion_percentage || '0',
      task.estimated_hours || '',
      task.actual_hours || '',
      (task.tags || []).join(';'),
      (task.links || []).join(';'),
      task.location || '',
      task.voice_note_url ? 'Yes' : 'No',
      (task.attachments && task.attachments.length > 0) ? 'Yes' : 'No',
      (task.comments && Array.isArray(task.comments)) ? task.comments.length.toString() : '0'
    ]);
    
    let csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    
    // Add analytics summary if included
    if (options.includeAnalytics && analytics) {
      csvContent += '\n\n--- ANALYTICS SUMMARY ---\n';
      csvContent += `Export Generated,${analytics.generatedAt}\n`;
      csvContent += `Time Range,${analytics.timeRange || 'All Time'}\n`;
      csvContent += `Total Tasks,${filteredTasks.length}\n`;
      csvContent += `Completion Rate,${analytics.completionRate.toFixed(1)}%\n`;
      
      csvContent += '\n--- STATUS BREAKDOWN ---\n';
      Object.entries(analytics.tasksByStatus).forEach(([status, count]) => {
        csvContent += `${status},${count}\n`;
      });
      
      csvContent += '\n--- PRIORITY BREAKDOWN ---\n';
      Object.entries(analytics.tasksByPriority).forEach(([priority, count]) => {
        csvContent += `${priority},${count}\n`;
      });
      
      csvContent += '\n--- CATEGORY BREAKDOWN ---\n';
      Object.entries(analytics.tasksByCategory).forEach(([category, count]) => {
        csvContent += `${category},${count}\n`;
      });
    }
    
    return csvContent;
  }

  /**
   * Export tasks to JSON format with comprehensive data
   */
  static exportToJSON(tasks: Task[], options: ExportOptions = { format: 'json' }, analytics?: AnalyticsData): string {
    const filteredTasks = this.filterTasks(tasks, options);
    
    const exportData = {
      metadata: {
        exportedAt: new Date().toISOString(),
        format: 'TaskManager Mobile Export v2.0',
        timeRange: analytics?.timeRange || 'custom',
        totalTasks: filteredTasks.length,
        filters: {
          dateRange: options.dateRange,
          statusFilter: options.statusFilter,
          priorityFilter: options.priorityFilter,
          categoryFilter: options.categoryFilter,
          includeCompleted: options.includeCompleted,
        }
      },
      analytics: options.includeAnalytics ? analytics : null,
      tasks: filteredTasks.map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        priority: task.priority,
        status: task.status,
        category: task.category,
        assignmentStatus: task.assignment_status,
        assignedTo: task.assigned_to,
        assignmentComments: task.assignment_comments,
        
        // Dates
        date: task.date,
        dueDate: task.dueDate || task.due_date,
        createdAt: task.created_at,
        updatedAt: task.updated_at,
        completedAt: task.completed_at,
        
        // Progress tracking
        completionPercentage: task.completion_percentage,
        estimatedHours: task.estimated_hours,
        actualHours: task.actual_hours,
        
        // Metadata
        tags: task.tags,
        links: task.links,
        location: task.location,
        
        // Media and attachments
        voiceNoteUrl: task.voice_note_url,
        voiceNoteDuration: task.voice_note_duration,
        attachments: task.attachments,
        fileUrls: task.file_urls,
        
        // Comments and collaboration
        comments: task.comments,
        
        // Recurring tasks
        recurringPattern: task.recurring_pattern,
        recurringTaskId: task.recurring_task_id,
        
        // Reminders and settings
        reminderSettings: task.reminder_settings,
        weatherDependent: task.weather_dependent,
      }))
    };
    
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Export tasks to HTML format (for PDF generation)
   */
  static exportToHTML(tasks: Task[]): string {
    const filteredTasks = tasks;
    
    const taskRows = filteredTasks.map(task => `
      <tr>
        <td>${task.title}</td>
        <td>${task.description || 'No description'}</td>
        <td>${task.priority}</td>
        <td>${task.status}</td>
        <td>${task.category || 'General'}</td>
        <td>${task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No due date'}</td>
        <td>${(task.tags || []).join(', ')}</td>
      </tr>
    `).join('');

    return `
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #1f2937; text-align: center; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #d1d5db; padding: 8px; text-align: left; }
            th { background-color: #f3f4f6; font-weight: bold; }
            .summary { margin: 20px 0; }
            .summary-item { margin: 5px 0; }
          </style>
        </head>
        <body>
          <h1>Task Report</h1>
          <div class="summary">
            <div class="summary-item"><strong>Total Tasks:</strong> ${tasks.length}</div>
            <div class="summary-item"><strong>Generated:</strong> ${new Date().toLocaleDateString()}</div>
          </div>
          <table>
            <thead>
              <tr>
                <th>Title</th>
                <th>Description</th>
                <th>Priority</th>
                <th>Status</th>
                <th>Category</th>
                <th>Due Date</th>
                <th>Tags</th>
              </tr>
            </thead>
            <tbody>
              ${taskRows}
            </tbody>
          </table>
        </body>
      </html>
    `;
  }

  /**
   * Generate text summary of tasks
   */
  static generateTaskSummary(tasks: Task[]): string {
    const total = tasks.length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const pending = tasks.filter(t => t.status === 'pending').length;
    const inProgress = tasks.filter(t => t.status === 'in-progress').length;
    
    let summary = `Task Summary (${new Date().toLocaleDateString()})\n\n`;
    summary += `Total Tasks: ${total}\n`;
    summary += `Completed: ${completed}\n`;
    summary += `In Progress: ${inProgress}\n`;
    summary += `Pending: ${pending}\n\n`;
    
    if (tasks.length > 0) {
      summary += "Tasks:\n";
      tasks.forEach((task, index) => {
        summary += `${index + 1}. ${task.title} (${task.priority} priority, ${task.status})\n`;
      });
    }
    
    return summary;
  }

  /**
   * Generate analytics summary
   */
  static generateAnalyticsSummary(
    tasks: Task[],
    analytics: {
      completionRate: number;
      tasksByStatus: Record<string, number>;
      tasksByPriority: Record<string, number>;
      tasksByCategory: Record<string, number>;
    }
  ): string {
    const data = {
      summary: {
        totalTasks: tasks.length,
        completionRate: analytics.completionRate,
        generatedAt: new Date().toISOString(),
      },
      breakdown: {
        byStatus: analytics.tasksByStatus,
        byPriority: analytics.tasksByPriority,
        byCategory: analytics.tasksByCategory,
      },
      tasks: tasks.map(task => ({
        title: task.title,
        description: task.description,
        priority: task.priority,
        status: task.status,
        category: task.category,
        dueDate: task.dueDate,
        tags: task.tags,
      }))
    };
    
    return JSON.stringify(data, null, 2);
  }

  /**
   * Filter tasks based on options
   */
  private static filterTasks(tasks: Task[], options: ExportOptions): Task[] {
    let filtered = [...tasks];

    // Filter by date range
    if (options.dateRange) {
      filtered = filtered.filter(task => {
        if (!task.created_at) return false;
        const taskDate = new Date(task.created_at);
        return taskDate >= options.dateRange!.start && taskDate <= options.dateRange!.end;
      });
    }

    // Filter by status
    if (options.statusFilter && options.statusFilter.length > 0) {
      filtered = filtered.filter(task => 
        options.statusFilter!.includes(task.status || '')
      );
    }

    // Filter by priority
    if (options.priorityFilter && options.priorityFilter.length > 0) {
      filtered = filtered.filter(task => 
        options.priorityFilter!.includes(task.priority || '')
      );
    }

    // Filter by category
    if (options.categoryFilter && options.categoryFilter.length > 0) {
      filtered = filtered.filter(task => 
        options.categoryFilter!.includes(task.category || 'General')
      );
    }

    // Filter out completed tasks if not included
    if (!options.includeCompleted) {
      filtered = filtered.filter(task => task.status !== 'completed');
    }

    return filtered;
  }

  /**
   * Enhanced file sharing with native capabilities
   */
  static async shareContent(content: string, filename: string, format: string = 'txt'): Promise<boolean> {
    try {
      // Create file path in the device's cache directory
      const fileUri = `${FileSystem.cacheDirectory}${filename}`;
      
      // Write content to file
      await FileSystem.writeAsStringAsync(fileUri, content, {
        encoding: FileSystem.EncodingType.UTF8,
      });
      
      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        console.warn('Sharing is not available on this device');
        return false;
      }
      
      // Share the file
      await Sharing.shareAsync(fileUri, {
        mimeType: this.getMIMEType(format),
        dialogTitle: `Share ${filename}`,
        UTI: this.getUTI(format),
      });
      
      // Clean up the temporary file after a delay
      setTimeout(async () => {
        try {
          await FileSystem.deleteAsync(fileUri, { idempotent: true });
        } catch (error) {
          console.warn('Failed to clean up temporary file:', error);
        }
      }, 5000);
      
      return true;
    } catch (error) {
      console.error('Error sharing content:', error);
      return false;
    }
  }

  /**
   * Save content to device storage and return file URI
   */
  static async saveToFile(content: string, filename: string): Promise<string | null> {
    try {
      const fileUri = `${FileSystem.documentDirectory}${filename}`;
      await FileSystem.writeAsStringAsync(fileUri, content, {
        encoding: FileSystem.EncodingType.UTF8,
      });
      return fileUri;
    } catch (error) {
      console.error('Error saving file:', error);
      return null;
    }
  }

  /**
   * Export and share tasks in the specified format
   */
  static async exportAndShare(
    tasks: Task[], 
    options: ExportOptions,
    analytics?: AnalyticsData
  ): Promise<boolean> {
    try {
      let content: string;
      let filename: string;
      const timestamp = new Date().toISOString().split('T')[0];
      
      switch (options.format) {
        case 'csv':
          content = this.exportToCSV(tasks, options, analytics);
          filename = `tasks_export_${timestamp}.csv`;
          break;
        case 'json':
          content = this.exportToJSON(tasks, options, analytics);
          filename = `tasks_export_${timestamp}.json`;
          break;
        case 'pdf':
          content = this.exportToHTML(tasks);
          filename = `tasks_export_${timestamp}.html`;
          break;
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }
      
      return await this.shareContent(content, filename, options.format);
    } catch (error) {
      console.error('Export and share failed:', error);
      return false;
    }
  }

  /**
   * Get export file extension
   */
  static getFileExtension(format: string): string {
    switch (format) {
      case 'csv': return '.csv';
      case 'json': return '.json';
      case 'pdf': return '.pdf';
      default: return '.txt';
    }
  }

  /**
   * Get MIME type for format
   */
  static getMIMEType(format: string): string {
    switch (format) {
      case 'csv': return 'text/csv';
      case 'json': return 'application/json';
      case 'pdf': return 'application/pdf';
      case 'html': return 'text/html';
      default: return 'text/plain';
    }
  }

  /**
   * Get UTI (Uniform Type Identifier) for iOS sharing
   */
  static getUTI(format: string): string {
    switch (format) {
      case 'csv': return 'public.comma-separated-values-text';
      case 'json': return 'public.json';
      case 'pdf': return 'com.adobe.pdf';
      case 'html': return 'public.html';
      default: return 'public.plain-text';
    }
  }

  /**
   * Generate comprehensive analytics data for export
   */
  static generateAnalyticsData(tasks: Task[], timeRange: string = 'all'): AnalyticsData {
    const total = tasks.length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const completionRate = total > 0 ? (completed / total) * 100 : 0;

    // Status breakdown
    const tasksByStatus: Record<string, number> = {
      pending: tasks.filter(t => t.status === 'pending').length,
      'in-progress': tasks.filter(t => t.status === 'in-progress').length,
      completed: completed,
      'on-hold': tasks.filter(t => t.status === 'on-hold').length,
      cancelled: tasks.filter(t => t.status === 'cancelled').length,
    };

    // Priority breakdown
    const tasksByPriority: Record<string, number> = {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length,
    };

    // Category breakdown
    const tasksByCategory: Record<string, number> = {};
    tasks.forEach(task => {
      const category = task.category || 'General';
      tasksByCategory[category] = (tasksByCategory[category] || 0) + 1;
    });

    return {
      completionRate,
      tasksByStatus,
      tasksByPriority,
      tasksByCategory,
      timeRange,
      generatedAt: new Date().toISOString(),
    };
  }
}

export default ExportService;