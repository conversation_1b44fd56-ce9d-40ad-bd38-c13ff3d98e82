import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import VoiceService from '../services/voiceService';

interface VoiceRecorderProps {
  onRecordingComplete: (recording: any, transcription?: string) => void;
  onCancel?: () => void;
}

export default function VoiceRecorder({ onRecordingComplete, onCancel }: VoiceRecorderProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [recording, setRecording] = useState<any>(null);
  const [duration, setDuration] = useState(0);
  const [isVoiceAvailable, setIsVoiceAvailable] = useState(true);

  useEffect(() => {
    checkVoiceAvailability();
  }, []);

  const checkVoiceAvailability = async () => {
    const available = await VoiceService.isVoiceAvailable();
    setIsVoiceAvailable(available);
  };

  const startRecording = async () => {
    try {
      const success = await VoiceService.startRecording();
      if (success) {
        setIsRecording(true);
        setDuration(0);
        
        // Start duration timer
        const interval = setInterval(() => {
          setDuration(prev => prev + 1000);
        }, 1000);

        // Store interval ID for cleanup
        (startRecording as any).intervalId = interval;
      } else {
        Alert.alert('Error', 'Failed to start recording');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    try {
      setIsRecording(false);
      
      // Clear duration timer
      if ((startRecording as any).intervalId) {
        clearInterval((startRecording as any).intervalId);
      }

      const recordingData = await VoiceService.stopRecording();
      if (recordingData) {
        setRecording(recordingData);
        
        // Ask if user wants to transcribe
        Alert.alert(
          'Recording Complete',
          'Would you like to transcribe this recording to text?',
          [
            { text: 'No', onPress: () => onRecordingComplete(recordingData) },
            { text: 'Yes', onPress: () => transcribeRecording(recordingData) },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const transcribeRecording = async (recordingData: any) => {
    setIsTranscribing(true);
    try {
      const transcription = await VoiceService.speechToText();
      onRecordingComplete(recordingData, transcription);
    } catch (error) {
      Alert.alert('Error', 'Failed to transcribe recording');
      onRecordingComplete(recordingData);
    } finally {
      setIsTranscribing(false);
    }
  };

  const cancelRecording = () => {
    if (isRecording) {
      VoiceService.stopRecording();
      setIsRecording(false);
      setDuration(0);
      
      // Clear duration timer
      if ((startRecording as any).intervalId) {
        clearInterval((startRecording as any).intervalId);
      }
    }
    onCancel?.();
  };

  if (!isVoiceAvailable) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Voice features are not available on this device</Text>
      </View>
    );
  }

  if (isTranscribing) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.transcribingText}>Transcribing voice to text...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.recorderContainer}>
        {!recording ? (
          <>
            <TouchableOpacity
              style={[styles.recordButton, isRecording && styles.recordingButton]}
              onPress={isRecording ? stopRecording : startRecording}
            >
              <Ionicons 
                name={isRecording ? "stop" : "mic"} 
                size={32} 
                color="#ffffff" 
              />
            </TouchableOpacity>
            
            <Text style={styles.instructionText}>
              {isRecording ? 'Tap to stop recording' : 'Tap to start recording'}
            </Text>
            
            {isRecording && (
              <Text style={styles.durationText}>
                {VoiceService.formatDuration(duration)}
              </Text>
            )}
          </>
        ) : (
          <View style={styles.recordingActions}>
            <TouchableOpacity
              style={styles.playButton}
              onPress={() => VoiceService.playRecording(recording.uri)}
            >
              <Ionicons name="play" size={24} color="#3b82f6" />
              <Text style={styles.playButtonText}>Play</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.saveButton}
              onPress={() => onRecordingComplete(recording)}
            >
              <Ionicons name="checkmark" size={24} color="#ffffff" />
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setRecording(null);
                setDuration(0);
              }}
            >
              <Ionicons name="close" size={24} color="#ef4444" />
              <Text style={styles.cancelButtonText}>Re-record</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {onCancel && !recording && (
        <TouchableOpacity style={styles.cancelRecordingButton} onPress={cancelRecording}>
          <Text style={styles.cancelRecordingText}>Cancel</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  recorderContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  recordingButton: {
    backgroundColor: '#ef4444',
  },
  instructionText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
  },
  durationText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  recordingActions: {
    flexDirection: 'row',
    gap: 12,
  },
  playButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#3b82f6',
    gap: 4,
  },
  playButtonText: {
    color: '#3b82f6',
    fontSize: 14,
    fontWeight: '600',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#10b981',
    gap: 4,
  },
  saveButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ef4444',
    gap: 4,
  },
  cancelButtonText: {
    color: '#ef4444',
    fontSize: 14,
    fontWeight: '600',
  },
  cancelRecordingButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  cancelRecordingText: {
    color: '#6b7280',
    fontSize: 14,
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
  },
  transcribingText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 12,
  },
});