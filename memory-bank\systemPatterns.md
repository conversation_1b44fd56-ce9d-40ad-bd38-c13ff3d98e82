# System Architecture & Design Patterns

## Architecture Overview

### Microservices Architecture
The application follows a microservices-inspired architecture with clear separation of concerns:

1. **Frontend Web App** (`src/`) - React SPA
2. **Mobile App** (`TaskManagerMobile/`) - React Native with Expo
3. **Serverless API** (`api/`) - Edge functions for backend operations
4. **Database Layer** (`supabase/`) - PostgreSQL with real-time subscriptions

### Clean Architecture Layers

#### Presentation Layer
- **Components**: Reusable UI elements using shadcn/ui + Radix UI
- **Pages**: Route-based components with lazy loading
- **Hooks**: Custom React hooks for business logic abstraction

#### Application Layer
- **Services**: Business logic and API communication
- **Stores**: Zustand stores for state management
- **Repositories**: Data access layer abstraction

#### Domain Layer
- **Types**: TypeScript interfaces and types
- **Validation**: Zod schemas for runtime validation
- **Utilities**: Pure functions and helpers

#### Infrastructure Layer
- **Integrations**: Third-party service connections
- **Storage**: Local storage and caching strategies
- **Network**: API clients and real-time subscriptions

## Design Patterns

### 1. Repository Pattern
```typescript
// src/repositories/TaskRepository.ts
interface TaskRepository {
  findAll(): Promise<Task[]>
  findById(id: string): Promise<Task | null>
  create(task: CreateTaskInput): Promise<Task>
  update(id: string, task: UpdateTaskInput): Promise<Task>
  delete(id: string): Promise<void>
}
```

### 2. Service Layer Pattern
```typescript
// src/services/taskService.ts
class TaskService {
  constructor(
    private taskRepository: TaskRepository,
    private analyticsService: AnalyticsService
  ) {}
  
  async createTask(input: CreateTaskInput): Promise<Task> {
    // Business logic here
    const task = await this.taskRepository.create(input)
    await this.analyticsService.trackTaskCreated(task)
    return task
  }
}
```

### 3. Dependency Injection
```typescript
// src/di/container.ts
import { container } from 'tsyringe'
container.register<TaskRepository>('TaskRepository', {
  useClass: SupabaseTaskRepository
})
```

### 4. Observer Pattern (Real-time)
```typescript
// Real-time subscriptions using Supabase
const subscription = supabase
  .channel('tasks')
  .on('postgres_changes', { event: '*', schema: 'public', table: 'tasks' }, 
    handleTaskChange
  )
```

### 5. Factory Pattern
```typescript
// Component factories for dynamic rendering
const taskCardVariants = {
  compact: CompactTaskCard,
  detailed: DetailedTaskCard,
  minimal: MinimalTaskCard
}
```

## Component Architecture

### Atomic Design Principles
- **Atoms**: Basic UI elements (Button, Input, Icon)
- **Molecules**: Simple component groups (TaskItem, DatePicker)
- **Organisms**: Complex components (TaskCalendar, TaskList)
- **Templates**: Page layouts (DashboardLayout, AuthLayout)
- **Pages**: Complete views (TasksPage, AnalyticsPage)

### Compound Components
Using Radix UI patterns for accessibility:
```typescript
<Dialog>
  <DialogTrigger>Open</DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Title</DialogTitle>
    </DialogHeader>
  </DialogContent>
</Dialog>
```

## State Management Architecture

### Global State (Zustand)
- **taskStore**: Task data and operations
- **subscriptionStore**: User subscription status
- **uiStore**: UI preferences and theme

### Server State (React Query)
- **Query Keys**: Standardized key patterns
- **Optimistic Updates**: Immediate UI feedback
- **Background Refetching**: Stale data refresh

### Local State (React)
- **Component State**: Form inputs, UI toggles
- **URL State**: Filters, pagination, search

## Data Flow Patterns

### Unidirectional Data Flow
```
User Action → Component → Service → API → Database
                    ↓
              State Update → UI Re-render
```

### Event-Driven Architecture
- **User Events**: Clicks, form submissions, navigation
- **System Events**: Real-time updates, offline/online status
- **Custom Events**: Analytics tracking, notifications

## Error Handling Patterns

### Error Boundaries
```typescript
// Global error boundary for graceful degradation
class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    logger.error('Component error:', error, errorInfo)
  }
}
```

### Service-Level Error Handling
```typescript
// Consistent error handling across services
class BaseService {
  protected async handleApiCall<T>(promise: Promise<T>): Promise<T> {
    try {
      return await promise
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
}
```

## Security Patterns

### Authentication Flow
1. **Supabase Auth** → JWT tokens with RLS policies
2. **Row Level Security** → Database-level access control
3. **API Rate Limiting** → Prevent abuse
4. **Input Validation** → Zod schemas on client and server

### Data Protection
- **Encryption at Rest**: Supabase storage encryption
- **Encryption in Transit**: HTTPS/TLS
- **Sensitive Data**: Environment variables, secure storage

## Performance Patterns

### Code Splitting
```typescript
// Lazy loading for heavy components
const AnalyticsDashboard = lazy(() => import('./pages/AnalyticsDashboard'))
```

### Caching Strategy
- **Browser Cache**: Static assets
- **React Query**: API response caching
- **Service Worker**: Offline functionality
- **Redis**: Server-side caching

### Optimistic Updates
```typescript
// Immediate UI feedback
const updateTask = useMutation({
  mutationFn: updateTaskApi,
  onMutate: async (newTask) => {
    // Optimistically update UI
    await queryClient.cancelQueries(['tasks'])
    const previousTasks = queryClient.getQueryData(['tasks'])
    queryClient.setQueryData(['tasks'], (old) => updateTaskInCache(old, newTask))
    return { previousTasks }
  }
})
```

## Testing Patterns

### Testing Pyramid
- **Unit Tests**: Individual functions and components
- **Integration Tests**: Component interactions
- **E2E Tests**: Complete user workflows

### Test Data Management
- **Factories**: Consistent test data generation
- **Fixtures**: Mock API responses
- **MSW**: API mocking for consistent tests

## Deployment Patterns

### CI/CD Pipeline
1. **Code Quality**: Linting, type checking, tests
2. **Build**: Production bundle creation
3. **Deploy**: Vercel/Netlify deployment
4. **Monitor**: Error tracking and analytics

### Environment Management
- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: Optimized build with monitoring