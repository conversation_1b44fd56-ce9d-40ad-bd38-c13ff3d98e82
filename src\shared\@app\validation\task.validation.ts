import { z } from 'zod';

export const TaskPrioritySchema = z.enum(['low', 'medium', 'high']);
export const TaskStatusSchema = z.enum(['pending', 'completed', 'cancelled', 'on-hold']);


export const FileReferenceSchema = z.object({
  key: z.string(),
  name: z.string(),
  type: z.string(),
  size: z.number().positive(),
  uploadedAt: z.string(),
  lastModified: z.number().optional()
});

export const VoiceNoteSchema = z.object({
  url: z.string().url(),
  duration: z.number().positive(),
  created_at: z.string()
});

export const CommentSchema = z.object({
  id: z.string(),
  text: z.string().min(1).max(1000),
  author: z.string(),
  timestamp: z.string()
});

export const RecurringPatternSchema = z.object({
  type: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
  interval: z.number().positive(),
  days_of_week: z.array(z.number().min(0).max(6)).optional(),
  day_of_month: z.number().min(1).max(31).optional(),
  end_date: z.string().optional(),
  max_occurrences: z.number().positive().optional()
});

export const TaskSchema = z.object({
  id: z.string(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  title: z.string().min(1).max(255),
  description: z.string().max(2000),
  priority: TaskPrioritySchema,
  status: TaskStatusSchema.optional(),

  completedAt: z.string().optional(),
  dueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  links: z.array(z.string().url()).optional(),
  attachments: z.array(FileReferenceSchema).optional(),
  voiceNotes: z.array(VoiceNoteSchema).optional(),
  comments: z.array(CommentSchema).optional(),

  recurring_pattern: RecurringPatternSchema.optional(),
  created_at: z.string(),
  user_id: z.string(),
  updated_at: z.string().optional(),
  progress_percentage: z.number().min(0).max(100).optional(),
  estimated_hours: z.number().positive().optional(),
  actual_hours: z.number().positive().optional(),
  location: z.string().optional()
});

export const CreateTaskSchema = TaskSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
  user_id: true
}).extend({
  user_id: z.string().optional()
});

export const UpdateTaskSchema = TaskSchema.partial().omit({
  id: true,
  created_at: true,
  user_id: true
});

export const TaskFilterSchema = z.object({
  status: z.array(TaskStatusSchema).optional(),
  priority: z.array(TaskPrioritySchema).optional(),
  category: z.array(z.string()).optional(),

  date_range: z.object({
    start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    end: z.string().regex(/^\d{4}-\d{2}-\d{2}$/)
  }).optional(),
  search: z.string().optional(),
  tags: z.array(z.string()).optional(),

  overdue_only: z.boolean().optional(),
  has_voice_note: z.boolean().optional()
});

export const TaskSortSchema = z.object({
  field: z.enum(['created_at', 'updated_at', 'due_date', 'priority', 'title', 'completion_percentage']),
  direction: z.enum(['asc', 'desc'])
});

// Validation functions
export function validateTask(data: unknown) {
  return TaskSchema.safeParse(data);
}

export function validateCreateTask(data: unknown) {
  return CreateTaskSchema.safeParse(data);
}

export function validateUpdateTask(data: unknown) {
  return UpdateTaskSchema.safeParse(data);
}

export function validateTaskFilter(data: unknown) {
  return TaskFilterSchema.safeParse(data);
}

export function validateTaskSort(data: unknown) {
  return TaskSortSchema.safeParse(data);
}