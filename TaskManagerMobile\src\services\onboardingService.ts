import AsyncStorage from '@react-native-async-storage/async-storage';
import { Task } from '../types/task';

export interface OnboardingProgress {
  currentStep: 'signup' | 'welcome' | 'tour' | 'complete';
  completedSteps: string[];
  hasCompletedOnboarding: boolean;
  samplesLoaded: boolean;
  tourCompleted: boolean;
  startedAt: string;
  completedAt?: string;
}

export class OnboardingService {
  private static STORAGE_KEY = '@onboarding-progress';
  private static COMPLETED_KEY = '@onboarding-completed';

  /**
   * Check if user has completed onboarding
   */
  static async hasCompletedOnboarding(userId: string): Promise<boolean> {
    try {
      const completed = await AsyncStorage.getItem(`${this.COMPLETED_KEY}-${userId}`);
      return completed === 'true';
    } catch (error) {
      console.error('Error checking onboarding completion:', error);
      return false;
    }
  }

  /**
   * Mark onboarding as completed
   */
  static async markOnboardingComplete(userId: string): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.COMPLETED_KEY}-${userId}`, 'true');
      
      // Update progress
      const progress = await this.getProgress(userId);
      progress.hasCompletedOnboarding = true;
      progress.completedAt = new Date().toISOString();
      progress.currentStep = 'complete';
      await this.saveProgress(userId, progress);
    } catch (error) {
      console.error('Error marking onboarding complete:', error);
    }
  }

  /**
   * Get onboarding progress for user
   */
  static async getProgress(userId: string): Promise<OnboardingProgress> {
    try {
      const stored = await AsyncStorage.getItem(`${this.STORAGE_KEY}-${userId}`);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error getting onboarding progress:', error);
    }

    // Default progress
    return {
      currentStep: 'signup',
      completedSteps: [],
      hasCompletedOnboarding: false,
      samplesLoaded: false,
      tourCompleted: false,
      startedAt: new Date().toISOString(),
    };
  }

  /**
   * Save onboarding progress
   */
  static async saveProgress(userId: string, progress: OnboardingProgress): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.STORAGE_KEY}-${userId}`, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
    }
  }

  /**
   * Update progress step
   */
  static async updateProgress(userId: string, step: OnboardingProgress['currentStep']): Promise<void> {
    const progress = await this.getProgress(userId);
    progress.currentStep = step;
    
    if (!progress.completedSteps.includes(step)) {
      progress.completedSteps.push(step);
    }

    await this.saveProgress(userId, progress);
  }

  /**
   * Generate sample tasks for new users
   */
  static generateSampleTasks(userId: string): Partial<Task>[] {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
    
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    return [
      {
        date: today.toISOString().split('T')[0],
        title: 'Welcome to TaskCalendar Mobile! 🎉',
        description: 'You\'ve successfully created your first task! This is a sample task to help you get started with the mobile app.',
        priority: 'high',
        status: 'pending',
        category: 'Getting Started',
        tags: ['welcome', 'first-task', 'sample'],
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        date: today.toISOString().split('T')[0],
        title: 'Morning Workout 💪',
        description: 'Start your day with a 30-minute workout routine. Exercise is great for productivity!',
        priority: 'medium',
        status: 'pending',
        category: 'Health & Fitness',
        due_date: new Date(today.getTime() + 2 * 60 * 60 * 1000).toISOString(),
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        date: today.toISOString().split('T')[0],
        title: 'Review Project Proposal',
        description: 'Review the Q1 project proposal and prepare feedback for the team meeting.',
        priority: 'high',
        status: 'pending',
        category: 'Work',
        due_date: new Date(today.getTime() + 4 * 60 * 60 * 1000).toISOString(),
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        date: tomorrow.toISOString().split('T')[0],
        title: 'Grocery Shopping 🛒',
        description: 'Buy ingredients for the weekend meal prep. Don\'t forget the fresh vegetables!',
        priority: 'low',
        status: 'pending',
        category: 'Personal',
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        date: tomorrow.toISOString().split('T')[0],
        title: 'Team Stand-up Meeting',
        description: 'Daily team sync to discuss progress and blockers.',
        priority: 'medium',
        status: 'pending',
        category: 'Work',
        due_date: new Date(tomorrow.getTime() + 10 * 60 * 60 * 1000).toISOString(),
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        date: dayAfterTomorrow.toISOString().split('T')[0],
        title: 'Doctor Appointment 🏥',
        description: 'Annual health check-up at 3 PM. Bring insurance card and previous test results.',
        priority: 'high',
        status: 'pending',
        category: 'Health',
        due_date: new Date(dayAfterTomorrow.getTime() + 15 * 60 * 60 * 1000).toISOString(),
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        date: nextWeek.toISOString().split('T')[0],
        title: 'Plan Weekend Trip 🗺️',
        description: 'Research and plan the weekend getaway. Check weather and create itinerary.',
        priority: 'low',
        status: 'pending',
        category: 'Travel',
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
  }

  /**
   * Mark samples as loaded
   */
  static async markSamplesLoaded(userId: string): Promise<void> {
    const progress = await this.getProgress(userId);
    progress.samplesLoaded = true;
    await this.saveProgress(userId, progress);
  }

  /**
   * Mark tour as completed
   */
  static async markTourCompleted(userId: string): Promise<void> {
    const progress = await this.getProgress(userId);
    progress.tourCompleted = true;
    await this.saveProgress(userId, progress);
  }

  /**
   * Reset onboarding (for testing/debugging)
   */
  static async resetOnboarding(userId: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${this.COMPLETED_KEY}-${userId}`);
      await AsyncStorage.removeItem(`${this.STORAGE_KEY}-${userId}`);
    } catch (error) {
      console.error('Error resetting onboarding:', error);
    }
  }

  /**
   * Get onboarding tips based on user progress
   */
  static async getContextualTips(userId: string): Promise<string[]> {
    const progress = await this.getProgress(userId);
    const tips: string[] = [];

    if (!progress.samplesLoaded) {
      tips.push('💡 Try loading sample tasks to see how the app works');
    }

    if (!progress.tourCompleted) {
      tips.push('🎯 Take the interactive tour to learn all the features');
    }

    if (progress.hasCompletedOnboarding) {
      tips.push('📱 Swipe left on tasks to mark them complete');
      tips.push('➕ Use the + button to quickly add new tasks');
      tips.push('📊 Check the analytics tab to track your progress');
    }

    return tips;
  }

  /**
   * Get user's onboarding stats
   */
  static async getStats(userId: string): Promise<{
    daysWithApp: number;
    completedSteps: number;
    totalSteps: number;
    completionRate: number;
  }> {
    const progress = await this.getProgress(userId);
    const startDate = new Date(progress.startedAt);
    const today = new Date();
    const daysWithApp = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const totalSteps = 4; // signup, welcome, tour, complete
    const completedSteps = progress.completedSteps.length;
    const completionRate = Math.round((completedSteps / totalSteps) * 100);

    return {
      daysWithApp,
      completedSteps,
      totalSteps,
      completionRate,
    };
  }
}