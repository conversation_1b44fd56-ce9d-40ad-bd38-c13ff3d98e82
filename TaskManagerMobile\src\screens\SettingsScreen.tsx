import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, ScrollView, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useUnifiedAuth } from '../hooks/useUnifiedAuth';

export default function SettingsScreen() {
  const navigation = useNavigation();
  const { signOut } = useUnifiedAuth();

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Sign Out', 
          style: 'destructive', 
          onPress: async () => {
            try {
              await signOut();
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          }
        },
      ]
    );
  };

  const handleExportData = () => {
    Alert.alert('Export Data', 'Data export functionality will be implemented soon.');
  };

  const handleImportData = () => {
    Alert.alert('Import Data', 'Data import functionality will be implemented soon.');
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'Are you sure you want to clear all your tasks? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', style: 'destructive', onPress: () => {
          // Implement clear data functionality
          Alert.alert('Success', 'All data has been cleared.');
        }},
      ]
    );
  };

  const handleManageSubscription = () => {
    navigation.navigate('Subscription');
  };

  const handleManageProfile = () => {
    navigation.navigate('Profile');
  };

  const SettingItem = ({ 
    title, 
    subtitle, 
    onPress, 
    destructive = false,
    icon
  }: { 
    title: string; 
    subtitle?: string; 
    onPress: () => void; 
    destructive?: boolean; 
    icon?: string;
  }) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, destructive && styles.destructiveText]}>
          {icon} {title}
        </Text>
        {subtitle && (
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        )}
      </View>
      <Text style={styles.settingArrow}>›</Text>
    </TouchableOpacity>
  );

  const SettingSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <SettingSection title="Data Management">
          <SettingItem
            title="Export Data"
            subtitle="Download your tasks as JSON or CSV"
            onPress={handleExportData}
          />
          <SettingItem
            title="Import Data"
            subtitle="Import tasks from a file"
            onPress={handleImportData}
          />
        </SettingSection>

        <SettingSection title="Notifications">
          <SettingItem
            title="Task Reminders"
            subtitle="Get notified about upcoming tasks"
            onPress={() => Alert.alert('Coming Soon', 'Notification settings will be available soon.')}
          />
          <SettingItem
            title="Daily Summary"
            subtitle="Receive daily task summaries"
            onPress={() => Alert.alert('Coming Soon', 'Daily summary settings will be available soon.')}
          />
        </SettingSection>

        <SettingSection title="Appearance">
          <SettingItem
            title="Theme"
            subtitle="Choose your preferred theme"
            onPress={() => Alert.alert('Coming Soon', 'Theme settings will be available soon.')}
          />
          <SettingItem
            title="Calendar View"
            subtitle="Customize calendar appearance"
            onPress={() => Alert.alert('Coming Soon', 'Calendar customization will be available soon.')}
          />
        </SettingSection>

        <SettingSection title="Account">
          <SettingItem
            title="Profile"
            subtitle="Manage your account information"
            onPress={() => Alert.alert('Coming Soon', 'Profile management will be available soon.')}
          />
          <SettingItem
            title="Sync Settings"
            subtitle="Configure data synchronization"
            onPress={() => Alert.alert('Coming Soon', 'Sync settings will be available soon.')}
          />
          <SettingItem
            title="Sign Out"
            subtitle="Sign out of your account"
            onPress={handleLogout}
            destructive
          />
        </SettingSection>

        <SettingSection title="Support">
          <SettingItem
            title="Help & FAQ"
            subtitle="Get help and find answers"
            onPress={() => Alert.alert('Coming Soon', 'Help section will be available soon.')}
          />
          <SettingItem
            title="Contact Support"
            subtitle="Get in touch with our team"
            onPress={() => Alert.alert('Coming Soon', 'Contact support will be available soon.')}
          />
          <SettingItem
            title="About"
            subtitle="App version and information"
            onPress={() => Alert.alert('Task Manager', 'Version 1.0.0\n\nA professional task management app with calendar integration.')}
          />
        </SettingSection>

        <SettingSection title="Danger Zone">
          <SettingItem
            title="Clear All Data"
            subtitle="Permanently delete all your tasks"
            onPress={handleClearData}
            destructive
          />
        </SettingSection>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Task Manager v1.0.0</Text>
          <Text style={styles.footerSubtext}>Made with ❤️ for productivity</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  sectionContent: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e5e7eb',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  settingArrow: {
    fontSize: 20,
    color: '#9ca3af',
    marginLeft: 8,
  },
  destructiveText: {
    color: '#ef4444',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#9ca3af',
  },
});