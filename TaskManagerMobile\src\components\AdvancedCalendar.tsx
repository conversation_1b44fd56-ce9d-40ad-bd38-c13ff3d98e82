import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isSameMonth, addMonths, subMonths, getDay } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { useTaskStore } from '../stores/taskStore';
import { Task } from '../types/task';

interface AdvancedCalendarProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 = Sunday, 1 = Monday
}

const { width: screenWidth } = Dimensions.get('window');

export default function AdvancedCalendar({ 
  selectedDate, 
  onDateSelect, 
  weekStartsOn = 1 // Default to Monday
}: AdvancedCalendarProps) {
  const { tasks } = useTaskStore();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [tasksByDate, setTasksByDate] = useState<Record<string, Task[]>>({});

  useEffect(() => {
    // Group tasks by date
    const grouped = tasks.reduce((acc, task) => {
      const date = task.date.split('T')[0];
      if (!acc[date]) acc[date] = [];
      acc[date].push(task);
      return acc;
    }, {} as Record<string, Task[]>);
    setTasksByDate(grouped);
  }, [tasks]);

  const getDaysInMonth = (date: Date) => {
    const start = startOfWeek(startOfMonth(date), { weekStartsOn });
    const end = endOfWeek(endOfMonth(date), { weekStartsOn });
    const days = [];
    let current = start;

    while (current <= end) {
      days.push(current);
      current = addDays(current, 1);
    }

    return days;
  };

  const getTaskIndicators = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    const dayTasks = tasksByDate[dateStr] || [];
    
    const indicators = {
      hasHighPriority: dayTasks.some(t => t.priority === 'high'),
      hasMediumPriority: dayTasks.some(t => t.priority === 'medium'),
      hasLowPriority: dayTasks.some(t => t.priority === 'low'),
      hasCompleted: dayTasks.some(t => t.status === 'completed'),
      hasPending: dayTasks.some(t => t.status === 'pending'),
      hasInProgress: dayTasks.some(t => t.status === 'in-progress'),
      taskCount: dayTasks.length,
    };

    return indicators;
  };

  const renderCalendarHeader = () => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    return (
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => setCurrentMonth(subMonths(currentMonth, 1))}
        >
          <Ionicons name="chevron-back" size={24} color="#3b82f6" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.monthButton}
          onPress={() => setCurrentMonth(new Date())}
        >
          <Text style={styles.monthText}>
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => setCurrentMonth(addMonths(currentMonth, 1))}
        >
          <Ionicons name="chevron-forward" size={24} color="#3b82f6" />
        </TouchableOpacity>
      </View>
    );
  };

  const renderWeekDays = () => {
    const weekDays = weekStartsOn === 1 
      ? ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return (
      <View style={styles.weekDays}>
        {weekDays.map(day => (
          <Text key={day} style={styles.weekDay}>
            {day}
          </Text>
        ))}
      </View>
    );
  };

  const renderDay = (day: Date) => {
    const indicators = getTaskIndicators(day);
    const isSelected = isSameDay(day, selectedDate);
    const isCurrentMonth = isSameMonth(day, currentMonth);
    const isToday = isSameDay(day, new Date());

    return (
      <TouchableOpacity
        key={day.toString()}
        style={[
          styles.day,
          !isCurrentMonth && styles.otherMonthDay,
          isSelected && styles.selectedDay,
          isToday && styles.today,
        ]}
        onPress={() => onDateSelect(day)}
      >
        <Text style={[
          styles.dayText,
          !isCurrentMonth && styles.otherMonthText,
          isSelected && styles.selectedDayText,
          isToday && styles.todayText,
        ]}>
          {format(day, 'd')}
        </Text>

        {/* Task indicators */}
        <View style={styles.indicators}>
          {indicators.taskCount > 0 && (
            <View style={styles.taskCount}>
              <Text style={styles.taskCountText}>
                {indicators.taskCount > 9 ? '9+' : indicators.taskCount}
              </Text>
            </View>
          )}

          <View style={styles.priorityIndicators}>
            {indicators.hasHighPriority && (
              <View style={[styles.priorityDot, { backgroundColor: '#ef4444' }]} />
            )}
            {indicators.hasMediumPriority && (
              <View style={[styles.priorityDot, { backgroundColor: '#f59e0b' }]} />
            )}
            {indicators.hasLowPriority && (
              <View style={[styles.priorityDot, { backgroundColor: '#10b981' }]} />
            )}
          </View>

          <View style={styles.statusIndicators}>
            {indicators.hasCompleted && (
              <View style={[styles.statusDot, { backgroundColor: '#10b981' }]} />
            )}
            {indicators.hasInProgress && (
              <View style={[styles.statusDot, { backgroundColor: '#3b82f6' }]} />
            )}
            {indicators.hasPending && (
              <View style={[styles.statusDot, { backgroundColor: '#6b7280' }]} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMonthStats = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const monthTasks = daysInMonth.reduce((acc, day) => {
      const dateStr = format(day, 'yyyy-MM-dd');
      const dayTasks = tasksByDate[dateStr] || [];
      return acc.concat(dayTasks);
    }, [] as Task[]);

    const totalTasks = monthTasks.length;
    const completedTasks = monthTasks.filter(t => t.status === 'completed').length;
    const highPriorityTasks = monthTasks.filter(t => t.priority === 'high').length;

    return (
      <View style={styles.statsContainer}>
        <View style={styles.stat}>
          <Text style={styles.statValue}>{totalTasks}</Text>
          <Text style={styles.statLabel}>Total</Text>
        </View>
        <View style={styles.stat}>
          <Text style={styles.statValue}>{completedTasks}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>
        <View style={styles.stat}>
          <Text style={styles.statValue}>{highPriorityTasks}</Text>
          <Text style={styles.statLabel}>High Priority</Text>
        </View>
      </View>
    );
  };

  const days = getDaysInMonth(currentMonth);

  return (
    <View style={styles.container}>
      {renderCalendarHeader()}
      {renderMonthStats()}
      {renderWeekDays()}
      
      <View style={styles.calendar}>
        {days.map(day => renderDay(day))}
      </View>

      {/* Legend */}
      <View style={styles.legend}>
        <Text style={styles.legendTitle}>Legend</Text>
        <View style={styles.legendItems}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#ef4444' }]} />
            <Text style={styles.legendText}>High Priority</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#f59e0b' }]} />
            <Text style={styles.legendText}>Medium Priority</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#10b981' }]} />
            <Text style={styles.legendText}>Low Priority</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#10b981' }]} />
            <Text style={styles.legendText}>Completed</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#3b82f6' }]} />
            <Text style={styles.legendText}>In Progress</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  navButton: {
    padding: 8,
  },
  monthButton: {
    flex: 1,
    alignItems: 'center',
  },
  monthText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
    paddingVertical: 8,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
  },
  stat: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  weekDays: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  weekDay: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '600',
    color: '#6b7280',
  },
  calendar: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  day: {
    width: `${100 / 7}%`,
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
    borderRadius: 8,
    margin: 1,
  },
  otherMonthDay: {
    opacity: 0.3,
  },
  selectedDay: {
    backgroundColor: '#3b82f6',
  },
  today: {
    borderWidth: 2,
    borderColor: '#3b82f6',
  },
  dayText: {
    fontSize: 14,
    color: '#1f2937',
  },
  otherMonthText: {
    color: '#9ca3af',
  },
  selectedDayText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  todayText: {
    fontWeight: 'bold',
  },
  indicators: {
    position: 'absolute',
    bottom: 2,
    left: 2,
    right: 2,
    alignItems: 'center',
  },
  taskCount: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingHorizontal: 4,
    paddingVertical: 1,
    marginBottom: 2,
  },
  taskCountText: {
    color: '#ffffff',
    fontSize: 8,
    fontWeight: 'bold',
  },
  priorityIndicators: {
    flexDirection: 'row',
    gap: 1,
    marginBottom: 1,
  },
  statusIndicators: {
    flexDirection: 'row',
    gap: 1,
  },
  priorityDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  statusDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  legend: {
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  legendTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  legendItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  legendText: {
    fontSize: 12,
    color: '#6b7280',
  },
});