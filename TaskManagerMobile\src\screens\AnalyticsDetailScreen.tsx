import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTaskStore } from '../stores/taskStore';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { Task } from '../types/task';

type RootStackParamList = {
  AnalyticsDetail: { filterType: string; title: string };
  TaskDetails: { taskId: string };
};

type AnalyticsDetailScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function AnalyticsDetailScreen() {
  const route = useRoute();
  const navigation = useNavigation<AnalyticsDetailScreenNavigationProp>();
  const { tasks, loadTasks } = useTaskStore();
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);

  const { filterType, title } = route.params as { filterType: string; title: string };

  useEffect(() => {
    loadTasks();
  }, []);

  useEffect(() => {
    let filtered = [...tasks];

    switch (filterType) {
      case 'all':
        filtered = tasks;
        break;
      case 'completed':
        filtered = tasks.filter(t => t.status === 'completed');
        break;
      case 'in-progress':
        filtered = tasks.filter(t => t.status === 'in-progress');
        break;
      case 'pending':
        filtered = tasks.filter(t => t.status === 'pending');
        break;
      case 'on-hold':
        filtered = tasks.filter(t => t.status === 'on-hold');
        break;
      case 'cancelled':
        filtered = tasks.filter(t => t.status === 'cancelled');
        break;
      case 'high-priority':
        filtered = tasks.filter(t => t.priority === 'high');
        break;
      case 'assigned':
        filtered = tasks.filter(t => t.assignment_status === 'assigned');
        break;
      case 'accepted':
        filtered = tasks.filter(t => t.assignment_status === 'accepted');
        break;
      case 'declined':
        filtered = tasks.filter(t => t.assignment_status === 'declined');
        break;
      case 'overdue':
        filtered = tasks.filter(t => 
          t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'completed'
        );
        break;
      case 'category':
        const categoryName = title.replace(' Tasks', '');
        filtered = tasks.filter(t => t.category === categoryName);
        break;
      default:
        filtered = tasks;
    }

    setFilteredTasks(filtered);
  }, [tasks, filterType, title]);

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return '#10b981';
      case 'in-progress': return '#3b82f6';
      case 'pending': return '#6b7280';
      case 'on-hold': return '#8b5cf6';
      case 'cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const renderTaskItem = ({ item }: { item: Task }) => (
    <TouchableOpacity
      style={styles.taskItem}
      onPress={() => navigation.navigate('TaskDetails', { taskId: item.id })}
    >
      <View style={styles.taskHeader}>
        <Text style={styles.taskTitle} numberOfLines={1}>{item.title}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      
      <Text style={styles.taskDescription} numberOfLines={2}>
        {item.description || 'No description provided'}
      </Text>

      <View style={styles.taskDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="calendar-outline" size={14} color="#6b7280" />
          <Text style={styles.detailText}>
            {format(new Date(item.date), 'MMM dd, yyyy')}
          </Text>
        </View>

        {item.dueDate && (
          <View style={styles.detailRow}>
            <Ionicons name="time-outline" size={14} color="#6b7280" />
            <Text style={styles.detailText}>
              Due: {format(new Date(item.dueDate), 'MMM dd, yyyy')}
            </Text>
          </View>
        )}

        <View style={styles.detailRow}>
          <Ionicons name="flag-outline" size={14} color={getPriorityColor(item.priority)} />
          <Text style={[styles.detailText, { color: getPriorityColor(item.priority) }]}>
            {item.priority} priority
          </Text>
        </View>

        {item.category && (
          <View style={styles.detailRow}>
            <Ionicons name="folder-outline" size={14} color="#6b7280" />
            <Text style={styles.detailText}>{item.category}</Text>
          </View>
        )}

        {item.assignment_status && (
          <View style={styles.detailRow}>
            <Ionicons name="people-outline" size={14} color="#3b82f6" />
            <Text style={styles.detailText}>
              Assigned: {item.assignment_status}
            </Text>
          </View>
        )}
      </View>

      {item.tags && item.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {item.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>
      )}
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-text-outline" size={48} color="#9ca3af" />
      <Text style={styles.emptyStateTitle}>No tasks found</Text>
      <Text style={styles.emptyStateText}>
        There are no tasks matching the "{title}" filter.
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1f2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{title}</Text>
        <View style={styles.headerRight}>
          <Text style={styles.taskCount}>{filteredTasks.length} tasks</Text>
        </View>
      </View>

      <FlatList
        data={filteredTasks}
        renderItem={renderTaskItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.contentContainer}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  headerRight: {
    minWidth: 60,
    alignItems: 'flex-end',
  },
  taskCount: {
    fontSize: 14,
    color: '#6b7280',
  },
  contentContainer: {
    padding: 16,
  },
  taskItem: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '500',
  },
  taskDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  taskDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontSize: 12,
    color: '#6b7280',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    gap: 6,
  },
  tag: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 12,
    color: '#6b7280',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    maxWidth: 280,
  },
});